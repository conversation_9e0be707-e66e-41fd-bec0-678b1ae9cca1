<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "0930"
   wasCreatedForAppExtension = "YES"
   version = "2.0">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "67E91EF41D769E1E0039F1C4"
               BuildableName = "MessagesExtension.appex"
               BlueprintName = "MessagesExtension"
               ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "67E91EEA1D769E1E0039F1C4"
               BuildableName = "Branch-TestBed-iMessage.app"
               BlueprintName = "Branch-TestBed-iMessage"
               ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "67E91EF41D769E1E0039F1C4"
            BuildableName = "MessagesExtension.appex"
            BlueprintName = "MessagesExtension"
            ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES"
      launchAutomaticallySubstyle = "2">
      <RemoteRunnable
         runnableDebuggingMode = "1"
         BundleIdentifier = "com.apple.MobileSMS">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "67E91EF41D769E1E0039F1C4"
            BuildableName = "MessagesExtension.appex"
            BlueprintName = "MessagesExtension"
            ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
         </BuildableReference>
      </RemoteRunnable>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "67E91EF41D769E1E0039F1C4"
            BuildableName = "MessagesExtension.appex"
            BlueprintName = "MessagesExtension"
            ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "67E91EEA1D769E1E0039F1C4"
            BuildableName = "Branch-TestBed-iMessage.app"
            BlueprintName = "Branch-TestBed-iMessage"
            ReferencedContainer = "container:Branch-TestBed-iMessage.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
