// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		4DB3280E1FA110B400ACF9B0 /* Branch.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DB3280D1FA1109000ACF9B0 /* Branch.framework */; };
		67E91EEF1D769E1E0039F1C4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 67E91EEE1D769E1E0039F1C4 /* Assets.xcassets */; };
		67E91EF61D769E1E0039F1C4 /* MessagesExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 67E91EF51D769E1E0039F1C4 /* MessagesExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		67E91EFB1D769E1E0039F1C4 /* Messages.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 67E91EFA1D769E1E0039F1C4 /* Messages.framework */; };
		67E91EFF1D769E1E0039F1C4 /* MessagesViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 67E91EFE1D769E1E0039F1C4 /* MessagesViewController.m */; };
		67E91F021D769E1E0039F1C4 /* MainInterface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 67E91F001D769E1E0039F1C4 /* MainInterface.storyboard */; };
		67E91F041D769E1E0039F1C4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 67E91F031D769E1E0039F1C4 /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		4DB3280C1FA1109000ACF9B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4DB328081FA1109000ACF9B0 /* BranchSDK.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = E298D0521C73D1B800589D22;
			remoteInfo = Branch;
		};
		4DB3280F1FA110C100ACF9B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4DB328081FA1109000ACF9B0 /* BranchSDK.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = E298D0511C73D1B800589D22;
			remoteInfo = Branch;
		};
		67E91EF71D769E1E0039F1C4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 67E91EE51D769E1E0039F1C4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 67E91EF41D769E1E0039F1C4;
			remoteInfo = MessagesExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		67E91F0B1D769E1E0039F1C4 /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				67E91EF61D769E1E0039F1C4 /* MessagesExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		4DB328081FA1109000ACF9B0 /* BranchSDK.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = BranchSDK.xcodeproj; path = "../carthage-files/BranchSDK.xcodeproj"; sourceTree = "<group>"; };
		67E91EEB1D769E1E0039F1C4 /* Branch-TestBed-iMessage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Branch-TestBed-iMessage.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		67E91EEE1D769E1E0039F1C4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		67E91EF01D769E1E0039F1C4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		67E91EF51D769E1E0039F1C4 /* MessagesExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = MessagesExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		67E91EFA1D769E1E0039F1C4 /* Messages.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Messages.framework; path = System/Library/Frameworks/Messages.framework; sourceTree = SDKROOT; };
		67E91EFD1D769E1E0039F1C4 /* MessagesViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessagesViewController.h; sourceTree = "<group>"; };
		67E91EFE1D769E1E0039F1C4 /* MessagesViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessagesViewController.m; sourceTree = "<group>"; };
		67E91F011D769E1E0039F1C4 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/MainInterface.storyboard; sourceTree = "<group>"; };
		67E91F031D769E1E0039F1C4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		67E91F051D769E1E0039F1C4 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		67E91EF21D769E1E0039F1C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				67E91EFB1D769E1E0039F1C4 /* Messages.framework in Frameworks */,
				4DB3280E1FA110B400ACF9B0 /* Branch.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4DB328091FA1109000ACF9B0 /* Products */ = {
			isa = PBXGroup;
			children = (
				4DB3280D1FA1109000ACF9B0 /* Branch.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		67E91EE41D769E1E0039F1C4 = {
			isa = PBXGroup;
			children = (
				4DB328081FA1109000ACF9B0 /* BranchSDK.xcodeproj */,
				67E91EED1D769E1E0039F1C4 /* Branch-TestBed-iMessage */,
				67E91EFC1D769E1E0039F1C4 /* MessagesExtension */,
				67E91EF91D769E1E0039F1C4 /* Frameworks */,
				67E91EEC1D769E1E0039F1C4 /* Products */,
			);
			sourceTree = "<group>";
		};
		67E91EEC1D769E1E0039F1C4 /* Products */ = {
			isa = PBXGroup;
			children = (
				67E91EEB1D769E1E0039F1C4 /* Branch-TestBed-iMessage.app */,
				67E91EF51D769E1E0039F1C4 /* MessagesExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		67E91EED1D769E1E0039F1C4 /* Branch-TestBed-iMessage */ = {
			isa = PBXGroup;
			children = (
				67E91EEE1D769E1E0039F1C4 /* Assets.xcassets */,
				67E91EF01D769E1E0039F1C4 /* Info.plist */,
			);
			path = "Branch-TestBed-iMessage";
			sourceTree = "<group>";
		};
		67E91EF91D769E1E0039F1C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				67E91EFA1D769E1E0039F1C4 /* Messages.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		67E91EFC1D769E1E0039F1C4 /* MessagesExtension */ = {
			isa = PBXGroup;
			children = (
				67E91EFD1D769E1E0039F1C4 /* MessagesViewController.h */,
				67E91EFE1D769E1E0039F1C4 /* MessagesViewController.m */,
				67E91F001D769E1E0039F1C4 /* MainInterface.storyboard */,
				67E91F031D769E1E0039F1C4 /* Assets.xcassets */,
				67E91F051D769E1E0039F1C4 /* Info.plist */,
			);
			path = MessagesExtension;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		67E91EEA1D769E1E0039F1C4 /* Branch-TestBed-iMessage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 67E91F0C1D769E1E0039F1C4 /* Build configuration list for PBXNativeTarget "Branch-TestBed-iMessage" */;
			buildPhases = (
				67E91EE91D769E1E0039F1C4 /* Resources */,
				67E91F0B1D769E1E0039F1C4 /* Embed App Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				67E91EF81D769E1E0039F1C4 /* PBXTargetDependency */,
			);
			name = "Branch-TestBed-iMessage";
			productName = "Branch-TestBed-iMessage";
			productReference = 67E91EEB1D769E1E0039F1C4 /* Branch-TestBed-iMessage.app */;
			productType = "com.apple.product-type.application.messages";
		};
		67E91EF41D769E1E0039F1C4 /* MessagesExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 67E91F081D769E1E0039F1C4 /* Build configuration list for PBXNativeTarget "MessagesExtension" */;
			buildPhases = (
				67E91EF11D769E1E0039F1C4 /* Sources */,
				67E91EF21D769E1E0039F1C4 /* Frameworks */,
				67E91EF31D769E1E0039F1C4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4DB328101FA110C100ACF9B0 /* PBXTargetDependency */,
			);
			name = MessagesExtension;
			productName = MessagesExtension;
			productReference = 67E91EF51D769E1E0039F1C4 /* MessagesExtension.appex */;
			productType = "com.apple.product-type.app-extension.messages";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		67E91EE51D769E1E0039F1C4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0930;
				ORGANIZATIONNAME = "Branch Metrics";
				TargetAttributes = {
					67E91EEA1D769E1E0039F1C4 = {
						CreatedOnToolsVersion = 8.0;
						DevelopmentTeam = R63EM248DP;
						ProvisioningStyle = Automatic;
					};
					67E91EF41D769E1E0039F1C4 = {
						CreatedOnToolsVersion = 8.0;
						DevelopmentTeam = R63EM248DP;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 67E91EE81D769E1E0039F1C4 /* Build configuration list for PBXProject "Branch-TestBed-iMessage" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 67E91EE41D769E1E0039F1C4;
			productRefGroup = 67E91EEC1D769E1E0039F1C4 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 4DB328091FA1109000ACF9B0 /* Products */;
					ProjectRef = 4DB328081FA1109000ACF9B0 /* BranchSDK.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				67E91EEA1D769E1E0039F1C4 /* Branch-TestBed-iMessage */,
				67E91EF41D769E1E0039F1C4 /* MessagesExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		4DB3280D1FA1109000ACF9B0 /* Branch.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Branch.framework;
			remoteRef = 4DB3280C1FA1109000ACF9B0 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		67E91EE91D769E1E0039F1C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				67E91EEF1D769E1E0039F1C4 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		67E91EF31D769E1E0039F1C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				67E91F041D769E1E0039F1C4 /* Assets.xcassets in Resources */,
				67E91F021D769E1E0039F1C4 /* MainInterface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		67E91EF11D769E1E0039F1C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				67E91EFF1D769E1E0039F1C4 /* MessagesViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4DB328101FA110C100ACF9B0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Branch;
			targetProxy = 4DB3280F1FA110C100ACF9B0 /* PBXContainerItemProxy */;
		};
		67E91EF81D769E1E0039F1C4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 67E91EF41D769E1E0039F1C4 /* MessagesExtension */;
			targetProxy = 67E91EF71D769E1E0039F1C4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		67E91F001D769E1E0039F1C4 /* MainInterface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				67E91F011D769E1E0039F1C4 /* Base */,
			);
			name = MainInterface.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		67E91F061D769E1E0039F1C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		67E91F071D769E1E0039F1C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		67E91F091D769E1E0039F1C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "iMessage App Icon";
				DEVELOPMENT_TEAM = R63EM248DP;
				INFOPLIST_FILE = MessagesExtension/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "io.branch.Branch-TestBed-iMessage.MessagesExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		67E91F0A1D769E1E0039F1C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "iMessage App Icon";
				DEVELOPMENT_TEAM = R63EM248DP;
				INFOPLIST_FILE = MessagesExtension/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "io.branch.Branch-TestBed-iMessage.MessagesExtension";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		67E91F0D1D769E1E0039F1C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = R63EM248DP;
				INFOPLIST_FILE = "Branch-TestBed-iMessage/Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "io.branch.Branch-TestBed-iMessage";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		67E91F0E1D769E1E0039F1C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				DEVELOPMENT_TEAM = R63EM248DP;
				INFOPLIST_FILE = "Branch-TestBed-iMessage/Info.plist";
				PRODUCT_BUNDLE_IDENTIFIER = "io.branch.Branch-TestBed-iMessage";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		67E91EE81D769E1E0039F1C4 /* Build configuration list for PBXProject "Branch-TestBed-iMessage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				67E91F061D769E1E0039F1C4 /* Debug */,
				67E91F071D769E1E0039F1C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		67E91F081D769E1E0039F1C4 /* Build configuration list for PBXNativeTarget "MessagesExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				67E91F091D769E1E0039F1C4 /* Debug */,
				67E91F0A1D769E1E0039F1C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		67E91F0C1D769E1E0039F1C4 /* Build configuration list for PBXNativeTarget "Branch-TestBed-iMessage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				67E91F0D1D769E1E0039F1C4 /* Debug */,
				67E91F0E1D769E1E0039F1C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 67E91EE51D769E1E0039F1C4 /* Project object */;
}
