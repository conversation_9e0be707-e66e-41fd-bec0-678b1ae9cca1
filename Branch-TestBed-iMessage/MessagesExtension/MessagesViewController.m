//
//  MessagesViewController.m
//  MessagesExtension
//
//  Created by <PERSON> on 8/30/16.
//  Copyright © 2016 Branch Metrics. All rights reserved.
//

#import "MessagesViewController.h"
@import Branch;

@interface MessagesViewController ()
@property (weak, nonatomic) IBOutlet UILabel *txtStatus;
@property (strong, nonatomic) BranchUniversalObject *branchUniversalObject;
@end

@implementation MessagesViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.branchUniversalObject = [[BranchUniversalObject alloc] initWithCanonicalIdentifier:@"item/12345"];
    self.branchUniversalObject.title = @"Branch is awesome!";
    self.branchUniversalObject.contentDescription =
        @"Branch is the best possible developer tool to help drive awareness and growth for my apps.";
    self.branchUniversalObject.imageUrl = @"https://pbs.twimg.com/profile_images/658759610220703744/IO1HUADP.png";
    self.branchUniversalObject.contentMetadata.customMetadata[@"property1"] = @"blue";
    self.branchUniversalObject.contentMetadata.customMetadata[@"property2"] = @"red";
}

- (IBAction)cmdCreateLink:(id)sender {
    BranchLinkProperties *linkProperties = [[BranchLinkProperties alloc] init];
    linkProperties.feature = @"sharing";
    linkProperties.channel = @"imessage";
    [linkProperties addControlParam:@"$desktop_url" withValue:@"https://branch.io"];
    [linkProperties addControlParam:@"$ios_url" withValue:@"https://itunes.apple.com/us/app/classic-mac/id1127542169?app=messages"];
    
    [self.branchUniversalObject getShortUrlWithLinkProperties:linkProperties andCallback:^(NSString * _Nonnull url, NSError * _Nullable error) {
        if (!error) {
            NSLog(@"got myself a cool link here: %@", url);
            [self.txtStatus setText:url];
        }
    }];
}

#pragma mark - Conversation Handling

-(void)didBecomeActiveWithConversation:(MSConversation *)conversation {
    // Called when the extension is about to move from the inactive to active state.
    // This will happen when the extension is about to present UI.
    
    NSLog(@"didBecomeActiveWithConversation: %@", [conversation description]);
    Branch *branch = [Branch getInstance];
    //[branch setDebug];    //  eDebug
    [branch initSessionWithLaunchOptions:@{} andRegisterDeepLinkHandler:^(NSDictionary * _Nonnull params, NSError * _Nullable error) {
        if (!error) {
            NSLog(@"found params %@", [params description]);
            [self.txtStatus setText:[NSString stringWithFormat:@"referred: %@", [params objectForKey:@"+clicked_branch_link"]]];
        }
    }];
    
    // Use this method to configure the extension and restore previously stored state.
}

-(void)willResignActiveWithConversation:(MSConversation *)conversation {
    // Called when the extension is about to move from the active to inactive state.
    // This will happen when the user dissmises the extension, changes to a different
    // conversation or quits Messages.
    
    NSLog(@"willResignActiveWithConversation: %@", [conversation description]);
    
    // Use this method to release shared resources, save user data, invalidate timers,
    // and store enough state information to restore your extension to its current state
    // in case it is terminated later.
}

-(void)didReceiveMessage:(MSMessage *)message conversation:(MSConversation *)conversation {
    // Called when a message arrives that was generated by another instance of this
    // extension on a remote device.
    
    NSLog(@"didReceiveMessage: %@ conversation: %@", [message description], [conversation description]);
    
    // Use this method to trigger UI updates in response to the message.
}

-(void)didStartSendingMessage:(MSMessage *)message conversation:(MSConversation *)conversation {
    // Called when the user taps the send button.
}

-(void)didCancelSendingMessage:(MSMessage *)message conversation:(MSConversation *)conversation {
    // Called when the user deletes the message without sending it.
    
    // Use this to clean up state related to the deleted message.
}

-(void)willTransitionToPresentationStyle:(MSMessagesAppPresentationStyle)presentationStyle {
    // Called before the extension transitions to a new presentation style.
    
    // Use this method to prepare for the change in presentation style.
}

-(void)didTransitionToPresentationStyle:(MSMessagesAppPresentationStyle)presentationStyle {
    // Called after the extension transitions to a new presentation style.
    
    // Use this method to finalize any behaviors associated with the change in presentation style.
}

@end
