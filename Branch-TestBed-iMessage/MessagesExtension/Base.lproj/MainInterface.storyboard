<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="11198.2" systemVersion="15G31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="ObA-dk-sSI">
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="11161"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Messages View Controller-->
        <scene sceneID="7MM-of-jgj">
            <objects>
                <viewController id="ObA-dk-sSI" customClass="MessagesViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="qkL-Od-lgU"/>
                        <viewControllerLayoutGuide type="bottom" id="n38-gi-rB5"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="zMn-AG-sqS">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="528"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NiH-Ml-F0d">
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="DJk-bk-5Lb"/>
                                    <constraint firstAttribute="width" constant="88" id="lvZ-HO-oj0"/>
                                </constraints>
                                <state key="normal" title="Create Link"/>
                                <connections>
                                    <action selector="cmdCreateLink:" destination="ObA-dk-sSI" eventType="touchUpInside" id="g05-pM-jhR"/>
                                </connections>
                            </button>
                            <label opaque="NO" clipsSubviews="YES" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="status text" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="d1e-fi-ked">
                                <constraints>
                                    <constraint firstAttribute="width" constant="288" id="MVs-xP-EkL"/>
                                    <constraint firstAttribute="height" constant="21" id="tXC-Xb-bzv"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="NiH-Ml-F0d" firstAttribute="centerX" secondItem="zMn-AG-sqS" secondAttribute="centerX" id="UbE-WA-YiA"/>
                            <constraint firstItem="NiH-Ml-F0d" firstAttribute="top" secondItem="d1e-fi-ked" secondAttribute="bottom" constant="7" id="Vms-om-fi4"/>
                            <constraint firstItem="d1e-fi-ked" firstAttribute="centerY" secondItem="zMn-AG-sqS" secondAttribute="centerY" id="aRA-Jp-4gN"/>
                            <constraint firstItem="d1e-fi-ked" firstAttribute="centerX" secondItem="zMn-AG-sqS" secondAttribute="centerX" id="nnv-AU-qfX"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="320" height="528"/>
                    <connections>
                        <outlet property="txtStatus" destination="d1e-fi-ked" id="Fzs-Vj-nK4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="X47-rx-isc" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.59999999999999" y="94.452773613193415"/>
        </scene>
    </scenes>
</document>
