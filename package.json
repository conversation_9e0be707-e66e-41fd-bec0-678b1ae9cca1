{"name": "neonix-wallet", "version": "7.57.0", "private": true, "scripts": {"install:foundryup": "yarn mm-foundryup", "anvil": "node_modules/.bin/anvil", "audit:ci": "./scripts/yarn-audit.sh", "watch": "./scripts/build.sh watcher watch", "watch:clean": "./scripts/build.sh watcher clean", "clean:ios": "rm -rf ios/build", "pod:install": "bundle exec pod install --project-directory=ios", "gem:bundle:install": "bundle install --gemfile=ios/Gemfile", "clean:ppom": "rm -rf ppom/dist ppom/node_modules app/lib/ppom/ppom.html.js", "clean:android": "rm -rf android/app/build", "clean:node": "rm -rf node_modules && yarn --frozen-lockfile", "clean": "yarn clean:ios && yarn clean:android && yarn clean:node && yarn clean:ppom", "clean-android": "yarn clean:android && yarn clean:node", "lint": "eslint '**/*.{js,ts,tsx}'", "lint:fix": "eslint '**/*.{js,ts,tsx}' --fix", "lint:tsc": "tsc --project ./tsconfig.json", "format": "prettier '**/*.{js,ts,tsx,json,feature}' --write", "format:check:changed": "git diff --name-only --diff-filter=ACM HEAD~1 | { grep -E '\\.(js|ts|tsx|json|feature)$' || true; } | xargs --no-run-if-empty prettier --check", "setup": "yarn clean && node scripts/setup.mjs", "setup:github-ci": "node scripts/setup.mjs --build-on-github-ci", "setup:flask": "export NEONIX_BUILD_TYPE='flask' && yarn setup", "setup:expo": "yarn clean && node scripts/setup.mjs --no-build-ios --no-build-android", "setup:e2e": "cd wdio && yarn install", "start:ios": "./scripts/build.sh ios debug", "start:ios:qa": "./scripts/build.sh ios qaDebug", "start:ios:e2e": "./scripts/build.sh ios debugE2E", "start:ios:e2e:qa": "./scripts/build.sh ios qadebugE2E", "start:ios:e2e:flask": "./scripts/build.sh ios flaskDebugE2E", "start:ios:device": "./scripts/build.sh ios debug --device", "start:ios:flask": "export NEONIX_BUILD_TYPE='flask' && ./scripts/build.sh ios flaskDebug", "start:android": "./scripts/build.sh android debug", "start:android:qa": "./scripts/build.sh android qaDebug", "start:android:e2e": "./scripts/build.sh android debugE2E", "start:android:flask": "export NEONIX_BUILD_TYPE='flask' && ./scripts/build.sh android flaskDebug", "build:announce": "node ./scripts/metamask-bot-build-announce-bitrise.js", "build:android:release": "./scripts/build.sh android release", "build:android:main:prod": "./scripts/build.sh android main production", "build:android:main:beta": "./scripts/build.sh android main beta", "build:android:main:rc": "./scripts/build.sh android main rc", "build:android:main:exp": "./scripts/build.sh android main exp", "build:android:main:local": "./scripts/build.sh android main local", "build:android:main:test": "./scripts/build.sh android main test", "build:android:main:e2e": "./scripts/build.sh android main e2e", "build:android:flask:prod": "./scripts/build.sh android flask production", "build:android:flask:local": "./scripts/build.sh android flask local", "build:android:flask:test": "./scripts/build.sh android flask test", "build:android:flask:e2e": "./scripts/build.sh android flask e2e", "build:android:qa:prod": "./scripts/build.sh android qa production", "build:android:qa:local": "./scripts/build.sh android qa local", "build:android:checksum": "./scripts/checksum.sh", "build:android:checksum:qa": "./scripts/checksum.sh QA", "build:android:checksum:flask": "export NEONIX_BUILD_TYPE='flask' && ./scripts/checksum.sh flask", "build:android:checksum:verify": "shasum -a 512 -c sha512sums.txt", "build:android:pre-release": "./scripts/build.sh android release --pre", "build:android:pre-release:bundle": "GENERATE_BUNDLE=true ./scripts/build.sh android release --pre", "build:android:pre-release:bundle:qa": "GENERATE_BUNDLE=true ./scripts/build.sh android QA --pre", "build:android:pre-release:bundle:flask": "export NEONIX_BUILD_TYPE='flask' && GENERATE_BUNDLE=true ./scripts/build.sh android flask --pre", "build:android:pre-release:bundle:beta": "export NEONIX_BUILD_TYPE='beta' && GENERATE_BUNDLE=true ./scripts/build.sh android release --pre", "build:ios:release": "./scripts/build.sh ios release", "build:ios:main:prod": "./scripts/build.sh ios main production", "build:ios:main:beta": "./scripts/build.sh ios main beta", "build:ios:main:rc": "./scripts/build.sh ios main rc", "build:ios:main:exp": "./scripts/build.sh ios main exp", "build:ios:main:e2e": "./scripts/build.sh ios main e2e", "build:ios:main:local": "./scripts/build.sh ios main local", "build:ios:main:test": "./scripts/build.sh ios main test", "build:ios:flask:prod": "./scripts/build.sh ios flask production", "build:ios:flask:local": "./scripts/build.sh ios flask local", "build:ios:flask:test": "./scripts/build.sh ios flask test", "build:ios:flask:e2e": "./scripts/build.sh ios flask e2e", "build:ios:qa:prod": "./scripts/build.sh ios qa production", "build:ios:qa:local": "./scripts/build.sh ios qa local", "build:ios:pre-release": "./scripts/build.sh ios release --pre", "build:ios:pre-qa": "./scripts/build.sh ios QA --pre", "build:ios:pre-flask": "export NEONIX_BUILD_TYPE='flask' && ./scripts/build.sh ios flask --pre", "build:ios:pre-beta": "export NEONIX_BUILD_TYPE='beta' && ./scripts/build.sh ios release --pre", "build:android:qa": "./scripts/build.sh android QA", "build:ios:qa": "./scripts/build.sh ios QA", "build:attribution": "./scripts/generate-attributions.sh", "release:android": "./scripts/build.sh android release && open android/app/build/outputs/apk/release/", "release:ios": "./scripts/build.sh ios release", "release:android:qa": "./scripts/build.sh android QA && open android/app/build/outputs/apk/release/", "test": "yarn test:unit", "test:unit": "jest ./app/ ./locales/ ./e2e/**/*.test.ts", "test:unit:update": "time jest -u ./app/", "test:api-specs": "detox reset-lock-file && detox test -c ios.sim.apiSpecs", "test:e2e:ios:main:prod": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c ios.sim.main.release", "test:e2e:android:main:prod": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c android.emu.release --headless --record-logs all", "test:e2e:android:run:github:qa-release": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c android.github_ci.release --headless --record-logs all", "test:e2e:ios-gha:main:prod": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c ios.github_ci.main.release --headless --record-logs all", "test:e2e:ios:flask:prod": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c ios.sim.flask.release", "test:e2e:android:flask:prod": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c android.emu.flask.release --headless --record-logs all", "test:e2e:ios:build:main-release": "IS_TEST='true' detox build -c ios.sim.main.release", "test:e2e:android:build:main-release": "IS_TEST='true' detox build -c android.emu.release", "test:e2e:ios:debug:build": "IS_TEST='true' detox build -c ios.sim.debug", "test:e2e:ios:debug:run": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox reset-lock-file && NODE_OPTIONS='--experimental-vm-modules' detox test -c ios.sim.debug", "test:e2e:android:debug:build": "IS_TEST='true' detox build -c android.emu.debug", "test:e2e:android:debug:run": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox test -c android.emu.debug", "test:e2e:ios:flask:build": "IS_TEST='true' detox build -c ios.sim.flask", "test:e2e:ios:flask:run": "IS_TEST='true' NODE_OPTIONS='--experimental-vm-modules' detox reset-lock-file && NODE_OPTIONS='--experimental-vm-modules' detox test -c ios.sim.flask", "test:wdio:ios": "yarn wdio ./wdio/config/ios.config.debug.js", "test:wdio:ios:browserstack": "yarn wdio ./wdio/config/ios.config.browserstack.js", "test:wdio:ios:browserstack:local": "yarn wdio ./wdio/config/ios.config.browserstack.local.js", "test:wdio:android": "yarn wdio ./wdio/config/android.config.debug.js", "test:wdio:android:browserstack": "yarn wdio ./wdio/config/android.config.browserstack.js", "test:wdio:android:browserstack:local": "yarn wdio ./wdio/config/android.config.browserstack.local.js", "test:reassure:baseline": "REASSURE=true yarn reassure --baseline", "test:reassure:branch": "REASSURE=true yarn reassure --branch && node -e \"const r=require('./.reassure/output.json'); if ((r.significant||[]).length>0) { console.error('Reassure: significant regressions detected'); process.exit(1);} else { process.exit(0);} \"", "run-appwright:android-bs": "yarn appwright test --project browserstack-android --config appwright/appwright.config.ts", "run-appwright:android-onboarding-bs": "yarn appwright test --project android-onboarding --config appwright/appwright.config.ts", "run-appwright:ios-onboarding-bs": "yarn appwright test --project ios-onboarding --config appwright/appwright.config.ts", "run-appwright:ios-bs": "yarn appwright test --project browserstack-ios --config appwright/appwright.config.ts", "run-appwright:android": "yarn appwright test --project android --config appwright/appwright.config.ts", "run-appwright:ios": "yarn appwright test --project ios --config appwright/appwright.config.ts", "test:depcheck": "yarn depcheck", "test:tgz-check": "./scripts/tgz-check.sh", "test:attribution-check": "./scripts/attributions-check.sh", "test:merge-coverage": "nyc report --temp-dir ./tests/coverage --report-dir ./tests/merged-coverage/ --reporter json --reporter text --reporter l<PERSON>vonly", "test:validate-coverage": "nyc check-coverage --nycrc-path ./coverage-thresholds.json -t ./tests/merged-coverage/", "update-changelog": "./scripts/auto-changelog.sh", "changeset-changelog": "wrap () { node ./scripts/generate-rc-commits.js \"$@\" && ./scripts/changelog-csv.sh }; wrap ", "prestorybook": "rnstl", "deduplicate": "yarn yarn-deduplicate && yarn install", "patch:tx": "./scripts/patch-transaction-controller.sh", "patch:approval": "./scripts/patch-approval-controller.sh", "storybook-generate": "sb-rn-get-stories", "storybook-watch": "sb-rn-watcher", "gen-bundle:ios": "yarn react-native bundle --platform ios --dev false --entry-file index.js --bundle-output ios/main.jsbundle", "gen-bundle:android": "yarn react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/main.jsbundle", "circular:deps": "dpdm ./app/* --circular --exit-code circular:1 --warning=false", "generate-icons": "yarn ts-node app/component-library/components/Icons/Icon/scripts/generate-assets.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md,feature}": ["prettier --write"]}, "resolutions": {"**/json-schema": "^0.4.0", "react-native-svg/**/nth-check": "^2.0.1", "**/node-fetch": "^2.6.7", "**/xml2js": ">=0.5.0", "react-native-level-fs/**/bl": "^1.2.3", "react-native-level-fs/levelup/semver": "^5.7.2", "@metamask/react-native-payments/validator": "^13.7.0", "**/minimist": "1.2.6", "d3-color": "3.1.0", "**/color-string": "^1.9.1", "tough-cookie": "4.1.3", "crypto-js": "4.2.0", "axios": "^1.12.0", "**/babel-runtime/regenerator-runtime": "^0.13.8", "redux-persist-filesystem-storage/react-native-blob-util": "^0.19.9", "xmldom": "npm:@xmldom/xmldom@0.7.13", "@storybook/react-native/chokidar/braces": "^3.0.3", "lint-staged/micromatch/braces": "^3.0.3", "@metamask/metamask-eth-abis": "3.1.1", "**/@walletconnect/socket-transport/ws": "^7.5.10", "**/@ethersproject/providers/ws": "^7.5.10", "**/appium-base-driver/webdriverio/puppeteer-core/ws": "^7.5.10", "**/puppeteer-core/ws": "^8.17.1", "react-native/ws": "^6.2.3", "socket.io-client/engine.io-client/ws": "^8.17.1", "micromatch": "4.0.8", "send": "0.19.0", "ethereumjs-util/**/secp256k1": "3.8.1", "**/secp256k1": "4.0.4", "**/@metamask/rpc-errors": "7.0.2", "**/@expo/image-utils/semver": "7.5.2", "base58-js": "1.0.0", "bech32": "2.0.0", "sha256-uint8array": "0.10.3", "express": "4.21.2", "nanoid": "^3.3.8", "**/@ethersproject/signing-key/elliptic": "^6.6.1", "**/@walletconnect/utils/elliptic": "^6.6.1", "@metamask/keyring-controller/@ethereumjs/tx": "npm:@ethereumjs/tx@5.4.0", "@keystonehq/metamask-airgapped-keyring": "^0.15.2", "metro/image-size": "^1.2.1", "content-hash/**/base-x": "3.0.11", "multihashes/**/base-x": "3.0.11", "@keystonehq/ur-decoder/**/base-x": "3.0.11", "@ethereumjs/tx": "^5.4.0", "appium-adb/@appium/support/form-data": "^4.0.4", "appium/@appium/support/form-data": "^4.0.0", "cipher-base": "1.0.5", "sha.js": "2.4.12", "appwright/**/@empiricalrun/llm": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/ffmpeg": "npm:noop-fn@1.0.0", "appwright/**/fluent-ffmpeg": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/darwin-arm64": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/darwin-x64": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/linux-arm": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/linux-arm64": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/linux-ia32": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/linux-x64": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/win32-ia32": "npm:noop-fn@1.0.0", "appwright/**/@ffmpeg-installer/win32-x64": "npm:noop-fn@1.0.0", "appwright/form-data": "4.0.4"}, "dependencies": {"@config-plugins/detox": "^9.0.0", "@consensys/native-ramps-sdk": "^2.0.0-beta.2", "@consensys/on-ramp-sdk": "2.1.11", "@craftzdog/react-native-buffer": "^6.1.0", "@deeeed/hyperliquid-node20": "^0.23.1-node20.1", "@ethersproject/abi": "^5.7.0", "@expo/metro-runtime": "~4.0.1", "@keystonehq/bc-ur-registry-eth": "^0.21.0", "@keystonehq/metamask-airgapped-keyring": "^0.15.2", "@keystonehq/ur-decoder": "^0.12.2", "@lavamoat/react-native-lockdown": "^0.0.2", "@ledgerhq/react-native-hw-transport-ble": "^6.34.1", "@metamask/account-api": "^0.9.0", "@metamask/account-tree-controller": "^0.15.1", "@metamask/accounts-controller": "^33.1.0", "@metamask/address-book-controller": "^6.1.0", "@metamask/app-metadata-controller": "^1.0.0", "@metamask/approval-controller": "^7.1.3", "@metamask/assets-controllers": "^74.3.2", "@metamask/base-controller": "^8.3.0", "@metamask/bitcoin-wallet-snap": "^1.0.0", "@metamask/bridge-controller": "^42.0.0", "@metamask/bridge-status-controller": "^42.0.0", "@metamask/chain-agnostic-permission": "^1.1.0", "@metamask/composable-controller": "^11.0.0", "@metamask/controller-utils": "^11.11.0", "@metamask/design-system-react-native": "^0.4.0", "@metamask/design-system-twrnc-preset": "^0.2.1", "@metamask/design-tokens": "^8.1.1", "@metamask/earn-controller": "^7.0.0", "@metamask/eip-5792-middleware": "^1.1.0", "@metamask/eip1193-permission-middleware": "^1.0.0", "@metamask/error-reporting-service": "^2.0.0", "@metamask/eth-hd-keyring": "^12.1.0", "@metamask/eth-json-rpc-filters": "^9.0.0", "@metamask/eth-json-rpc-middleware": "^18.0.0", "@metamask/eth-ledger-bridge-keyring": "11.1.0", "@metamask/eth-query": "^4.0.0", "@metamask/eth-sig-util": "^8.0.0", "@metamask/eth-snap-keyring": "^16.0.0", "@metamask/etherscan-link": "^2.0.0", "@metamask/ethjs-contract": "^0.4.1", "@metamask/ethjs-query": "^0.7.1", "@metamask/ethjs-unit": "^0.3.0", "@metamask/gas-fee-controller": "^24.0.0", "@metamask/json-rpc-engine": "^10.0.3", "@metamask/json-rpc-middleware-stream": "^8.0.7", "@metamask/key-tree": "^10.1.1", "@metamask/keyring-api": "^20.1.1", "@metamask/keyring-controller": "^22.1.0", "@metamask/keyring-internal-api": "^8.1.0", "@metamask/keyring-snap-client": "^5.0.0", "@metamask/logging-controller": "^6.0.4", "@metamask/message-signing-snap": "^1.1.2", "@metamask/metamask-eth-abis": "3.1.1", "@metamask/mobile-wallet-protocol-core": "^0.1.0", "@metamask/mobile-wallet-protocol-wallet-client": "^0.1.0", "@metamask/multichain-account-service": "^0.8.0", "@metamask/multichain-api-client": "^0.6.5", "@metamask/multichain-api-middleware": "^1.0.0", "@metamask/multichain-network-controller": "^0.10.0", "@metamask/multichain-transactions-controller": "^5.0.0", "@metamask/network-controller": "^24.1.0", "@metamask/network-enablement-controller": "^1.1.0", "@metamask/notification-services-controller": "^18.1.0", "@metamask/permission-controller": "^11.0.6", "@metamask/phishing-controller": "^13.1.0", "@metamask/post-message-stream": "^10.0.0", "@metamask/ppom-validator": "0.36.0", "@metamask/preferences-controller": "^18.4.0", "@metamask/preinstalled-example-snap": "^0.7.1", "@metamask/profile-sync-controller": "^25.0.0", "@metamask/react-native-acm": "^1.0.1", "@metamask/react-native-actionsheet": "2.4.2", "@metamask/react-native-button": "^3.0.0", "@metamask/react-native-payments": "^2.0.0", "@metamask/react-native-search-api": "1.0.1", "@metamask/react-native-webview": "^14.5.0", "@metamask/remote-feature-flag-controller": "^1.6.0", "@metamask/rpc-errors": "^7.0.2", "@metamask/scure-bip39": "^2.1.0", "@metamask/sdk-analytics": "0.0.5", "@metamask/sdk-communication-layer": "0.33.1", "@metamask/seedless-onboarding-controller": "^4.0.0", "@metamask/selected-network-controller": "^22.1.0", "@metamask/signature-controller": "^32.0.0", "@metamask/slip44": "^4.2.0", "@metamask/smart-transactions-controller": "^18.1.0", "@metamask/snaps-controllers": "^14.2.2", "@metamask/snaps-execution-environments": "^10.2.1", "@metamask/snaps-rpc-methods": "^13.5.0", "@metamask/snaps-sdk": "^9.3.0", "@metamask/snaps-utils": "^11.5.0", "@metamask/solana-wallet-snap": "^2.3.8", "@metamask/solana-wallet-standard": "^0.5.1", "@metamask/stake-sdk": "^3.2.0", "@metamask/swappable-obj-proxy": "^2.1.0", "@metamask/swaps-controller": "^13.3.0", "@metamask/token-search-discovery-controller": "^3.1.0", "@metamask/transaction-controller": "^60.3.0", "@metamask/utils": "^11.4.2", "@ngraveio/bc-ur": "^1.1.6", "@notifee/react-native": "^9.0.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "^1.16.1", "@react-native-community/checkbox": "^0.5.20", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native-community/cli-server-api": "^17.0.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.4.3", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/app": "^20.5.0", "@react-native-firebase/messaging": "^20.5.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native/babel-preset": "0.76.9", "@react-native/eslint-config": "0.76.9", "@react-native/typescript-config": "0.76.9", "@react-navigation/bottom-tabs": "^5.11.11", "@react-navigation/compat": "^5.3.20", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "@reduxjs/toolkit": "^1.9.7", "@reown/walletkit": "^1.2.3", "@segment/analytics-react-native": "^2.20.3", "@segment/sovran-react-native": "^1.0.4", "@sentry/browser": "~8.54.0", "@sentry/core": "~8.54.0", "@sentry/react": "~8.54.0", "@sentry/react-native": "~6.10.0", "@shopify/flash-list": "2.0.1", "@solana/addresses": "2.0.0", "@tradle/react-native-http": "2.0.1", "@types/he": "^1.2.3", "@types/react-test-renderer": "^18.0.0", "@viem/anvil": "^0.0.10", "@walletconnect/client": "^1.8.0", "@walletconnect/core": "^2.19.2", "@walletconnect/react-native-compat": "2.19.2", "@walletconnect/utils": "^2.19.2", "@xmldom/xmldom": "^0.8.10", "appium-adb": "^9.11.4", "appwright": "^0.1.45", "asyncstorage-down": "4.2.0", "axios": "^1.8.2", "base-64": "1.0.0", "bignumber.js": "^9.0.1", "bitcoin-address-validation": "2.2.3", "bnjs4": "npm:bn.js@^4.12.0", "bnjs5": "npm:bn.js@^5.2.1", "buffer": "6.0.3", "cockatiel": "^3.1.2", "compare-versions": "^3.6.0", "content-hash": "2.5.2", "contentful": "^11.5.22", "cron-parser": "^4.9.0", "cross-spawn": "7.0.6", "crypto-js": "^4.2.0", "d3-shape": "^3.2.0", "dayjs": "^1.11.13", "eciesjs": "^0.3.15", "eth-ens-namehash": "2.0.8", "eth-url-parser": "1.0.4", "ethereumjs-abi": "^0.6.8", "ethereumjs-util": "^7.0.10", "ethers": "^5.0.14", "ethjs-ens": "2.0.1", "event-target-shim": "^6.0.2", "eventemitter2": "^6.4.9", "events": "3.0.0", "expo": "~52.0.47", "expo-apple-authentication": "~7.1.3", "expo-auth-session": "~6.0.3", "expo-build-properties": "~0.13.2", "expo-dev-client": "~5.0.18", "expo-file-system": "~18.0.7", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-sensors": "~14.0.2", "fast-equals": "^5.2.2", "fuse.js": "3.4.4", "he": "^1.2.0", "https-browserify": "0.0.1", "human-standard-token-abi": "^2.0.0", "humanize-duration": "^3.27.2", "is-url": "^1.2.4", "lodash": "^4.17.21", "lottie-react-native": "6.7.2", "luxon": "^3.5.0", "mockttp": "^3.15.2", "multihashes": "0.4.14", "number-to-bn": "1.7.0", "path": "0.12.7", "pbkdf2": "3.1.3", "pify": "6.1.0", "prop-types": "15.7.2", "pump": "3.0.0", "punycode": "^2.1.1", "qs": "6.12.1", "query-string": "^6.12.1", "randomfill": "^1.0.4", "react": "18.3.1", "react-native": "0.76.9", "react-native-aes-crypto": "3.0.3", "react-native-aes-crypto-forked": "git+https://github.com/MetaMask/react-native-aes-crypto-forked.git#397d5db5250e8e7408294807965b5b9fd4ca6a25", "react-native-animatable": "^1.3.3", "react-native-background-timer": "2.1.1", "react-native-ble-plx": "3.4.0", "react-native-blob-jsi-helper": "^0.3.1", "react-native-blob-util": "^0.19.9", "react-native-branch": "^5.6.2", "react-native-browser-polyfill": "0.1.2", "react-native-confetti": "^0.1.0", "react-native-confetti-cannon": "^1.5.0", "react-native-confirmation-code-field": "^7.6.1", "react-native-crypto": "2.2.1", "react-native-default-preference": "^1.4.3", "react-native-device-info": "^9.0.2", "react-native-elevated-view": "0.0.6", "react-native-fade-in-image": "1.4.1", "react-native-fast-crypto": "^2.2.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-get-random-values": "^1.8.0", "react-native-gzip": "^1.1.0", "react-native-i18n": "2.0.15", "react-native-in-app-review": "^4.3.3", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-jazzicon": "^0.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "8.0.0", "react-native-level-fs": "3.0.1", "react-native-linear-gradient": "^2.8.3", "react-native-material-textfield": "0.16.1", "react-native-mmkv": "^3.2.0", "react-native-modal": "^14.0.0-rc.1", "react-native-os": "^1.2.6", "react-native-pager-view": "^6.7.0", "react-native-permissions": "^3.7.2", "react-native-progress": "3.5.0", "react-native-qrcode-svg": "5.1.2", "react-native-quick-base64": "^2.2.0", "react-native-quick-crypto": "^0.7.15", "react-native-randombytes": "^3.5.3", "react-native-reanimated": "^3.17.2", "react-native-release-profiler": "^0.4.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "3.37.0", "react-native-scrollable-tab-view": "ptomasroos/react-native-scrollable-tab-view#583a66f7fd3eb2cac1a05c71d7167d78a8ad9de6", "react-native-sensors": "5.3.0", "react-native-share": "7.3.7", "react-native-size-matters": "0.4.0", "react-native-skeleton-placeholder": "^5.0.0", "react-native-step-indicator": "^1.0.3", "react-native-svg": "^15.11.1", "react-native-svg-charts": "^5.4.0", "react-native-swipe-gestures": "1.0.3", "react-native-tcp": "aprock/react-native-tcp#98fbc801f0586297f16730b2f4c75eef15dfabcd", "react-native-url-polyfill": "^1.3.0", "react-native-vector-icons": "10.2.0", "react-native-video": "^6.10.1", "react-native-view-shot": "^3.1.2", "react-native-vision-camera": "^4.6.4", "react-native-web": "~0.19.13", "react-native-webview-invoke": "^0.6.2", "react-redux": "^8.1.3", "reactotron-react-native": "^5.1.14", "readable-stream": "2.3.7", "redux": "^4.2.1", "redux-mock-store": "1.5.4", "redux-persist": "6.0.0", "redux-persist-filesystem-storage": "^4.2.0", "redux-saga": "^1.3.0", "redux-thunk": "^2.4.2", "reselect": "^5.1.1", "rive-react-native": "^9.3.4", "rxjs": "^7.8.1", "socket.io-client": "^4.5.3", "stream-browserify": "3.0.0", "through2": "3.0.1", "unicode-confusables": "^0.1.1", "uri-js": "^4.4.1", "url": "0.11.0", "url-parse": "1.5.9", "uuid": "^8.3.2", "valid-url": "1.0.9", "viem": "^2.28.0", "vm-browserify": "1.1.2", "zxcvbn": "4.4.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/preset-env": "^7.25.3", "@babel/register": "^7.24.6", "@babel/runtime": "^7.25.0", "@cucumber/message-streams": "^4.0.1", "@cucumber/messages": "^22.0.0", "@ethersproject/contracts": "^5.7.0", "@ethersproject/providers": "^5.7.2", "@lavamoat/allow-scripts": "^3.0.4", "@lavamoat/git-safe-dependencies": "^0.2.1", "@metamask/browser-passworder": "^5.0.0", "@metamask/build-utils": "^1.0.0", "@metamask/eslint-config-typescript": "^9.0.0", "@metamask/eslint-plugin-design-tokens": "^1.0.0", "@metamask/foundryup": "1.0.0", "@metamask/mobile-provider": "^3.0.0", "@metamask/object-multiplex": "^1.1.0", "@metamask/providers": "^18.3.1", "@metamask/test-dapp": "9.5.0", "@metamask/test-dapp-multichain": "^0.17.1", "@metamask/test-dapp-solana": "^0.3.0", "@octokit/rest": "^21.0.0", "@open-rpc/mock-server": "^1.7.5", "@open-rpc/schema-utils-js": "^1.16.2", "@open-rpc/test-coverage": "^2.2.2", "@react-native/metro-config": "0.76.9", "@rpii/wdio-html-reporter": "^7.7.1", "@storybook/addon-controls": "^7.5.1", "@storybook/addon-ondevice-controls": "^6.5.6", "@storybook/builder-webpack5": "^7.5.1", "@storybook/react-native": "^6.5.6", "@testing-library/react": "14.0.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.2.0", "@types/bnjs4": "npm:@types/bn.js@^4.11.6", "@types/bnjs5": "npm:@types/bn.js@^5.1.6", "@types/crypto-js": "^4.1.1", "@types/enzyme": "^3.10.12", "@types/eth-url-parser": "^1.0.0", "@types/i18n-js": "^3.8.4", "@types/is-url": "^1.2.30", "@types/jest": "^29.5.12", "@types/koa": "^2.15.0", "@types/lodash": "^4.14.184", "@types/luxon": "^3.4.2", "@types/node": "^20.12.8", "@types/qs": "^6.9.15", "@types/react": "^18.2.6", "@types/react-native-background-timer": "^2.0.0", "@types/react-native-elevated-view": "^0.0.4", "@types/react-native-material-textfield": "^0.16.5", "@types/react-native-scrollable-tab-view": "^0.10.3", "@types/react-native-svg-charts": "^5.0.12", "@types/react-native-vector-icons": "^6.4.13", "@types/react-native-video": "^5.0.14", "@types/redux-mock-store": "^1.0.3", "@types/url-parse": "^1.4.8", "@types/valid-url": "^1.0.4", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@walletconnect/types": "^2.19.2", "@wdio/appium-service": "^7.19.1", "@wdio/browserstack-service": "^7.26.0", "@wdio/cli": "^7.19.1", "@wdio/cucumber-framework": "^7.19.1", "@wdio/junit-reporter": "^7.25.4", "@wdio/local-runner": "^7.19.1", "@wdio/spec-reporter": "^7.19.1", "@welldone-software/why-did-you-render": "^8.0.1", "appium": "^2.12.1", "appium-adb": "^9.11.4", "appium-uiautomator2-driver": "4.2.7", "appium-xcuitest-driver": "5.16.1", "assert": "^1.5.0", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-react-compiler": "^19.1.0-rc.2", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "babel-plugin-transform-remove-console": "6.9.4", "base64-js": "^1.5.1", "browserstack-local": "^1.5.1", "chromedriver": "^123.0.1", "depcheck": "^1.4.7", "deprecated-react-native-prop-types": "^5.0.0", "detox": "^20.35.0", "dotenv": "^16.0.3", "dpdm": "^3.14.0", "enzyme": "3.9.0", "enzyme-adapter-react-16": "1.10.0", "enzyme-to-json": "3.3.5", "eslint": "^8.44.0", "eslint-config-prettier": "^8.1.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "43.0.7", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "eslint-plugin-react-native": "^4.0.0", "eslint-plugin-tailwindcss": "^3.18.2", "execa": "^8.0.1", "fbjs-scripts": "^3.0.1", "fs-extra": "^10.1.0", "ganache": "^7.9.2", "husky": "^9.1.7", "improved-yarn-audit": "^3.0.0", "jest": "^29.7.0", "jest-junit": "^15.0.0", "jetifier": "2.0.0", "koa": "^2.14.2", "lint-staged": "10.5.4", "listr2": "^8.0.2", "metro-react-native-babel-preset": "~0.76.9", "metro-react-native-babel-transformer": "~0.76.9", "multiple-cucumber-html-reporter": "^3.0.1", "nock": "^13.3.1", "nyc": "^15.1.0", "patch-package": "^6.2.2", "prettier": "2.8.8", "prettier-plugin-gherkin": "^1.1.1", "react-compiler-runtime": "^19.1.0-rc.2", "react-dom": "18.2.0", "react-native-launch-arguments": "^4.0.1", "react-native-performance": "^5.1.2", "react-native-storybook-loader": "^2.0.4", "react-native-svg-asset-plugin": "^0.5.0", "react-native-svg-transformer": "^1.0.0", "react-test-renderer": "18.3.1", "reassure": "^1.4.0", "redux-devtools-expo-dev-plugin": "^1.0.0", "redux-saga-test-plan": "^4.0.6", "regenerator-runtime": "0.13.9", "serve-handler": "^6.1.5", "simple-git": "^3.25.0", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "typescript": "~5.4.5", "wdio-cucumberjs-json-reporter": "^4.4.3", "webextension-polyfill": "^0.12.0", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "xhr2": "^0.2.1", "xml2js": "^0.5.0", "yarn-deduplicate": "^6.0.2"}, "config": {"react-native-storybook-loader": {"searchDir": ["./app/component-library/components", "./app/components", "./app/component-library/components-temp"], "pattern": "**/*.stories.@(js|tsx)", "outputFile": "./storybook/storyLoader.js"}}, "engines": {"node": "^20.18.0", "yarn": "^1.22.22"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.34.3", "@img/sharp-libvips-darwin-arm64": "^1.2.1", "@img/sharp-libvips-linux-x64": "^1.2.1", "@img/sharp-linux-x64": "^0.34.3"}, "lavamoat": {"allowScripts": {"ethereumjs-abi>ethereumjs-util>ethereum-cryptography>keccak": true, "@sentry/react-native>@sentry/cli": true, "@storybook/manager-webpack5>@storybook/core-common>webpack>watchpack>watchpack-chokidar2>chokidar>fsevents": false, "@storybook/addon-controls>@storybook/core-common>esbuild": false, "@wdio/cucumber-framework>@cucumber/cucumber>duration>es5-ext": false, "appium-adb>@appium/support>sharp": true, "appium>appium-android-driver>appium-chromedriver": false, "appium>appium-base-driver>webdriverio>@types/puppeteer-core>@types/puppeteer>puppeteer": false, "appium>appium-flutter-driver>rpc-websockets>bufferutil": false, "appium>appium-flutter-driver>rpc-websockets>utf-8-validate": false, "appium>appium-tizen-driver>jimp>@babel/polyfill>core-js": false, "appium>appium-tizen-driver>jimp>core-js": false, "appium>appium-windows-driver": false, "chromedriver": false, "detox": true, "detox>bunyan>dtrace-provider": false, "eciesjs>secp256k1": true, "ethereumjs-util>keccak": true, "ethereumjs-util>secp256k1": true, "ganache>@trufflesuite/bigint-buffer": false, "ganache>bufferutil": false, "ganache>keccak": true, "ganache>leveldown": false, "ganache>secp256k1": true, "ganache>utf-8-validate": false, "husky": false, "react-native-inappbrowser-reborn": false, "react-native-svg-asset-plugin>sharp": true, "@storybook/builder-webpack5>@swc/core": false, "@metamask/sdk-communication-layer>bufferutil": false, "@metamask/sdk-communication-layer>utf-8-validate": false, "detox>ws>bufferutil": false, "@metamask/notification-services-controller>firebase>@firebase/firestore>@grpc/proto-loader>protobufjs": false, "@metamask/sdk-communication-layer>eciesjs>secp256k1": false, "detox>ws>utf-8-validate": false, "ganache>@trufflesuite/uws-js-unofficial>utf-8-validate": false, "@react-native-firebase/app>firebase>@firebase/firestore>@grpc/proto-loader>protobufjs": false, "ethereumjs-util>ethereum-cryptography>keccak": true, "appium": false, "appium>@appium/support>sharp": false, "@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/domain-service>eip55>keccak": false, "@storybook/addon-ondevice-controls>core-js": false, "viem>ws>bufferutil": false, "viem>ws>utf-8-validate": false, "detox>@wix-pilot/core>canvas": true, "ts-node>@swc/core": false, "@metamask/test-dapp-solana>@solana/spl-token>@solana/buffer-layout-utils>bigint-buffer": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-trezor>@trezor/connect-web>@trezor/connect>@trezor/blockchain-link>@trezor/websocket-client>ws>bufferutil": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-trezor>@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-trezor>@trezor/connect-web>@trezor/connect>@trezor/transport>usb": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-trezor>@trezor/connect-web>@trezor/connect>@trezor/utxo-lib>blake-hash": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-trezor>@trezor/connect-web>@trezor/connect>@trezor/utxo-lib>tiny-secp256k1": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-walletconnect>@walletconnect/solana-adapter>@reown/appkit>@reown/appkit-common>viem>ws>bufferutil": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-walletconnect>@walletconnect/solana-adapter>@reown/appkit>@reown/appkit-controllers>viem>ws>bufferutil": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-walletconnect>@walletconnect/solana-adapter>@reown/appkit>@reown/appkit-utils>viem>ws>bufferutil": false, "@metamask/test-dapp-solana>@solana/wallet-adapter-wallets>@solana/wallet-adapter-walletconnect>@walletconnect/solana-adapter>@reown/appkit>viem>ws>bufferutil": false, "@metamask/test-dapp-solana>@solana/web3.js>rpc-websockets>bufferutil": false, "@metamask/mobile-wallet-protocol-core>centrifuge>protobufjs": false, "appwright>appium": false, "appwright>appium>@appium/support>sharp": false, "appwright>webdriver>@wdio/utils>edgedriver": false, "appwright>webdriver>@wdio/utils>geckodriver": false, "appium-xcuitest-driver>appium-ios-simulator>@appium/support>sharp": false, "appium-xcuitest-driver>appium-remote-debugger>@appium/support>sharp": false, "appium-xcuitest-driver>appium-webdriveragent>@appium/support>sharp": false, "appwright>appium-xcuitest-driver>appium-webdriveragent>appium-ios-device>@appium/support>sharp": false}}, "packageManager": "yarn@1.22.22", "foundryup": {"binaries": ["anvil"], "checksums": {"algorithm": "sha256", "binaries": {"anvil": {"darwin-amd64": "8404e555223fe884557d5e22de494baf8b5f0b82c6f87a4c790c5150e546c9d0", "darwin-arm64": "888500bc210752e71a355ed4d492ad6dcb4c0ef54d283c105a29a5ccc73d0dbd", "linux-amd64": "6104069b183fa0f3cdcb692681da9dbd203a3c1bceb435853bbf7abd991c649e", "linux-arm64": "d66ed8f848e829882ebb65d28aaac72aeab6a101655bb62147186040655928b5", "win32-amd64": "6c71d9a7be39ed32b53c89bdbc83aa748f41587517212ffe2a8b955c3e9c2e9b"}}}, "version": "v0.3.0"}}