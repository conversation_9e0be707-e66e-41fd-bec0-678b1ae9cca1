export const HOME_FAVORITE_BUTTON = 'Favorites';

export const HOME_FAVORITES_UNISWAP_CARD_TITLE = 'My Uniswap';

export const HOME_FAVORITES_CARDS_URL = 'https://uniswap.exchange';

export const NO_FAVORITES_MESSAGE = 'You have no favorites yet';

export const ETHEREUM_PHISHING_DETECTION_BACK_BUTTON = 'Back to safety';

export const BRUNO_MAIN_ID = 'inner';

export const ERROR_PAGE_TITLE = 'error-page-title';

export const ERROR_PAGE_MESSAGE = 'error-page-message';

export const ERROR_PAGE_RETURN_BUTTON = 'error-page-return-button';

export const REDDIT_ICON = 'www.reddit';

export const UNISWAP_CONNECT_BUTTON = 'Connect';

export const UNISWAP_METAMASK_WALLET_BUTTON = 'MetaMask';

export const UNISWAP_WALLET_PROFILE_ICON =
  '//android.view.View[@resource-id="root"]/android.view.View[1]/android.view.View/android.widget.Button';
