@androidApp
@smoke
@onboarding
Feature: Onboarding Create New Wallet

  Scenario: Onboarding New wallet
    User opens the app for first time and creates a new wallet.
    Given the Welcome Screen is displayed
    When I tap "Get started"
    And Terms of Use is displayed
    And I agree to terms
    And Terms of Use is not displayed
    Then Wallet setup screen is displayed
    When On Wallet Setup Screen I tap "Create a new wallet"
    Then I tap "Import using Secret Recovery Phrase"
    Then I am presented with a new Account screen with password fields
    And I input a new password "**********"
    And I confirm the new password "**********"
    And secure wallet page is presented
    Then I select remind me later on secure wallet screen
    And Select "Skip" on remind secure modal
    And On Wallet Setup Screen I tap "Agree"
    Then I should proceed to the new wallet
