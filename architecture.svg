<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1684px" height="1544px" viewBox="-0.5 -0.5 1684 1544" content="&lt;mxfile host=&quot;www.draw.io&quot; modified=&quot;2020-01-16T21:17:39.760Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.117 Safari/537.36&quot; version=&quot;12.5.5&quot; etag=&quot;yXqs4JPf6Dr1tBg5_tgn&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;3edaxd6d9PyB9akGHe6N&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="1" y="1" width="1680" height="1540" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="241" y="216.84" width="1244.16" height="1244.16" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="835.17" y="731" width="615.83" height="570" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 406 317.25 L 746 317.25 L 756 327.25 L 756 657.25 L 416 657.25 L 406 647.25 L 406 317.25 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,581,487.25)" pointer-events="all"/><path d="M 416 657.25 L 416 327.25 L 406 317.25 M 416 327.25 L 756 327.25" fill="none" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,581,487.25)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 327px; margin-left: 415px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Verdana; color: #000000; line-height: 1.2; pointer-events: all; text-decoration: underline; white-space: nowrap; ">Browser</div></div></div></foreignObject><text x="415" y="339" fill="#000000" font-family="Verdana" font-size="12px" text-decoration="underline">Browser</text></switch></g><path d="M 976 279.75 L 1186 279.75 L 1196 289.75 L 1196 609.75 L 986 609.75 L 976 599.75 L 976 279.75 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1086,444.75)" pointer-events="all"/><path d="M 986 609.75 L 986 289.75 L 976 279.75 M 986 289.75 L 1196 289.75" fill="none" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1086,444.75)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 350px; margin-left: 925px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Verdana; color: #000000; line-height: 1.2; pointer-events: all; text-decoration: underline; white-space: nowrap; ">WALLET</div></div></div></foreignObject><text x="925" y="362" fill="#000000" font-family="Verdana" font-size="12px" text-decoration="underline">WALLET</text></switch></g><path d="M 806 866 L 1266 866 L 1276 876 L 1276 1226 L 816 1226 L 806 1216 L 806 866 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1041,1046)" pointer-events="all"/><path d="M 816 1226 L 816 876 L 806 866 M 816 876 L 1276 876" fill="none" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1041,1046)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 826px; margin-left: 865px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Verdana; color: #000000; line-height: 1.2; pointer-events: all; text-decoration: underline; white-space: nowrap; ">ENGINE</div></div></div></foreignObject><text x="865" y="838" fill="#000000" font-family="Verdana" font-size="12px" text-decoration="underline">ENGINE</text></switch></g><path d="M 1084.58 806.04 L 1085.54 561.12" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 1084.56 811.29 L 1081.09 804.28 L 1084.58 806.04 L 1088.09 804.31 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 1085.56 555.87 L 1089.04 562.88 L 1085.54 561.12 L 1082.04 562.85 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="941" y="891" width="210" height="350" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="966" y="911" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 926px; margin-left: 968px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TransactionController</div></div></div></foreignObject><text x="1046" y="930" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TransactionController</text></switch></g><rect x="966" y="1011" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1026px; margin-left: 968px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">KeyringController</div></div></div></foreignObject><text x="1046" y="1030" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">KeyringController</text></switch></g><rect x="966" y="961" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 976px; margin-left: 968px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">NetworkController</div></div></div></foreignObject><text x="1046" y="980" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">NetworkController</text></switch></g><rect x="971" y="1181" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1196px; margin-left: 973px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">AssetsController</div></div></div></foreignObject><text x="1051" y="1200" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">AssetsController</text></switch></g><path d="M 1053 1159 L 1052.73 1063" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="991" y="851" width="100" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 871px; margin-left: 993px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">GABA</div></div></div></foreignObject><text x="1041" y="875" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">GABA</text></switch></g><rect x="451" y="372.25" width="260" height="260" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="531" y="332.25" width="100" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 352px; margin-left: 533px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TABS</div></div></div></foreignObject><text x="581" y="356" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TABS</text></switch></g><rect x="471" y="382.25" width="220" height="110" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="526" y="377.25" width="100" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 397px; margin-left: 528px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TAB 1</div></div></div></foreignObject><text x="576" y="401" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TAB 1</text></switch></g><rect x="481" y="407.25" width="200" height="75" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 501 801 L 691 801 L 701 811 L 701 1101 L 511 1101 L 501 1091 L 501 801 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,601,951)" pointer-events="all"/><path d="M 511 1101 L 511 811 L 501 801 M 511 811 L 701 811" fill="none" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,601,951)" pointer-events="all"/><path d="M 616.45 448.97 L 787.7 166.45" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 613.73 453.46 L 614.36 445.66 L 616.45 448.97 L 620.35 449.29 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 790.42 161.96 L 789.79 169.76 L 787.7 166.45 L 783.8 166.13 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="528.5" y="407.25" width="95" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 93px; height: 1px; padding-top: 417px; margin-left: 531px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">WEBVIEW</div></div></div></foreignObject><text x="576" y="421" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">WEBVIEW</text></switch></g><rect x="529" y="454.25" width="90" height="17" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 463px; margin-left: 531px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DAPP 1</div></div></div></foreignObject><text x="574" y="466" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">DAPP 1</text></switch></g><path d="M 596.5 454.25 L 596.5 454.25" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 596.5 454.25 L 596.5 454.25 L 596.5 454.25" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 596.5 454.25 L 596.5 454.25 L 596.5 454.25 L 596.5 454.25 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 580.69 445.88 L 580.72 435.96" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580.66 451.13 L 577.19 444.12 L 580.69 445.88 L 584.19 444.15 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 580.75 430.71 L 584.22 437.72 L 580.72 435.96 L 577.22 437.69 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="471" y="507.25" width="220" height="110" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="526" y="502.25" width="100" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 522px; margin-left: 528px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TAB N</div></div></div></foreignObject><text x="576" y="526" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TAB N</text></switch></g><rect x="481" y="532.25" width="200" height="75" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="528.5" y="532.25" width="95" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 93px; height: 1px; padding-top: 542px; margin-left: 531px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">WEBVIEW</div></div></div></foreignObject><text x="576" y="546" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">WEBVIEW</text></switch></g><rect x="529" y="579.25" width="90" height="17" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 588px; margin-left: 531px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DAPP N</div></div></div></foreignObject><text x="574" y="591" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">DAPP N</text></switch></g><path d="M 580.69 570.88 L 580.72 560.96" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580.66 576.13 L 577.19 569.12 L 580.69 570.88 L 584.19 569.15 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 580.75 555.71 L 584.22 562.72 L 580.72 560.96 L 577.22 562.69 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 615.93 573.52 L 808.22 176.73" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 613.64 578.24 L 613.54 570.42 L 615.93 573.52 L 619.84 573.47 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 810.51 172.01 L 810.61 179.83 L 808.22 176.73 L 804.31 176.78 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="508.5" y="976" width="185" height="35" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 183px; height: 1px; padding-top: 994px; margin-left: 511px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MiddleWares</div></div></div></foreignObject><text x="601" y="997" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MiddleWares</text></switch></g><path d="M 1006 1266 L 1066 1266 L 1076 1276 L 1076 1526 L 1016 1526 L 1006 1516 L 1006 1266 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1041,1396)" pointer-events="all"/><path d="M 1016 1526 L 1016 1276 L 1006 1266 M 1016 1276 L 1076 1276" fill="none" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,1041,1396)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1376px; margin-left: 915px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Verdana; color: #000000; line-height: 1.2; pointer-events: all; text-decoration: underline; white-space: nowrap; ">80</div></div></div></foreignObject><text x="915" y="1388" fill="#000000" font-family="Verdana" font-size="12px" text-decoration="underline">80</text></switch></g><path d="M 1040 1342.76 L 1040 1296.24" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 1036.5 1336.88 L 1040 1343.88 L 1043.5 1336.88" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 1043.5 1302.12 L 1040 1295.12 L 1036.5 1302.12" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 687.37 569.77 L 781 570 L 781 771 L 606 771 L 606 844.63" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 682.12 569.75 L 689.13 566.27 L 687.37 569.77 L 689.11 573.27 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 606 849.88 L 602.5 842.88 L 606 844.63 L 609.5 842.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 854.63 951.98 L 757.37 951.6" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 859.88 952 L 852.87 955.47 L 854.63 951.98 L 852.9 948.47 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 752.12 951.58 L 759.13 948.11 L 757.37 951.6 L 759.1 955.11 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="996" y="394.75" width="180" height="47.5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 419px; margin-left: 998px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ASSETS</div></div></div></foreignObject><text x="1086" y="422" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ASSETS</text></switch></g><rect x="996" y="459.75" width="180" height="47.5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 484px; margin-left: 998px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">NFTS</div></div></div></foreignObject><text x="1086" y="487" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">NFTS</text></switch></g><path d="M 875.34 176.01 L 995.84 329.74" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 872.1 171.88 L 879.17 175.23 L 875.34 176.01 L 873.66 179.55 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 999.08 333.87 L 992.01 330.52 L 995.84 329.74 L 997.52 326.2 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 811.41 141 C 811.41 121 811.41 111 831.41 111 C 818.08 111 818.08 91 831.41 91 C 844.74 91 844.74 111 831.41 111 C 851.41 111 851.41 121 851.41 141 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="534.83" y="946" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 953px; margin-left: 537px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">JSON RPC ENGINE</div></div></div></foreignObject><text x="537" y="965" fill="#000000" font-family="Helvetica" font-size="12px">JSON RPC ENGINE</text></switch></g><rect x="981" y="1391" width="130" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 1398px; margin-left: 983px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">ETHEREUM NODE</div></div></div></foreignObject><text x="983" y="1410" fill="#000000" font-family="Helvetica" font-size="12px">ETHEREUM NODE</text></switch></g><rect x="521" y="866" width="160" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 873px; margin-left: 523px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">BACKGROUND BRIDGE</div></div></div></foreignObject><text x="523" y="885" fill="#000000" font-family="Helvetica" font-size="12px">BACKGROUND BRIDGE</text></switch></g><rect x="811.41" y="141" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 148px; margin-left: 813px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 20px"><b>USER</b></font></div></div></div></foreignObject><text x="813" y="160" fill="#000000" font-family="Helvetica" font-size="12px">USER</text></switch></g><rect x="241" y="221" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 228px; margin-left: 243px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 30px"><u>APP</u></font></div></div></div></foreignObject><text x="243" y="240" fill="#000000" font-family="Helvetica" font-size="12px">APP</text></switch></g><path d="M 474.63 463.53 L 371 464 L 371 771 L 577 771 L 577.28 843.43" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 479.88 463.51 L 472.9 467.04 L 474.63 463.53 L 472.87 460.04 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 577.3 848.68 L 573.77 841.7 L 577.28 843.43 L 580.77 841.67 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="1260" y="876" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 891px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">InstaPay</div></div></div></foreignObject><text x="1340" y="895" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">InstaPay</text></switch></g><rect x="1260" y="926" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 941px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">WalletConnect</div></div></div></foreignObject><text x="1340" y="945" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">WalletConnect</text></switch></g><rect x="1260" y="976" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 991px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">DeepLinkManager</div></div></div></foreignObject><text x="1340" y="995" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">DeepLinkManager</text></switch></g><rect x="1260" y="1031" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1046px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Analytics</div></div></div></foreignObject><text x="1340" y="1050" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Analytics</text></switch></g><rect x="835.17" y="731" width="100" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 751px; margin-left: 837px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 18px">CORE</font></div></div></div></foreignObject><text x="885" y="755" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">CORE</text></switch></g><rect x="1260" y="1086" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1101px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Encryptor</div></div></div></foreignObject><text x="1340" y="1105" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Encryptor</text></switch></g><rect x="1260" y="1136" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1151px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">SecureKeychain</div></div></div></foreignObject><text x="1340" y="1155" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">SecureKeychain</text></switch></g><rect x="1260" y="1186" width="160" height="30" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 1201px; margin-left: 1262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">TXNotificationManager</div></div></div></foreignObject><text x="1340" y="1205" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TXNotificationManager</text></switch></g></g></svg>
