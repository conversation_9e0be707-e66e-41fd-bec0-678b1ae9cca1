# Sign up and generate your own keys at pubnub.com
# Then rename this file to ".js.env" and rebuild the app
#
# In order for this feature to work properly, you need to
# build metamask-extension from source (https://github.com/MetaMask/metamask-extension)
# and set your the same values there.
#
# For more info take a look at https://github.com/MetaMask/metamask-extension/pull/5955

export MM_PUBNUB_SUB_KEY=""
export MM_PUBNUB_PUB_KEY=""
export MM_OPENSEA_KEY=""
export MM_FOX_CODE="EXAMPLE_FOX_CODE"

# NOTE: Non-MetaMask only, will need to create an account and generate
# API key at https://infura.io in order to connect to main and test nets.
# More info: https://github.com/MetaMask/metamask-mobile/issues/1984

export MM_INFURA_PROJECT_ID="null"
export IGNORE_BOXLOGS_DEVELOPMENT="false"

# Sentry.init dsn value
export MM_SENTRY_DSN=""
# Sentry.init debug option is set to true in dev environments by default. Set this to "false" to turn it off
export SENTRY_DEBUG_DEV="false"
# Determines if Sentry will auto upload source maps and debug files. Disabled by default
export SENTRY_DISABLE_AUTO_UPLOAD="true"

# ENV vars for e2e tests
# enable e2e tests when value is true
export IS_TEST=""
# defined as secrets to run on Bitrise CI
# but have to be defined here for local tests
export MM_TEST_ACCOUNT_SRP=""
export MM_TEST_ACCOUNT_PRIVATE_KEY=""
export MM_STAKE_TEST_ACCOUNT_PRIVATE_KEY=""
export BROWSERSTACK_LOCAL=""
export BROWSERSTACK_USERNAME=""
export BROWSERSTACK_ACCESS_KEY=""

# address is the address of the first account generated from the previous SRP
export MM_TEST_ACCOUNT_ADDRESS=""


# Wallet connect project id -- should be v2 compatible
export WALLET_CONNECT_PROJECT_ID=""

# Optional: SDK Remote Communication url (for testing with your local socket server)
# export SDK_COMMLAYER_URL="http://{YOUR_LOCAL_IP}

# CDN for blockaid files
export BLOCKAID_FILE_CDN=""
export BLOCKAID_PUBLIC_KEY=""

# Default PORT for metro
export WATCHER_PORT=8081

# Environment: "production", "pre-release" or "local"
export METAMASK_ENVIRONMENT="local"

# Build type: "main" or "flask" or "beta"
export METAMASK_BUILD_TYPE="main"

# Segment SDK proxy endpoint and write key
export SEGMENT_WRITE_KEY=""
export SEGMENT_PROXY_URL=""
export SEGMENT_DELETE_API_SOURCE_ID=""
export SEGMENT_REGULATIONS_ENDPOINT=""

# Optional for dev purpose: Segment flush interval in seconds
# example for 1 second flush interval
export SEGMENT_FLUSH_INTERVAL="1"
# example for flush when 1 event is queued
export SEGMENT_FLUSH_EVENT_LIMIT="1"

# URL of the decoding API used to provide additional data from signature requests
export DECODING_API_URL: 'https://signature-insights.api.cx.metamask.io/v1'

# URL of security alerts API used to validate dApp requests.
export SECURITY_ALERTS_API_URL="https://security-alerts.api.cx.metamask.io"

# Enable Portfolio View
export PORTFOLIO_VIEW="true"


# Temporary mechanism to enable security alerts API prior to release.
export MM_SECURITY_ALERTS_API_ENABLED="true"
# Firebase
export GOOGLE_SERVICES_B64_ANDROID=""
export GOOGLE_SERVICES_B64_IOS=""
# Notifications Feature Announcements
# These are Contentful variables used to fetch feature announcements
export FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN=
export FEATURES_ANNOUNCEMENTS_SPACE_ID=

# Enables the Settings Page - Developer Options
export MM_ENABLE_SETTINGS_PAGE_DEV_OPTIONS="true"

# The endpoint used to submit errors and tracing data to Sentry for dev environment.
# export MM_SENTRY_DSN_DEV=

# Per dapp selected network feature flag
export MM_PER_DAPP_SELECTED_NETWORK=""

# Remove global network selector flag
export MM_REMOVE_GLOBAL_NETWORK_SELECTOR=""



# Permissions Settings feature flag specific to UI changes
export MM_PERMISSIONS_SETTINGS_V1_ENABLED=""

# Earn Variables
## Stablecoin Lending
export MM_STABLECOIN_LENDING_UI_ENABLED="true"
export MM_STABLE_COIN_SERVICE_INTERRUPTION_BANNER_ENABLED="true"
### Redesigned stablecoin lending
export MM_STABLECOIN_LENDING_UI_ENABLED_REDESIGNED="true"
## Pooled-Staking
export MM_POOLED_STAKING_ENABLED="true"
export MM_POOLED_STAKING_SERVICE_INTERRUPTION_BANNER_ENABLED="true"

# Activates remote feature flag override mode.
# Remote feature flag values won't be updated,
# and selectors should return their fallback values
export OVERRIDE_REMOTE_FEATURE_FLAGS="false"

#Bridge flag to toggle between dev and prod API
export BRIDGE_USE_DEV_APIS="false"

export MM_BRIDGE_ENABLED="true"
export MM_UNIFIED_SWAPS_ENABLED="true"

# Set ramps environment to staging.
# This is required when developing or checking existing ramps tests locally.
export RAMP_INTERNAL_BUILD="true"


# To enable seedless onboarding ( set to true for seedless onboarding )
export SEEDLESS_ONBOARDING_ENABLED='false'

# env for seedless onboarding main-dev
export ANDROID_APPLE_CLIENT_ID='io.metamask.appleloginclient.dev'
export ANDROID_GOOGLE_CLIENT_ID='8************-i8oeh9kuvl1n6lk1ffkobpvth27bmi41.apps.googleusercontent.com'
export ANDROID_GOOGLE_SERVER_CLIENT_ID='************-i8oeh9kuvl1n6lk1ffkobpvth27bmi41.apps.googleusercontent.com'
export IOS_GOOGLE_CLIENT_ID='************-h6tp2h3crls6hbggispcgovbvk4vabu3.apps.googleusercontent.com'
export IOS_GOOGLE_REDIRECT_URI='com.googleusercontent.apps.************-h6tp2h3crls6hbggispcgovbvk4vabu3:/oauth2redirect/google'

# env for seedless onboarding flask-dev
#export ANDROID_APPLE_CLIENT_ID="io.metamask.appleloginclient.flask.dev"
#export ANDROID_GOOGLE_CLIENT_ID="************-ab20kuqbls6fj5s50fvmvbnket8nv1sh.apps.googleusercontent.com"
#export ANDROID_GOOGLE_SERVER_CLIENT_ID="************-ab20kuqbls6fj5s50fvmvbnket8nv1sh.apps.googleusercontent.com"
#export IOS_GOOGLE_CLIENT_ID="************-89b2lmqgm5ka8j8t403qhooouv57id9b.apps.googleusercontent.com"
#export IOS_GOOGLE_REDIRECT_URI="com.googleusercontent.apps.************-89b2lmqgm5ka8j8t403qhooouv57id9b:/oauth2redirect/google"

# Enable send re-designs locally
export MM_SEND_REDESIGN_ENABLED="true"

# Preinstalled Snaps
export FORCE_PREINSTALLED_SNAPS="false"

# Enable multichain accounts (BIP-44 project)
export MM_ENABLE_MULTICHAIN_ACCOUNTS_STATE_2="false"

# Enable why-did-you-render tool for tracking re renders: https://github.com/welldone-software/why-did-you-render
export ENABLE_WHY_DID_YOU_RENDER="false"
# Rewards API URL
export REWARDS_API_URL=""

## Perps
export MM_PERPS_ENABLED="false"
export MM_PERPS_SERVICE_INTERRUPTION_BANNER_ENABLED="false"
export MM_PERPS_BLOCKED_REGIONS="US,CA-ON"
