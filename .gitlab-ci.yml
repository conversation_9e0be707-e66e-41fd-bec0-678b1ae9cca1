stages:
- unit
- integration

unitTest:
  stage: unit
  tags:
  - mac_mini
  script:
  - cd Branch-TestBed && pod install --verbose
  - cd .. &&  xcodebuild test -workspace ./Branch-TestBed/Branch-TestBed.xcworkspace -scheme Branch-SDK-Unhosted-Tests -destination 'platform=iOS Simulator,name=iPhone X,OS=12.2'
integrationTest:
  stage: integration
  tags:
  - mac_mini
  script:
  - cd Branch-TestBed && pod install --verbose
  - cd .. && xcodebuild test -workspace ./Branch-TestBed/Branch-TestBed.xcworkspace -scheme Branch-SDK-Tests -destination 'platform=iOS Simulator,name=iPhone X,OS=12.2'
