# NNX Token Price Implementation

## Overview
This implementation adds custom price fetching logic for the NNX token on the NeoNix Mainnet network. Instead of relying on external price APIs, the price is fetched directly from the NNX price contract at address `******************************************` using the `price()` function.

## Changes Made

### 1. Bridge Exchange Rates (`app/components/UI/Bridge/utils/exchange-rates.ts`)
- Added constants for NNX token address and NeoNix Mainnet chain ID
- Implemented `fetchNNXTokenPrice()` function to call the price contract
- Modified `fetchTokenExchangeRates()` to handle NNX token specially on NeoNix Mainnet
- Modified `getTokenExchangeRate()` to handle single NNX token requests

### 2. Simulation Balance Changes (`app/components/UI/SimulationDetails/useBalanceChanges.ts`)
- Added the same NNX price fetching logic
- Modified the local `fetchTokenExchangeRates()` function to handle NNX token

### 3. Swaps Fiat Conversion Rates (`app/components/UI/Swaps/utils/useFiatConversionRates.ts`)
- Added NNX price fetching logic
- Modified the main hook to handle NNX token specially

### 4. Test Coverage (`app/components/UI/Bridge/utils/exchange-rates.test.ts`)
- Added comprehensive tests for NNX token price fetching
- Tests cover success cases, error handling, and fallback scenarios

## Technical Details

### Contract Interaction
- **Contract Address**: `******************************************`
- **Function**: `price()` 
- **Function Signature**: `0xa035b1fe`
- **Return Type**: `uint256` (18 decimal places)
- **Network**: NeoNix Mainnet (Chain ID: `0xf1ac2`)

### Price Fetching Logic
1. Check if the request is for NNX token on NeoNix Mainnet
2. If yes, call the price contract using EthQuery
3. Convert the returned value from wei (18 decimals) to a standard number
4. For other tokens or networks, use the standard price fetching mechanism

### Error Handling
- Gracefully handles network not found scenarios
- Handles contract call failures
- Falls back to undefined price if contract call fails
- Logs warnings for debugging purposes

## Integration Points

The implementation integrates with existing price fetching mechanisms:

1. **Bridge Operations**: Automatically uses custom NNX pricing when bridging NNX tokens
2. **Transaction Simulations**: Shows accurate NNX token values in transaction previews
3. **Swaps**: Handles NNX token pricing in swap operations
4. **Redux Actions**: The existing `setSourceTokenExchangeRate` and `setDestTokenExchangeRate` actions automatically benefit from the custom logic

## Usage

The implementation is transparent to the rest of the application. When any component requests the price of NNX token on NeoNix Mainnet, it will automatically:

1. Detect the NNX token and NeoNix Mainnet combination
2. Call the price contract instead of external APIs
3. Return the price in the same format as other tokens

## Testing

Run the tests with:
```bash
npm test app/components/UI/Bridge/utils/exchange-rates.test.ts
```

The tests cover:
- Successful price fetching from contract
- Error handling for contract failures
- Network not found scenarios
- Standard fetching for non-NNX tokens
- Standard fetching for other networks

## Future Considerations

1. **Currency Conversion**: Currently assumes the contract returns USD prices. May need additional conversion logic for other currencies.
2. **Caching**: Consider implementing price caching to reduce contract calls.
3. **Monitoring**: Add metrics to monitor the success rate of contract calls.
4. **Fallback**: Consider implementing a fallback to external APIs if contract calls consistently fail.
