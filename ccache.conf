# Customize Ccache settings to optimize for current use cases:

# Allow Ccache to be more lenient in its handling of certain features
export CCACHE_SLOPPINESS=clang_index_store,file_stat_matches,include_file_ctime,include_file_mtime,ivfsoverlay,pch_defines,modules,system_headers,time_macros

# Enable file cloning to improve cache efficiency
export CCACHE_FILECLONE=true

# Enable dependency tracking to ensure accurate caching
export CCACHE_DEPEND=true

# Enable inode caching for faster cache lookups
export CCACHE_INODECACHE=true

# Use content-based compiler checking for cache validity
export CCACHE_COMPILERCHECK=content

# Enable hardlinking to improve cache efficiency
export CCACHE_HARDLINK=true