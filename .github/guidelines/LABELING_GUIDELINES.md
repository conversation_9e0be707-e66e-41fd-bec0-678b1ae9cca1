# PR Labeling Guidelines

To maintain a consistent and efficient development workflow, we have set specific label guidelines for all pull requests (PRs). Please ensure you adhere to the following instructions:

## Mandatory team label (automated)

- **Internal Developers**: Every PR raised by an internal developer must include a label prefixed with `team-` (e.g., `team-mobile-ux`, `team-mobile-platform`, etc.). This indicates the respective internal team responsible for the PR.

- **External Contributors**: PRs submitted by contributors who are not part of the organization will be automatically labeled with `external-contributor`.

## Mandatory checklog entry or label (manual addition)

The releases changelog generation is based on PR's changelog entries.
The check can be bypassed when necessary, either by setting the changelog entry to "null" or by adding following label:

- **no-changelog**: PR's changes do not need to be included in the changelog.

## Optional labels (manual addition)

 Any label can be manually added on demand depending on the PR's content. For instance, the label **QA passed** will indicate that a thorough manual testing has been performed and the PR is ready to be merged. In addition, following labels have some specific use cases.

### Run iOS E2E tests

Android E2E smoke tests run on every PR automatically, but running iOS E2E smoke tests are optional based on the following label:

- **run-ios-e2e-smoke**: The iOS E2E smoke tests jobs will run in the pipeline.

### Bypass Quality Gates

Using any of these labels should be exceptional in case of CI friction and urgencies. Please use them reasonably and verify new changes and regressions manually.

- **skip-sonar-cloud**: The PR will be merged without running SonarCloud checks.
- **skip-e2e**: The PR will be merged without running E2E tests.

### Block merge if any is present

- **needs-qa**: The PR requires a full manual QA prior to being merged and added to a release.
- **need-ux-ds-review**: The PR requires a review from the User Experience or Design System teams.
- **blocked**: There are unresolved dependencies or other issues blocking the progress of this PR.
- **stale**: The PR has not had recent activity in the last 90 days. It will be closed in 7 days.
- **DO-NOT-MERGE**: The PR should not be merged under any circumstances.

## Release version label (automated)

- **release-x.y.z**: This label is automatically added to a PR and its linked issues upon the PR's merge. The `x.y.z` in the label represents the version in which the changes from the PR will be included. This label is auto-generated by a [GitHub action](../workflows/add-release-label.yml), which determines the version by incrementing the minor version number from the most recent release. Manual intervention is only required in specific cases. For instance, if a merged PR is cherry-picked into a release branch, typically done to address Release Candidate (RC) bugs, the label would need to be manually updated to reflect the correct version.

To maintain code quality and project integrity, it's crucial to respect these label guidelines. Please ensure you review and update labels appropriately throughout the PR lifecycle.

Thank you for your cooperation!
