name: Cherry Pick Commit

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Target branch name to merge the cherry-picked branch into'
        required: true
      commit_hash:
        description: 'Commit Hash'
        required: true
      PR_number:
        description: 'PR # Associated with Cherry Pick'
        required: true
jobs:
  cherry-pick:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ vars.branch_name }}
          token: ${{ secrets.BUG_REPORT_TOKEN }}
      - name: Get Node.js version
        id: nvm
        run: echo "NODE_VERSION=$(cat .nvmrc)" >> "$GITHUB_OUTPUT"
      - uses: actions/setup-node@v3
        with:
          node-version: ${{ steps.nvm.outputs.NODE_VERSION }}
      - name: Create Cherry Pick PR
        id: create-cherry-pick-pr
        shell: bash
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          ./scripts/create-cherry-pick-pr.sh ${{ github.event.inputs.branch_name }} ${{ github.event.inputs.commit_hash }} ${{ github.event.inputs.PR_number }}
