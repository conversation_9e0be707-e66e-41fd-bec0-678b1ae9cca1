name: iOS E2E Smoke Tests

on:
  workflow_call:

permissions:
  contents: read
  id-token: write

jobs:
  confirmations-ios-smoke:
    strategy:
      matrix:
        split: [1, 2, 3]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: confirmations-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeConfirmations"
      split_number: ${{ matrix.split }}
      total_splits: 3
    secrets: inherit

  confirmations-redesigned-ios-smoke:
   strategy:
     matrix:
       split: [1, 2]
     fail-fast: false
   uses: ./.github/workflows/run-e2e-workflow.yml
   with:
     test-suite-name: confirmations-redesigned-ios-smoke-${{ matrix.split }}
     platform: ios
     test_suite_tag: "SmokeConfirmationsRedesigned"
     split_number: ${{ matrix.split }}
     total_splits: 2
   secrets: inherit

  trade-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: trade-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeTrade"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  wallet-platform-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: wallet-platform-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeWalletPlatform"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  identity-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: identity-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeIdentity"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  accounts-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: accounts-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeAccounts"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  network-abstraction-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: network-abstraction-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeNetworkAbstractions"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  network-expansion-ios-smoke:
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: network-expansion-ios-smoke-${{ matrix.split }}
      platform: ios
      test_suite_tag: "SmokeNetworkExpansion"
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit

  report-ios-smoke-tests:
    name: Report iOS Smoke Tests
    runs-on: ubuntu-latest
    if: ${{ !cancelled() }}
    needs:
      - confirmations-ios-smoke
      - confirmations-redesigned-ios-smoke
      - trade-ios-smoke
      - wallet-platform-ios-smoke
      - identity-ios-smoke
      - accounts-ios-smoke
      - network-abstraction-ios-smoke
      - network-expansion-ios-smoke

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          path: all-test-results/
          pattern: "*-ios-*-test-results"

      - name: Post Test Report
        uses: dorny/test-reporter@dc3a92680fcc15842eef52e8c4606ea7ce6bd3f3
        with:
          name: "iOS E2E Smoke Test Results"
          path: "all-test-results/**/*.xml"
          reporter: "jest-junit"
          fail-on-error: false
          list-suites: "failed"
          list-tests: "failed"

      - name: Create mobile test report
        id: create-json-report
        continue-on-error: true
        run: |
          # Create a temporary directory for xml2js to avoid conflicts
          mkdir -p temp-deps && cd temp-deps
          npm init -y
          npm install xml2js@0.5.0 --no-audit --no-fund
          
          # Copy node_modules to workspace
          cp -r node_modules ${{ github.workspace }}/
          
          # Run the mobile test report generator
          cd ${{ github.workspace }}
          TEST_RESULTS_PATH=all-test-results \
          TEST_RUNS_PATH=test/test-results/test-runs-ios.json \
          RUN_ID=${{ github.run_id }} \
          PR_NUMBER=${{ github.event.pull_request.number || '0' }} \
          GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }} \
          node .github/scripts/create-e2e-test-report.mjs
          
          # Clean up temporary node_modules
          rm -rf node_modules

      - name: Upload test runs JSON
        if: steps.create-json-report.outcome == 'success'
        uses: actions/upload-artifact@v4
        with:
          name: test-e2e-ios-report
          path: test/test-results/test-runs-ios.json

      - name: Upload merged XML report
        uses: actions/upload-artifact@v4
        with:
          name: ios-merged-test-report
          path: all-test-results/**/*.xml
