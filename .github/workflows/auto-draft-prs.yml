name: Auto Draft

on:
  pull_request:
    types: [opened]
    branches:
    - main

permissions:
    pull-requests: write
    contents: read
    issues: write

jobs:
  process_pr:
    runs-on: ubuntu-latest
    steps:
      - name: Convert PR to Draft and Add Label
        uses: actions/github-script@v6
        with:
          script: |
            // Convert PR to draft
            await github.rest.pulls.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              draft: true
            });