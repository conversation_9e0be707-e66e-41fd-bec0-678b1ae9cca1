name: "Check PR has required labels"
on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - reopened
      - synchronize
      - labeled
      - unlabeled

jobs:
  check-pr-labels:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
            fetch-depth: 1 # This retrieves only the latest commit.

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Check PR has required labels
        id: check-pr-has-required-labels
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: yarn run check-pr-has-required-labels
        working-directory: '.github/scripts'
