name: Check template and add labels

on:
  issues:
    types: [opened, edited, labeled, unlabeled, reopened]
  pull_request_target:
    types: [opened, edited, labeled, unlabeled, reopened]

jobs:
  check-template-and-add-labels:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 1 # This retrieves only the latest commit.

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Check template and add labels
        id: check-template-and-add-labels
        env:
          LABEL_TOKEN: ${{ secrets.LABEL_TOKEN }}
        run: npm run check-template-and-add-labels
        working-directory: '.github/scripts'
