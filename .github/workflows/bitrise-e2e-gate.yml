name: Run Bitrise E2E Gate

on:
  pull_request:
    types: [labeled, synchronize]
  merge_group: # Trigger on merge queue events to satisfy the branch protection rules
    types: [checks_requested]

env:
  E2E_LABEL: 'run-ios-e2e-smoke'
  E2E_PIPELINE: 'pr_smoke_e2e_pipeline'
  WORKFLOW_NAME: 'run-bitrise-e2e-check'

jobs:
  run-bitrise-e2e-gate:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
      checks: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Check Bitrise E2E Gate
        env:
          BITRISE_BUILD_TRIGGER_TOKEN: ${{ secrets.BITRISE_BUILD_TRIGGER_TOKEN }}
          BITRISE_APP_ID: ${{ secrets.BITRISE_APP_ID }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: yarn run bitrise-results-check
        working-directory: '.github/scripts'

