name: Android E2E Regression Tests

on:
  workflow_call:
  schedule:
    - cron: '30 4 * * *'  # 4:30 AM UTC
  workflow_dispatch:

permissions:
  contents: read
  id-token: write
  actions: read

jobs:
  build-android-apks:
    name: 'Build Android APKs'
    if: ${{ github.event_name != 'merge_group' }}
    permissions:
      contents: read
      id-token: write
    uses: ./.github/workflows/build-android-e2e.yml
    secrets: inherit

  regression-confirmations-android:
    name: 'Confirmations Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-confirmations-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionConfirmations'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-trade-android:
    name: 'Trade Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-trade-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionTrade'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-wallet-platform-android:
    name: 'Wallet Platform Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-wallet-platform-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionWalletPlatform'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-identity-android:
    name: 'Identity Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-identity-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionIdentity'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-accounts-android:
    name: 'Accounts Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-accounts-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionAccounts'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-network-abstraction-android:
    name: 'Network Abstraction Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-network-abstraction-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionNetworkAbstractions'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-network-expansion-android:
    name: 'Network Expansion Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2, 3, 4]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-network-expansion-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionNetworkExpansion'
      split_number: ${{ matrix.split }}
      total_splits: 4
    secrets: inherit

  regression-assets-android:
    name: 'Assets Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1, 2]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-assets-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionAssets'
      split_number: ${{ matrix.split }}
      total_splits: 2
    secrets: inherit
  
  regression-ux-android:
    name: 'UX Regression (Android) - ${{ matrix.split }}'
    needs: [build-android-apks]
    strategy:
      matrix:
        split: [1]
      fail-fast: false
    uses: ./.github/workflows/run-e2e-workflow.yml
    with:
      test-suite-name: regression-ux-android-${{ matrix.split }}
      platform: android
      test_suite_tag: 'RegressionWalletUX'
      split_number: ${{ matrix.split }}
      total_splits: 1
    secrets: inherit

  report-android-regression-tests:
    name: Report Android Regression Tests
    runs-on: ubuntu-latest
    if: always()
    needs:
      - regression-confirmations-android
      - regression-trade-android
      - regression-wallet-platform-android
      - regression-identity-android
      - regression-accounts-android
      - regression-network-abstraction-android
      - regression-network-expansion-android
      - regression-assets-android
      - regression-ux-android

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Download all test results
        uses: actions/download-artifact@v4
        continue-on-error: true
        with:
          path: all-test-results/
          pattern: '*-android-*-test-results'

      - name: Post Test Report
        uses: dorny/test-reporter@dc3a92680fcc15842eef52e8c4606ea7ce6bd3f3
        with:
          name: 'Android E2E Regression Test Results'
          path: 'all-test-results/**/*.xml'
          reporter: 'jest-junit'
          fail-on-error: false
          list-suites: 'failed'
          list-tests: 'failed'

      - name: Upload merged report
        uses: actions/upload-artifact@v4
        continue-on-error: true
        with:
          name: android-merged-test-report
          path: all-test-results/**/*.xml