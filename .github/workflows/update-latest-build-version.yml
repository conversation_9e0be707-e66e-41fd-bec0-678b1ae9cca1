##############################################################################################
#
# This Workflow is responsible for updating the latest build version of the project.
# You can provide your own base branch, tag, or SHA for git operations and the pull request.
# and it will generate the latest build version & update the neccessary files for you.
#
##############################################################################################
name: Update Latest Build Version

on:
  workflow_dispatch:
    inputs:
      base-branch:
        description: 'The base branch, tag, or SHA for git operations and the pull request.'
        required: true
jobs:
  generate-build-version:
    uses: MetaMask/metamask-mobile-build-version/.github/workflows/metamask-mobile-build-version.yml@v0.2.0
    permissions:
      id-token: write

  bump-version:
    runs-on: ubuntu-latest
    needs: generate-build-version
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ inputs.base-branch }}
          token: ${{ secrets.PR_TOKEN }}

      - name: Bump script
        env:
          HEAD_REF: ${{ inputs.base-branch }}
        run: |
          ./scripts/set-build-version.sh ${{ needs.generate-build-version.outputs.build-version }}
          git diff
          git config user.name metamaskbot
          git config user.email <EMAIL>
          git add bitrise.yml
          git add package.json
          git add ios/MetaMask.xcodeproj/project.pbxproj
          git add android/app/build.gradle
          git commit -m "[skip ci] Bump version number to ${{ needs.generate-build-version.outputs.build-version }}"
          git push origin HEAD:"$HEAD_REF" --force-with-lease
