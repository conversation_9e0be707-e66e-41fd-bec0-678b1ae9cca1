name: Build iOS E2E Apps

on:
  workflow_call:

permissions:
  contents: read
  id-token: write

jobs:
  build-ios-apps:
    name: Build iOS E2E Apps
    runs-on: ghcr.io/cirruslabs/macos-runner:sequoia-xl
    outputs:
      artifacts-url: ${{ steps.set-artifacts-url.outputs.artifacts-url }}
      app-uploaded: ${{ steps.upload-app.outcome == 'success' }}
      sourcemap-uploaded: ${{ steps.upload-sourcemap.outcome == 'success' }}
    env:
      RCT_NO_LAUNCH_PACKAGER: 1
      XCODE_BUILD_SETTINGS: "COMPILER_INDEX_STORE_ENABLE=NO"
      GITHUB_CI: "true"  # This ensures it's available during pod install
      PLATFORM: ios
      METAMASK_ENVIRONMENT: qa
      METAMASK_BUILD_TYPE: main
      IS_TEST: true
      E2E: "true"
      IGNORE_BOXLOGS_DEVELOPMENT: true
      CI: "true"
      NODE_OPTIONS: "--max-old-space-size=8192"
      MM_UNIFIED_SWAPS_ENABLED: "true"
      MM_BRIDGE_ENABLED: "true"
      BRIDGE_USE_DEV_APIS: "true"
      RAMP_INTERNAL_BUILD: "true"
      SEEDLESS_ONBOARDING_ENABLED: "true"
      MM_REMOVE_GLOBAL_NETWORK_SELECTOR: "true"
      MM_NOTIFICATIONS_UI_ENABLED: "true"
      MM_SECURITY_ALERTS_API_ENABLED: "true"
      BLOCKAID_FILE_CDN: "static.cx.metamask.io/api/v1/confirmations/ppom"
      FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN: ${{ secrets.FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN }}
      FEATURES_ANNOUNCEMENTS_SPACE_ID: ${{ secrets.FEATURES_ANNOUNCEMENTS_SPACE_ID }}
      SEGMENT_WRITE_KEY_QA: ${{ secrets.SEGMENT_WRITE_KEY_QA }}
      SEGMENT_PROXY_URL_QA: ${{ secrets.SEGMENT_PROXY_URL_QA }}
      SEGMENT_DELETE_API_SOURCE_ID_QA: ${{ secrets.SEGMENT_DELETE_API_SOURCE_ID_QA }}
      SEGMENT_REGULATIONS_ENDPOINT_QA: ${{ secrets.SEGMENT_REGULATIONS_ENDPOINT_QA }}
      MM_SENTRY_DSN_TEST: ${{ secrets.MM_SENTRY_DSN_TEST }}
      MM_SENTRY_AUTH_TOKEN: ${{ secrets.MM_SENTRY_AUTH_TOKEN }}
      MAIN_IOS_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_IOS_GOOGLE_CLIENT_ID_UAT }}
      MAIN_IOS_GOOGLE_REDIRECT_URI_UAT: ${{ secrets.MAIN_IOS_GOOGLE_REDIRECT_URI_UAT }}
      MAIN_ANDROID_APPLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_APPLE_CLIENT_ID_UAT }}
      MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT }}
      MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT }}
      GOOGLE_SERVICES_B64_IOS: ${{ secrets.GOOGLE_SERVICES_B64_IOS }}
      GOOGLE_SERVICES_B64_ANDROID: ${{ secrets.GOOGLE_SERVICES_B64_ANDROID }}
      MM_INFURA_PROJECT_ID: ${{ secrets.MM_INFURA_PROJECT_ID }}

    steps:
      # Get the source code from the repository
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Cache Xcode derived data
        uses: cirruslabs/cache@0ea6c28a9b52ff2a1a01354742d8fbe0c4599693
        with:
          path: |
            ~/Library/Developer/Xcode/DerivedData
            ios/build
          key: ${{ runner.os }}-xcode-${{ hashFiles('ios/**/*.{h,m,mm,swift}', 'ios/**/Podfile.lock', 'yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-xcode-

      # Install Node.js, Xcode tools, and other iOS development dependencies
      - name: Installing iOS Environment Setup
        uses: MetaMask/github-tools/.github/actions/setup-e2e-env@self-hosted-runners-config
        with:
          platform: ios
          setup-simulator: false

      - name: Print iOS tool versions
        run: |
            echo "🔧 Node.js Version:"
            node -v || echo "Node not found"
            echo "🧶 Yarn Version:"
            yarn -v || echo "Yarn not found"
            echo "📦 CocoaPods Version:"
            pod --version || echo "CocoaPods not found"
            echo "🛠️ Xcode Path:"
            xcode-select -p || echo "Xcode not found"
            echo "📱 Booted iOS Simulators:"
            xcrun simctl list | grep Booted || echo "No booted simulators found"
        shell: bash

      # Clean iOS plist files to prevent extended attribute issues
      - name: Clean iOS plist files
        run: find ios -name "*.plist" -exec xattr -c {} \;

      # Run project setup with retry for better resilience
      - name: Setup project dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: |
            echo "🚀 Setting up project..."
            yarn setup:github-ci --build-ios --no-build-android
      
      # Build the iOS E2E app for simulator
      - name: Build iOS E2E App
        run: |
          echo "🏗 Building iOS E2E App..."
          export NODE_OPTIONS="--max-old-space-size=8192"
          yarn build:ios:main:e2e
        shell: bash
        env:
          PLATFORM: ios
          METAMASK_ENVIRONMENT: main
          METAMASK_BUILD_TYPE: main
          IS_TEST: true
          IS_SIM_BUILD: "true"  # Ensures simulator build (.app) not device build (.ipa)
          IGNORE_BOXLOGS_DEVELOPMENT: true
          GITHUB_CI: "true"
          CI: "true"

          NODE_OPTIONS: "--max_old_space_size=4096" # Increase memory limit for build

          SEGMENT_WRITE_KEY_QA: ${{ secrets.SEGMENT_WRITE_KEY_QA }}
          SEGMENT_PROXY_URL_QA: ${{ secrets.SEGMENT_PROXY_URL_QA }}
          SEGMENT_DELETE_API_SOURCE_ID_QA: ${{ secrets.SEGMENT_DELETE_API_SOURCE_ID_QA }}
          SEGMENT_REGULATIONS_ENDPOINT_QA: ${{ secrets.SEGMENT_REGULATIONS_ENDPOINT_QA }}

          MM_SENTRY_DSN_TEST: ${{ secrets.MM_SENTRY_DSN_TEST }}
          MM_SENTRY_AUTH_TOKEN: ${{ secrets.MM_SENTRY_AUTH_TOKEN }}

          MAIN_IOS_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_IOS_GOOGLE_CLIENT_ID_UAT }}
          MAIN_IOS_GOOGLE_REDIRECT_URI_UAT: ${{ secrets.MAIN_IOS_GOOGLE_REDIRECT_URI_UAT }}
          MAIN_ANDROID_APPLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_APPLE_CLIENT_ID_UAT }}
          MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT }}
          MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT }}
          GOOGLE_SERVICES_B64_IOS: ${{ secrets.GOOGLE_SERVICES_B64_IOS }}
          GOOGLE_SERVICES_B64_ANDROID: ${{ secrets.GOOGLE_SERVICES_B64_ANDROID }}

      # Upload the iOS .app file that works in simulators
      - name: Upload iOS APP Artifact (Simulator)
        id: upload-app
        uses: actions/upload-artifact@v4
        with:
          name: MetaMask.app
          path: ios/build/Build/Products/Release-iphonesimulator/MetaMask.app
          retention-days: 7
          if-no-files-found: error
        continue-on-error: true

      # Upload source map file for crash debugging and error tracking if exists
      - name: Upload iOS Source Map
        id: upload-sourcemap
        uses: actions/upload-artifact@v4
        with:
          name: index.js.map
          path: sourcemaps/ios/index.js.map
          retention-days: 7
          if-no-files-found: error
        continue-on-error: true

      # Generate artifact download URL and display upload status summary
      - name: Set Artifacts URL and Status
        id: set-artifacts-url
        run: |
          ARTIFACTS_URL="https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
          echo "artifacts-url=${ARTIFACTS_URL}" >> "$GITHUB_OUTPUT"
          echo "📦 Artifacts available at: ${ARTIFACTS_URL}"
          echo ""
          echo "Upload Status Summary:"
          echo "- APP (Simulator): ${{ steps.upload-app.outcome }}"
          echo "- Source Map: ${{ steps.upload-sourcemap.outcome }}"

        env:
          GITHUB_REPOSITORY: "${{ github.repository }}"
          GITHUB_RUN_ID: "${{ github.run_id }}"
