name: Close release bug report issue when release branch gets merged

on:
  pull_request:
    branches:
      - stable
    types:
      - closed
    
jobs:
  close-bug-report:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true && startsWith(github.event.pull_request.head.ref, 'release/')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
            fetch-depth: 1 # This retrieves only the latest commit.

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Close release bug report issue
        id: close-release-bug-report-issue
        env:
          BUG_REPORT_REPO: mobile-planning
          BUG_REPORT_TOKEN: ${{ secrets.BUG_REPORT_TOKEN }}
        run: yarn run close-release-bug-report-issue
        working-directory: '.github/scripts'
