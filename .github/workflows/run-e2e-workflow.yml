# This workflow runs mobile E2E tests for a specific test category.
# It passes matrix sharding info to the test framework via environment variables.

name: Run E2E

on:
  workflow_call:
    inputs:
      test-suite-name:
        description: 'Name of the test suite'
        required: true
        type: string
      platform:
        description: 'Platform to test (ios or android)'
        required: true
        type: string
      test_suite_tag:
        description: 'The Cucumber tag expression to use for filtering tests'
        required: true
        type: string
      split_number:
        description: 'Which split number to run (1-based index)'
        required: false
        type: number
        default: 1
      total_splits:
        description: 'Total number of splits to divide tests into'
        required: false
        type: number
        default: 1
      test-timeout-minutes:
        description: 'The timeout in minutes for the test command'
        required: false
        type: number
        default: 30

jobs:
  test-e2e-mobile:
    name: ${{ inputs.test-suite-name }}
    runs-on: ${{ inputs.platform == 'ios' && fromJSON('["ghcr.io/cirruslabs/macos-runner:sequoia", "low-priority"]') || 'gha-mmsdk-scale-set-ubuntu-22.04-amd64-xl' }}
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}-${{ inputs.platform }}-${{ inputs.test-suite-name }}-${{ inputs.split_number }}
      cancel-in-progress: ${{ !(contains(github.ref, 'refs/heads/main') || contains(github.ref, 'refs/heads/stable')) }}

    env:
      PREBUILT_IOS_APP_PATH: artifacts/MetaMask.app
      METAMASK_ENVIRONMENT: 'qa'
      METAMASK_BUILD_TYPE: 'main'
      TEST_SUITE_TAG: ${{ inputs.test_suite_tag }}
      GITHUB_CI: 'true'
      SPLIT_NUMBER: ${{ inputs.split_number }}
      TOTAL_SPLITS: ${{ inputs.total_splits }}
      MM_UNIFIED_SWAPS_ENABLED: 'true'
      RAMP_INTERNAL_BUILD: 'true'
      MM_BRIDGE_ENABLED: 'true'
      BRIDGE_USE_DEV_APIS: 'true'
      MM_SECURITY_ALERTS_API_ENABLED: 'true'
      MM_NOTIFICATIONS_UI_ENABLED: 'true'
      FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN: ${{ secrets.FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN }}
      FEATURES_ANNOUNCEMENTS_SPACE_ID: ${{ secrets.FEATURES_ANNOUNCEMENTS_SPACE_ID }}
      MM_TEST_WALLET_SRP: ${{ secrets.MM_TEST_WALLET_SRP }}
      SEEDLESS_ONBOARDING_ENABLED: 'true'
      MM_REMOVE_GLOBAL_NETWORK_SELECTOR: 'true'
      SEGMENT_WRITE_KEY_QA: ${{ secrets.SEGMENT_WRITE_KEY_QA }}
      SEGMENT_PROXY_URL_QA: ${{ secrets.SEGMENT_PROXY_URL_QA }}
      SEGMENT_DELETE_API_SOURCE_ID_QA: ${{ secrets.SEGMENT_DELETE_API_SOURCE_ID_QA }}
      MAIN_IOS_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_IOS_GOOGLE_CLIENT_ID_UAT }}
      MAIN_IOS_GOOGLE_REDIRECT_URI_UAT: ${{ secrets.MAIN_IOS_GOOGLE_REDIRECT_URI_UAT }}
      MAIN_ANDROID_APPLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_APPLE_CLIENT_ID_UAT }}
      MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT }}
      MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT }}
      SEGMENT_REGULATIONS_ENDPOINT_QA: ${{ secrets.SEGMENT_REGULATIONS_ENDPOINT_QA }}
      MM_SENTRY_DSN_TEST: ${{ secrets.MM_SENTRY_DSN_TEST }}
      MM_SENTRY_AUTH_TOKEN: ${{ secrets.MM_SENTRY_AUTH_TOKEN }}
      GOOGLE_SERVICES_B64_IOS: ${{ secrets.GOOGLE_SERVICES_B64_IOS }}
      GOOGLE_SERVICES_B64_ANDROID: ${{ secrets.GOOGLE_SERVICES_B64_ANDROID }}
      MM_SOLANA_E2E_TEST_SRP: ${{ secrets.MM_SOLANA_E2E_TEST_SRP }}
      MM_INFURA_PROJECT_ID: ${{ secrets.MM_INFURA_PROJECT_ID }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'merge_group' && github.event.merge_group.head_sha || github.event.pull_request.head.sha || github.sha }}
          clean: true
          fetch-depth: 0

      - name: Set up E2E environment
        uses: MetaMask/github-tools/.github/actions/setup-e2e-env@self-hosted-runners-config 
        with:
          platform: ${{ inputs.platform }}
          setup-simulator: ${{ inputs.platform == 'ios' }}
          android-avd-name: emulator
          configure-keystores: false

      - name: Build Detox framework cache (iOS)
        if: ${{ inputs.platform == 'ios' }}
        run: |
          echo "Building Detox framework cache for iOS..."
          yarn detox clean-framework-cache
          yarn detox build-framework-cache

      - name: Setup Android artifacts from build job
        if: ${{ inputs.platform == 'android' }}
        run: |
          echo "🏗 Setting up Android artifacts from build job..."
          
          # Create required directories
          mkdir -p android/app/build/outputs/apk/prod/release/

      - name: Download Android build artifacts
        if: ${{ inputs.platform == 'android' }}
        uses: actions/download-artifact@v4
        with:
          path: artifacts/

      - name: Move Android artifacts to expected locations
        if: ${{ inputs.platform == 'android' }}
        run: |
          # Move main APK
          if [[ -f "artifacts/app-prod-release.apk/app-prod-release.apk" ]]; then
            mkdir -p "android/app/build/outputs/apk/prod/release/"
            cp "artifacts/app-prod-release.apk/app-prod-release.apk" "android/app/build/outputs/apk/prod/release/"
            echo "✅ Android main APK ready for E2E tests"
          else
            echo "❌ Android main APK not found"
            ls -la artifacts/
            exit 1
          fi

          # Move test APK
          if [[ -f "artifacts/app-prod-release-androidTest.apk/app-prod-release-androidTest.apk" ]]; then
            mkdir -p "android/app/build/outputs/apk/androidTest/prod/release/"
            cp "artifacts/app-prod-release-androidTest.apk/app-prod-release-androidTest.apk" "android/app/build/outputs/apk/androidTest/prod/release/"
            echo "✅ Android test APK ready for E2E tests"
          else
            echo "❌ Android test APK not found"
            ls -la artifacts/
            exit 1
          fi

      - name: Setup iOS artifacts from build job
        if: ${{ inputs.platform == 'ios' }}
        run: |
          echo "🏗 Setting up iOS artifacts from build job..."
          
          # Create required directories
          mkdir -p ios/build/Build/Products/Release-iphonesimulator/

      - name: Download iOS build artifacts
        if: ${{ inputs.platform == 'ios' }}
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
      - name: Clean environment before tests (iOS only)
        if: ${{ inputs.platform == 'ios' }}
        run: |
          echo "🧹 Cleaning iOS environment before E2E tests..."

          # Clean up lock files (iOS-specific issue)
          find . -name "*.lock" -type f -delete 2>/dev/null || true

          # Reset iOS simulator
          xcrun simctl shutdown all 2>/dev/null || true
          xcrun simctl erase all 2>/dev/null || true

          # Clean any hanging processes
          pkill -f "Metro\|node\|npm" 2>/dev/null || true

          echo "✅ iOS environment cleaned"

      - name: Run E2E tests
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: ${{ inputs.test-timeout-minutes }}
          max_attempts: 3
          retry_wait_seconds: 30
          command: |
            platform="${{ inputs.platform }}"
            test_suite_tag="${{ inputs.test_suite_tag }}"

            echo "🚀 Running ${{ inputs.test-suite-name }} tests on $platform"

            # Validate required test suite tag
            if [[ -z "$test_suite_tag" ]]; then
              echo "❌ Error: test_suite_tag is required for non-api-specs tests"
              exit 1
            fi

            export TEST_SUITE_TAG="$test_suite_tag"
            echo "Using TEST_SUITE_TAG: $TEST_SUITE_TAG"

            # Run tests (Detox/Jest handle retries internally)
            echo "🚀 Starting E2E tests..."
            if [[ "$platform" == "ios" ]]; then
              export BITRISE_TRIGGERED_WORKFLOW_ID="ios_workflow"
            else
              export BITRISE_TRIGGERED_WORKFLOW_ID="android_workflow"
            fi
            
            # Always use the splitting script (handles both split and non-split cases)
            echo "Running split ${{ inputs.split_number }} of ${{ inputs.total_splits }}"

            ./scripts/run-e2e-tags-gha.sh
            
            echo "✅ Test execution completed"
        env:
          JOB_NAME: ${{ inputs.test-suite-name }}
          RUN_ID: ${{ github.run_id }}
          PR_NUMBER: ${{ github.event.pull_request.number || '' }}

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.test-suite-name }}-test-results
          path: e2e/reports/
          retention-days: 7
      
      - name: Prepare screenshots
        if: failure()
        run: |
          echo "🔎 Pruning artifacts while preserving platform Detox artifacts and debug assets..."

          case "${{ inputs.platform }}" in
            android)
              KEEP_PREFIX="android"
              ;;
            ios)
              KEEP_PREFIX="ios"
              ;;
            *)
              echo "⚠️ Unknown platform '${{ inputs.platform }}'; skipping pruning to avoid accidental deletion."
              exit 0
              ;;
          esac

          # Remove all artifacts except the ones that match the keep prefix. We should later on improve this to avoid having rm -rf references.
          find artifacts -mindepth 1 -prune \
            ! -name "${KEEP_PREFIX}*" \
            ! -name 'coverage-*' \
            -exec echo "Removing:" {} \; \
            -exec rm -rf {} + 2>/dev/null || true
        continue-on-error: true

      - name: Upload screenshots
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.test-suite-name }}-screenshots
          path: artifacts/
          retention-days: 7
