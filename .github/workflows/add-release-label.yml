name: Add release label to PR and linked issues when PR gets merged

on:
  pull_request:
    branches:
      - main
    types:
      - closed

jobs:
  add-release-label:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
            fetch-depth: 0 # This is needed to checkout all branches

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Get the next semver version
        id: get-next-semver-version
        env:
          FORCE_NEXT_SEMVER_VERSION: ${{ vars.FORCE_NEXT_SEMVER_VERSION }}
        run: ./get-next-semver-version.sh "$FORCE_NEXT_SEMVER_VERSION"
        working-directory: '.github/scripts'

      - name: Add release label to PR and linked issues
        id: add-release-label-to-pr-and-linked-issues
        env:
          RELEASE_LABEL_TOKEN: ${{ secrets.RELEASE_LABEL_TOKEN }}
          NEXT_SEMVER_VERSION: ${{ env.NEXT_SEMVER_VERSION }}
        run: yarn run add-release-label-to-pr-and-linked-issues
        working-directory: '.github/scripts'
