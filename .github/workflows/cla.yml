name: "CLA Signature Bot"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]
  merge_group:
    types: [checks_requested]
    
jobs:
  CLABot:
    if: github.event_name == 'pull_request_target' || contains(github.event.comment.html_url, '/pull/')
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - name: "CLA Signature Bot"
        uses: MetaMask/cla-signature-bot@v3.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          path-to-signatures: 'cla.json'
          url-to-cladocument: 'https://metamask.io/cla.html'
          # This branch can't have protections, commits are made directly to the specified branch.
          branch: 'cla-signatures'
          allowlist: 'dependabot[bot],metamaskbot,crowdin-bot,runway-github[bot],cursorbot'
          allow-organization-members: true
          blockchain-storage-flag: false
