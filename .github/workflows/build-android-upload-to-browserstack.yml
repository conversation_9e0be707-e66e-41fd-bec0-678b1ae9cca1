# This workflow creates two non E2E Android Builds and uploads to BrowserStack
# Build 1: With ADDITIONAL_SRP_1 and PREDEFINED_PASSWORD (pre-imported wallet)
# Build 2: Without these environment variables (fresh wallet)

name: Build Android Dual Versions and Upload to Browser<PERSON><PERSON>ck

on:
  workflow_call:
    outputs:
      with-srp-browserstack-url:
        description: 'BrowserStack URL for with-SRP version'
        value: ${{ jobs.upload-to-browserstack.outputs.with-srp-browserstack-url }}
      without-srp-browserstack-url:
        description: 'BrowserStack URL for without-SRP version'
        value: ${{ jobs.upload-to-browserstack.outputs.without-srp-browserstack-url }}
      with-srp-app-id:
        description: 'BrowserStack App ID for with-SRP version'
        value: ${{ jobs.upload-to-browserstack.outputs.with-srp-app-id }}
      without-srp-app-id:
        description: 'BrowserStack App ID for without-SRP version'
        value: ${{ jobs.upload-to-browserstack.outputs.without-srp-app-id }}
      with-srp-version:
        description: 'App version for with-SRP build'
        value: ${{ jobs.upload-to-browserstack.outputs.with-srp-version }}
      without-srp-version:
        description: 'App version for without-SRP build'
        value: ${{ jobs.upload-to-browserstack.outputs.without-srp-version }}
  workflow_dispatch:
    inputs:
      description:
        description: 'Optional description for this build run'
        required: false
        type: string

permissions:
  contents: read
  id-token: write

env:
  BROWSERSTACK_USERNAME: ${{ secrets.BROWSERSTACK_USERNAME }}
  BROWSERSTACK_ACCESS_KEY: ${{ secrets.BROWSERSTACK_ACCESS_KEY }}

jobs:
  build-android-dual:
    name: Build Android Dual Versions
    runs-on: gha-mmsdk-scale-set-ubuntu-22.04-amd64-xl
    env:
      GRADLE_USER_HOME: /home/<USER>/_work/.gradle
    # No outputs - these will be provided by the collect-results job
    
    strategy:
      fail-fast: false
      matrix:
        build_type: [with-srp, without-srp]
    
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup Android Build Environment
        uses: MetaMask/github-tools/.github/actions/setup-e2e-env@self-hosted-runners-config
        with:
          platform: android
          setup-simulator: false
          configure-keystores: true
          target: qa

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            /home/<USER>/_work/.gradle/caches
            /home/<USER>/_work/.gradle/wrapper
            android/.gradle
          key: gradle-${{ runner.os }}-${{ matrix.build_type }}-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            gradle-${{ runner.os }}-${{ matrix.build_type }}-
            gradle-${{ runner.os }}-

      - name: Build Android E2E APK - ${{ matrix.build_type }}
        run: |
          echo "🚀 Setting up project for ${{ matrix.build_type }} build..."
          yarn setup:github-ci --no-build-ios
          
          echo "🏗 Building Android E2E APK (${{ matrix.build_type }})..."
          export NODE_OPTIONS="--max-old-space-size=8192"
          # Use dedicated dual versions properties file for ARM architectures
          cp android/gradle.properties.github.dual-versions android/gradle.properties
          yarn build:android:main:e2e
        shell: bash
        env:
          PLATFORM: android
          METAMASK_ENVIRONMENT: qa
          METAMASK_BUILD_TYPE: main
          IS_TEST: false
          E2E: "true"
          IGNORE_BOXLOGS_DEVELOPMENT: true
          GITHUB_CI: "true"
          CI: "true"
          NODE_OPTIONS: "--max-old-space-size=8192"
          MM_UNIFIED_SWAPS_ENABLED: "true"
          MM_BRIDGE_ENABLED: "true"
          BRIDGE_USE_DEV_APIS: "true"
          RAMP_INTERNAL_BUILD: "true"
          SEEDLESS_ONBOARDING_ENABLED: "true"
          MM_NOTIFICATIONS_UI_ENABLED: "true"
          DISABLE_NOTIFICATION_PROMPT: "true"
          MM_SECURITY_ALERTS_API_ENABLED: "true"
          MM_REMOVE_GLOBAL_NETWORK_SELECTOR: "true"
          # Conditionally set SRP and password based on build type
          ADDITIONAL_SRP_1: ${{ matrix.build_type == 'with-srp' && secrets.TEST_SRP_1 || '' }}
          PREDEFINED_PASSWORD: ${{ matrix.build_type == 'with-srp' && secrets.E2E_PASSWORD || '' }}
          FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN: ${{ secrets.FEATURES_ANNOUNCEMENTS_ACCESS_TOKEN }}
          FEATURES_ANNOUNCEMENTS_SPACE_ID: ${{ secrets.FEATURES_ANNOUNCEMENTS_SPACE_ID }}
          SEGMENT_WRITE_KEY_QA: ${{ secrets.SEGMENT_WRITE_KEY_QA }}
          SEGMENT_PROXY_URL_QA: ${{ secrets.SEGMENT_PROXY_URL_QA }}
          SEGMENT_DELETE_API_SOURCE_ID_QA: ${{ secrets.SEGMENT_DELETE_API_SOURCE_ID_QA }}
          SEGMENT_REGULATIONS_ENDPOINT_QA: ${{ secrets.SEGMENT_REGULATIONS_ENDPOINT_QA }}
          MM_SENTRY_DSN_TEST: ${{ secrets.MM_SENTRY_DSN_TEST }}
          MM_SENTRY_AUTH_TOKEN: ${{ secrets.MM_SENTRY_AUTH_TOKEN }}
          MAIN_IOS_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_IOS_GOOGLE_CLIENT_ID_UAT }}
          MAIN_IOS_GOOGLE_REDIRECT_URI_UAT: ${{ secrets.MAIN_IOS_GOOGLE_REDIRECT_URI_UAT }}
          MAIN_ANDROID_APPLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_APPLE_CLIENT_ID_UAT }}
          MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_CLIENT_ID_UAT }}
          MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT: ${{ secrets.MAIN_ANDROID_GOOGLE_SERVER_CLIENT_ID_UAT }}
          GOOGLE_SERVICES_B64_IOS: ${{ secrets.GOOGLE_SERVICES_B64_IOS }}
          GOOGLE_SERVICES_B64_ANDROID: ${{ secrets.GOOGLE_SERVICES_B64_ANDROID }}
          MM_INFURA_PROJECT_ID: ${{ secrets.MM_INFURA_PROJECT_ID }}

      - name: Upload Android APK - ${{ matrix.build_type }}
        id: upload-apk
        uses: actions/upload-artifact@v4
        with:
          name: app-prod-release-${{ matrix.build_type }}.apk
          path: android/app/build/outputs/apk/prod/release/app-prod-release.apk
          retention-days: 7
          if-no-files-found: error

  # Job to upload APKs to BrowserStack and get URLs
  upload-to-browserstack:
    name: Upload to BrowserStack and Get URLs
    runs-on: ubuntu-latest
    needs: build-android-dual
    if: always() && (needs.build-android-dual.result == 'success' || needs.build-android-dual.result == 'partial_success')
    outputs:
      with-srp-browserstack-url: ${{ steps.browserstack-upload.outputs.with-srp-browserstack-url }}
      without-srp-browserstack-url: ${{ steps.browserstack-upload.outputs.without-srp-browserstack-url }}
      with-srp-app-id: ${{ steps.browserstack-upload.outputs.with-srp-app-id }}
      without-srp-app-id: ${{ steps.browserstack-upload.outputs.without-srp-app-id }}
      with-srp-version: ${{ steps.browserstack-upload.outputs.with-srp-version }}
      without-srp-version: ${{ steps.browserstack-upload.outputs.without-srp-version }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download APK Artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts
        continue-on-error: true
      
      - name: BrowserStack Env Setup
        uses: browserstack/github-actions/setup-env@4478e16186f38e5be07721931642e65a028713c3
        with:
          username: ${{ env.BROWSERSTACK_USERNAME }}
          access-key: ${{ env.BROWSERSTACK_ACCESS_KEY }}
          project-name: ${{ github.repository }}
      
      - name: Upload APKs to BrowserStack
        id: browserstack-upload
        run: |
          echo "Uploading APKs to BrowserStack..."
          
          # List artifacts directory structure for debugging
          echo "Artifacts directory structure:"
          find ./artifacts -type f -name "*.apk" | head -10
          echo "Full artifacts directory tree:"
          find ./artifacts -type d | head -20
          
          # Find the APK files in their subdirectories
          WITH_SRP_APK=$(find ./artifacts -path "*/app-prod-release-with-srp.apk/app-prod-release.apk" | head -1)
          WITHOUT_SRP_APK=$(find ./artifacts -path "*/app-prod-release-without-srp.apk/app-prod-release.apk" | head -1)
          
          # Fallback: if not found in subdirectories, try direct search
          if [ -z "$WITH_SRP_APK" ]; then
            echo "With-SRP APK not found in subdirectory, trying direct search..."
            WITH_SRP_APK=$(find ./artifacts -name "app-prod-release-with-srp.apk" | head -1)
          fi
          
          if [ -z "$WITHOUT_SRP_APK" ]; then
            echo "Without-SRP APK not found in subdirectory, trying direct search..."
            WITHOUT_SRP_APK=$(find ./artifacts -name "app-prod-release-without-srp.apk" | head -1)
          fi
          
          echo "Found APKs:"
          echo "  With SRP: $WITH_SRP_APK"
          echo "  Without SRP: $WITHOUT_SRP_APK"
          
          # Verify files exist and are readable
          if [ -n "$WITH_SRP_APK" ] && [ -f "$WITH_SRP_APK" ]; then
            echo "With-SRP APK file exists and is readable"
            ls -la "$WITH_SRP_APK"
          else
            echo "With-SRP APK file not found or not readable"
          fi
          
          if [ -n "$WITHOUT_SRP_APK" ] && [ -f "$WITHOUT_SRP_APK" ]; then
            echo "Without-SRP APK file exists and is readable"
            ls -la "$WITHOUT_SRP_APK"
          else
            echo "Without-SRP APK file not found or not readable"
          fi
          
          # Upload with-SRP APK
          if [ -f "$WITH_SRP_APK" ]; then
            echo "Uploading with-SRP APK to BrowserStack..."
            WITH_SRP_RESPONSE=$(curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
              -X POST "https://api-cloud.browserstack.com/app-automate/upload" \
              -F "file=@$WITH_SRP_APK" \
              -F "custom_id=MetaMask-Android-With-SRP-${{ github.run_id }}")
            
            WITH_SRP_APP_URL=$(echo "$WITH_SRP_RESPONSE" | jq -r '.app_url')
            WITH_SRP_APP_ID=$(echo "$WITH_SRP_RESPONSE" | jq -r '.app_id')
            
            echo "With-SRP APK uploaded successfully"
            echo "  App URL: $WITH_SRP_APP_URL"
            echo "  App ID: $WITH_SRP_APP_ID"
            
            {
              echo "with-srp-browserstack-url=$WITH_SRP_APP_URL"
              echo "with-srp-app-id=$WITH_SRP_APP_ID"
              echo "with-srp-version=Manual-Input"
            } >> "$GITHUB_OUTPUT"
          else
            echo "With-SRP APK not found"
            {
              echo "with-srp-browserstack-url="
              echo "with-srp-app-id="
              echo "with-srp-version="
            } >> "$GITHUB_OUTPUT"
          fi
          
          # Upload without-SRP APK
          if [ -f "$WITHOUT_SRP_APK" ]; then
            echo "Uploading without-SRP APK to BrowserStack..."
            WITHOUT_SRP_RESPONSE=$(curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
              -X POST "https://api-cloud.browserstack.com/app-automate/upload" \
              -F "file=@$WITHOUT_SRP_APK" \
              -F "custom_id=MetaMask-Android-Without-SRP-${{ github.run_id }}")
            
            WITHOUT_SRP_APP_URL=$(echo "$WITHOUT_SRP_RESPONSE" | jq -r '.app_url')
            WITHOUT_SRP_APP_ID=$(echo "$WITHOUT_SRP_RESPONSE" | jq -r '.app_id')
            
            echo "Without-SRP APK uploaded successfully"
            echo "App URL: $WITHOUT_SRP_APP_URL"
            echo "App ID: $WITHOUT_SRP_APP_ID"
            
            {
              echo "without-srp-browserstack-url=$WITHOUT_SRP_APP_URL"
              echo "without-srp-app-id=$WITHOUT_SRP_APP_ID"
              echo "without-srp-version=Manual-Input"
            } >> "$GITHUB_OUTPUT"
          else
            echo "Without-SRP APK not found"
            {
              echo "without-srp-browserstack-url="
              echo "without-srp-app-id="
              echo "without-srp-version="
            } >> "$GITHUB_OUTPUT"
          fi
          
          echo "BrowserStack upload process completed!"
          echo "With-SRP URL: $WITH_SRP_APP_URL"
          echo "Without-SRP URL: $WITHOUT_SRP_APP_URL"