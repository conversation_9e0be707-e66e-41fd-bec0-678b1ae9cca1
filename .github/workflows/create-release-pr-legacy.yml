name: Create Release Pull Request (Legacy)

on:
  workflow_dispatch:
    inputs:
      base-branch:
        description: 'The base branch, tag, or SHA for git operations and the pull request.'
        required: true
      semver-version:
        description: 'A semantic version. eg: x.x.x'
        required: true
      previous-version-tag:
        description: 'Previous release version tag. eg: v7.7.0'
        required: true
jobs:
  generate-build-version:
    uses: MetaMask/metamask-mobile-build-version/.github/workflows/metamask-mobile-build-version.yml@v0.2.0
    permissions:
      id-token: write

  create-release-pr:
    needs: generate-build-version
    uses: MetaMask/github-tools/.github/workflows/create-release-pr.yml@fc6fe1a3fb591f6afa61f0dbbe7698bd50fab9c7
    with:
      platform: mobile
      base-branch: ${{ inputs.base-branch }}
      semver-version: ${{ inputs.semver-version }}
      previous-version-tag: ${{ inputs.previous-version-tag }}
      mobile-build-version: ${{ needs.generate-build-version.outputs.build-version }}
      github-tools-version: fc6fe1a3fb591f6afa61f0dbbe7698bd50fab9c7

    secrets:
      # This token needs read permissions to metamask-planning & write permissions to metamask-mobile
      github-token: ${{ secrets.PR_TOKEN }}
      google-application-creds-base64: ${{ secrets.GCP_RLS_SHEET_ACCOUNT_BASE64 }}
    permissions:
      contents: write
      pull-requests: write
