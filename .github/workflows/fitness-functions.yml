name: Fitness Functions CI

on:
  pull_request:
    types: [assigned, opened, synchronize, reopened]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Run fitness functions
        env:
          BASE_REF: ${{ github.event.pull_request.base.ref }}
        run: |
          # The following command generates a diff of changes between the common
          # ancestor of $BASE_REF and HEAD, and the current commit (HEAD), for
          # files in the current directory and its subdirectories. The output is
          # then saved to a file called "diff".
          git diff "$(git merge-base "origin/$BASE_REF" HEAD)" HEAD -- . > ./diff
          yarn run fitness-functions -- "ci" "./diff"
        working-directory: '.github/scripts'
