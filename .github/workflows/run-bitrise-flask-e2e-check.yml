name: Run Bitrise Flask E2E Check

on:
  issue_comment:
    types: [edited, deleted]
  pull_request:
    types: [opened, reopened, labeled, unlabeled, synchronize]

env:
  E2E_LABEL: 'Run Flask E2E'
  NO_E2E_LABEL: 'No Flask E2E Needed'
  E2E_PIPELINE: 'flask_smoke_e2e_pipeline'
  WORKFLOW_NAME: 'run-bitrise-flask-e2e-check'

jobs:
  run-bitrise-flask-e2e-check:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
      checks: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install dependencies with retry
        uses: nick-fields/retry@ce71cc2ab81d554ebbe88c79ab5975992d79ba08 #v3.0.2
        with:
          timeout_minutes: 10
          max_attempts: 3
          retry_wait_seconds: 30
          command: cd .github/scripts && yarn --immutable

      - name: Check Bitrise Flask E2E Status
        env:
          BITRISE_BUILD_TRIGGER_TOKEN: ${{ secrets.BITRISE_BUILD_TRIGGER_TOKEN }}
          BITRISE_APP_ID: ${{ secrets.BITRISE_APP_ID }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          STATUS_CHECK_NAME: 'Bitrise Flask E2E Status'
        # The status check created under this workflow may be bucketed under another check suite in Github actions. This is a result of workflows with the same triggers.
        # For example, the status check may show as `CLA Signature Bot / Bitrise Flask E2E Status`. This is a bug on Github's UI. https://github.com/orgs/community/discussions/24616
        run: yarn run run-bitrise-e2e-check
        working-directory: '.github/scripts' 