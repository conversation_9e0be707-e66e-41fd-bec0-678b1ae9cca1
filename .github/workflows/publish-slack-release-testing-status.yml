name: Trigger Publish Slack Release Testing Status

on:
  schedule:
    - cron: '0 15 * * 1-5'  #M-F at 15:00 UTC
  workflow_dispatch:  # Allows manual triggering

jobs:
  call-publish-slack-release-testing-status:
    permissions:
      contents: write
      pull-requests: write
    uses: MetaMask/github-tools/.github/workflows/publish-slack-release-testing-status.yml@bce51a03da4736bef72f67b71ca77714a38fc067
    with:
      platform: 'mobile'
      test-only: 'true'
      google-document-id: '1tsoodlAlyvEUpkkcNcbZ4PM9HuC9cEM80RZeoVv5OCQ'
    secrets:
      slack-api-key: ${{ secrets.SLACKBOT_RLS_TOKEN }}
      github-token: ${{ secrets.PR_TOKEN }}
      google-application-creds-base64: ${{ secrets.GCP_RLS_SHEET_ACCOUNT_BASE64 }}
