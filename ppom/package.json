{"name": "ppom-view", "version": "1.0.0", "devDependencies": {"@babel/preset-env": "^7.22.6", "@blockaid/ppom_release": "^1.5.3", "babel-loader": "^9.1.2", "binary-base64-loader": "^1.0.0", "buffer": "^6.0.3", "eslint": "^7.23.0", "html-inline-script-webpack-plugin": "^3.2.0", "html-webpack-plugin": "^5.5.3", "raw-loader": "^4.0.2", "react-native-webview-invoke": "^0.6.2", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.88.1", "webpack-cli": "^5.1.4"}, "scripts": {"lint": "eslint '**/*.{js,ts,tsx}'", "build-html": "webpack --config ./webpack.config.html.js", "build-html-js": "webpack --config ./webpack.config.html-js.js", "build-version": "webpack --config ./webpack.config.version.js", "build": "yarn clean && yarn build-html && yarn build-html-js && yarn build-version", "clean": "rm -rf ../app/lib/ppom/ppom.html.js && rm -rf ./dist"}}