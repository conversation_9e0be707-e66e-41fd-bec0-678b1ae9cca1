# ENV vars for e2e tests to run on Bitrise CI
export MM_TEST_ACCOUNT_SRP='word1 word... word12'
export MM_TEST_ACCOUNT_ADDRESS='0x...'
export MM_TEST_ACCOUNT_PRIVATE_KEY=''
export IS_TEST="true"
# Temporary mechanism to enable security alerts API prior to release.
export MM_SECURITY_ALERTS_API_ENABLED="true"

# Multichain test dapp URL configuration
# By default, uses the local embedded test dapp
# Set USE_LOCAL_DAPP=false to use a remote dapp URL instead
export USE_LOCAL_DAPP=true

# Remote dapp URL (only used when USE_LOCAL_DAPP=false)
# Option 1: Use the official MetaMask test dapp
export MULTICHAIN_DAPP_URL="https://metamask.github.io/test-dapp-multichain/latest/"
# Option 2: Use a custom remote URL
# export MULTICHAIN_DAPP_URL="https://your-multichain-dapp.example.com/"

# E2E prebuild paths
export PREBUILT_IOS_APP_PATH='build/MetaMask.app'
export PREBUILT_ANDROID_APK_PATH='build/MetaMask.apk'
export PREBUILT_ANDROID_TEST_APK_PATH='build/MetaMask-Test.apk'

export TEST_SRP_1=
export TEST_SRP_2=
export TEST_SRP_3=
export BROWSERSTACK_USERNAME=
export BROWSERSTACK_ACCESS_KEY=
export SEEDLESS_ONBOARDING_ENABLED=
export SOLANA_MODAL_ENABLED=