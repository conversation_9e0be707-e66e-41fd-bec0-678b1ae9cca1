# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

NeoNix Wallet is a React Native wallet application providing access to Ethereum and other blockchains. It's built using Expo framework with native iOS and Android support, featuring a comprehensive UI component system, robust testing infrastructure, and multiple build variants for different environments.

## Technology Stack

- **Core Framework**: React Native 0.76.9 with Expo 52.0.47
- **State Management**: Redux with Redux Saga, Redux Persist
- **UI Framework**: @metamask/design-system-react-native with Tailwind CSS
- **Testing**: Jest (unit), <PERSON>ox (E2E iOS/Android), <PERSON><PERSON><PERSON> (performance), <PERSON><PERSON><PERSON> (BrowserStack)
- **Build Tools**: Metro bundler, Babel with React Compiler
- **Development**: TypeScript 5.4.5, ESLint, Prettier
- **Native Dependencies**: Firebase, Sentry, react-native-reanimated, and extensive crypto libraries

## Essential Development Commands

### Initial Setup

**Native Development (Full Setup)**:

```bash
# Complete setup with native dependencies
yarn setup

# Start Metro bundler
yarn watch
```

**Expo Development (Recommended for JS-only changes)**:

```bash
# Expo setup - faster, no native compilation
yarn setup:expo

# Start bundler for Expo development
yarn watch
```

### Running the Application

**iOS**:

```bash
# Debug build on iOS simulator
yarn start:ios

# QA build
yarn start:ios:qa

# Flask variant (experimental features)
yarn start:ios:flask

# Run on physical device
yarn start:ios:device
```

**Android**:

```bash
# Debug build on Android emulator/device
yarn start:android

# QA build
yarn start:android:qa

# Flask variant
yarn start:android:flask
```

### Testing Commands

**Unit Tests**:

```bash
# Run all unit tests
yarn test:unit

# Update snapshots
yarn test:unit:update
```

**E2E Tests (Detox)**:

```bash
# Build for E2E testing
yarn test:e2e:ios:debug:build
yarn test:e2e:android:debug:build

# Run E2E tests
yarn test:e2e:ios:debug:run
yarn test:e2e:android:debug:run

# Run specific test file
yarn test:e2e:ios:debug:run e2e/specs/TEST_NAME.spec.js

# Run by tag (smoke tests)
yarn test:e2e:ios:debug:run --testNamePattern="Smoke"
```

**Performance Tests (Appium/Appwright)**:

```bash
# Appium tests on local emulator
yarn test:wdio:android
yarn test:wdio:ios

# Appwright on BrowserStack
yarn run-appwright:android-bs
yarn run-appwright:ios-bs
```

### Build Commands

**Development Builds**:

```bash
# Android debug
yarn build:android:main:local

# iOS debug
yarn build:ios:main:local
```

**Release Builds**:

```bash
# Production builds
yarn build:android:main:prod
yarn build:ios:main:prod

# QA builds
yarn build:android:qa:prod
yarn build:ios:qa:prod

# Flask builds (experimental)
yarn build:android:flask:prod
yarn build:ios:flask:prod
```

### Code Quality

```bash
# Linting
yarn lint
yarn lint:fix
yarn lint:tsc

# Formatting
yarn format
yarn format:check:changed

# Dependency checks
yarn test:depcheck
yarn audit:ci
```

## Project Architecture

### Directory Structure

- **`app/`** - Main application source code
  - **`actions/`** - Redux actions
  - **`components/`** - React Native UI components
  - **`component-library/`** - Shared component library (use after design-system)
  - **`core/`** - Core business logic and controllers
  - **`reducers/`** - Redux reducers
  - **`selectors/`** - Redux state selectors
  - **`store/`** - Redux store configuration
  - **`util/`** - Utility functions and helpers
  - **`constants/`** - Application constants
  - **`styles/`** - Global styles and themes

### State Management Architecture

- **Redux Store**: Centralized state management with persistence
- **Redux Saga**: Handles side effects, async operations, and complex flows
- **Controllers**: Business logic layer from @metamask packages
- **Selectors**: Memoized state access patterns using Reselect

### Key Controllers (from @metamask packages)

- **AccountsController**: Account management and switching
- **NetworkController**: Network configuration and switching
- **TransactionController**: Transaction lifecycle management
- **AssetsController**: Token and collectible detection
- **PermissionController**: DApp permission management
- **KeyringController**: Private key and account creation

## UI Development Guidelines

**CRITICAL**: Always use the component hierarchy in this strict order:

1. **FIRST**: `@metamask/design-system-react-native` components
2. **SECOND**: `app/component-library` components (only if design system lacks it)
3. **LAST RESORT**: Custom components with StyleSheet (avoid unless necessary)

### Required Imports

```tsx
import { useTailwind } from '@metamask/design-system-twrnc-preset';
import {
  Box,
  Text,
  Button,
  ButtonBase,
  Icon,
  TextVariant,
  BoxFlexDirection,
  BoxAlignItems,
  BoxJustifyContent,
  // ... other design system components
} from '@metamask/design-system-react-native';
```

### Styling Rules

- ✅ **DO**: Use `Box` instead of `View`
- ✅ **DO**: Use `Text` with variants instead of raw Text
- ✅ **DO**: Use `twClassName` prop for static styles
- ✅ **DO**: Use `tw.style()` for dynamic/interactive styles
- ❌ **NEVER**: Use `StyleSheet.create()`
- ❌ **NEVER**: Import `tw from 'twrnc'` (use `useTailwind` hook)

### Example Pattern

```tsx
const MyComponent = () => {
  const tw = useTailwind();

  return (
    <Box
      flexDirection={BoxFlexDirection.Row}
      alignItems={BoxAlignItems.Center}
      twClassName="w-full bg-default p-4 gap-3"
    >
      <Text variant={TextVariant.HeadingMd}>Title</Text>
      <ButtonBase
        style={({ pressed }) =>
          tw.style(
            'px-4 py-2 rounded-lg bg-primary-default',
            pressed && 'bg-primary-pressed',
          )
        }
      >
        <Text variant={TextVariant.BodyMd}>Press Me</Text>
      </ButtonBase>
    </Box>
  );
};
```

## Testing Strategy

### Unit Testing Best Practices

- Use AAA pattern (Arrange, Act, Assert)
- Test public behavior, not implementation details
- Use meaningful test names describing the purpose
- Mock external dependencies minimally
- Prefer `toBeOnTheScreen()` over `toBeDefined()` for UI elements

### Example Unit Test

```typescript
it('displays error message when API fails', async () => {
  // Arrange
  mockApi.failOnce();

  // Act
  const { findByText } = render(<MyComponent />);

  // Assert
  expect(await findByText('Something went wrong')).toBeOnTheScreen();
});
```

### E2E Testing

- **Primary Framework**: Detox for iOS and Android
- **Devices**: iPhone 15 Pro (iOS), Pixel 5 API 34 (Android)
- **Test Environment**: Set `export IS_TEST='true'`
- **Requirements**: Test wallet with testnet/mainnet access

## Pull Request Guidelines

### Title Format (Conventional Commits)

- `feat:` - New features or enhancements
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (no logic impact)
- `refactor:` - Code restructuring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks
- `perf:` - Performance improvements

### Example Titles

- ✅ `feat: add NFT gallery to collectibles tab`
- ✅ `fix: resolve wallet connection timeout on cold start`
- ✅ `test: add e2e spec for onboarding SRP import flow`
- ❌ `Add some stuff`
- ❌ `fixing bug`

### PR Requirements

- **Assignment**: Always assign PR to yourself (the author)
- **Template**: Complete all sections in `.github/pull-request-template.md`
- **Gherkin Testing Steps**: Provide reproducible manual test scenarios
- **Screenshots**: Required for UI changes
- **Team Labels**: Auto-detect based on GitHub team membership when possible

## Environment and Prerequisites

### Required Software

- **Node.js**: ^20.18.0
- **Yarn**: ^1.22.22
- **Xcode**: Latest version (for iOS development)
- **Android Studio**: Latest version (for Android development)
- **Git**: For version control

### Environment Files

Required environment files in project root:

- `.js.env` - JavaScript environment variables
- `.android.env` - Android-specific variables
- `.ios.env` - iOS-specific variables

### Firebase Setup

NeoNix Wallet uses Firebase Cloud Messaging. You need:

- **Internal**: Grab `.js.env` from 1Password vault
- **External**: Create Firebase project with package name `io.neonix`
  - Add `android/app/google-services.json`
  - Add `ios/GoogleServices/GoogleService-Info.plist`

## Build Variants

### Development Variants

- **Debug**: Development builds with debugging enabled
- **QA**: Testing builds with QA environment settings
- **Flask**: Experimental features enabled
- **E2E**: Builds configured for end-to-end testing

### Environment Variables

Key environment variables that affect builds:

- `NEONIX_BUILD_TYPE`: main, flask, QA
- `NEONIX_ENVIRONMENT`: local, beta, production, test, e2e
- `IS_TEST`: Enable test environment features

## Performance and Debugging

### Performance Testing

- **Reassure**: Performance regression testing
- **Launch Time Tests**: Appium-based app launch monitoring
- **Memory Profiling**: React Native performance tools integration

### Debugging Tools

```bash
# Enable performance monitoring
yarn test:reassure:baseline
yarn test:reassure:branch

# Debug tools
yarn start:ios --device  # Physical device debugging
```

## Common Development Patterns

### State Selection

```typescript
import { useSelector } from 'react-redux';
import { selectNetworkConfigurations } from '../selectors/networkController';

const networks = useSelector(selectNetworkConfigurations);
```

### Controller Usage

```typescript
import { useEngine } from '../core/Engine';

const { NetworkController } = useEngine();
await NetworkController.setActiveNetwork(networkId);
```

### Asset Management

```typescript
import { useTokenBalances } from '../hooks/useTokenBalances';

const { tokenBalances } = useTokenBalances();
```

## Troubleshooting

### Common Issues

- **Metro bundler issues**: `yarn watch:clean`
- **iOS build failures**: `yarn clean:ios && yarn pod:install`
- **Android build failures**: `yarn clean:android`
- **Expo loading slowly**: First test may fail and auto-restart
- **Firebase errors**: Ensure correct `google-services` files are in place

### Debug Commands

```bash
# Clear all caches and reinstall
yarn clean

# Reset Metro bundler
yarn watch:clean

# Check for circular dependencies
yarn circular:deps

# Validate attributions
yarn test:attribution-check
```

## Important Notes

- **Design System Priority**: Always check `@metamask/design-system-react-native` first
- **No StyleSheet**: Never use `StyleSheet.create()` - use Tailwind classes
- **Test Coverage**: Unit tests are required for new functionality
- **PR Assignment**: Always assign PRs to yourself as the author
- **Expo vs Native**: Use Expo for JS-only changes, native setup for library changes
- **Build Artifacts**: Pre-compiled development builds available through Runway links
- **Running** - We do not use expo to run, instead we run using yarn setup and yarn start:android
