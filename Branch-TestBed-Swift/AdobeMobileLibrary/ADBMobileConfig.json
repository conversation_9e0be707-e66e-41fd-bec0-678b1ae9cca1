{"lastModified": "2017-09-27T03:20:01.469Z", "marketingCloud": {"org": "5242A86A57A9D16D7F000101@AdobeOrg"}, "target": {"clientCode": "", "timeout": 5}, "audienceManager": {"server": "", "analyticsForwardingEnabled": false, "timeout": 5}, "acquisition": {"server": "c00.adobe.com", "appid": "a2a5860764cbb7569561ca04ac251599c74a79e5724e01b65a6bd7853a588cfb"}, "analytics": {"rsids": "ebmthirdpartytestbed", "server": "exchangepartnerbranchmetrics.sc.omtrdc.net", "ssl": true, "offlineEnabled": true, "charset": "UTF-8", "lifecycleTimeout": 300, "privacyDefault": "<PERSON>in", "batchLimit": 0, "timezone": "PDT", "timezoneOffset": -420, "referrerTimeout": 15, "backdateSessionInfo": false, "poi": []}, "messages": [{"messageId": "b7ba0b5b-1c2d-4fa0-8e1c-17d39fafae6d", "payload": {"templateurl": "https://requestb.in/1h7yo5b1", "templatebody": "", "contenttype": "", "timeout": 2}, "showOffline": true, "showRule": "always", "endDate": 2524730400, "startDate": 0, "template": "callback", "audiences": [], "triggers": [{"key": "a.<PERSON>", "matches": "ex", "values": null}]}], "remotes": {"analytics.poi": "https://assets.adobedtm.com/b213090c5204bf94318f4ef0539a38b487d10368/scripts/satellite-598242c564746d5eef004ee6.json", "messages": "https://assets.adobedtm.com/b213090c5204bf94318f4ef0539a38b487d10368/scripts/satellite-598242c564746d0ba5015804.json"}}