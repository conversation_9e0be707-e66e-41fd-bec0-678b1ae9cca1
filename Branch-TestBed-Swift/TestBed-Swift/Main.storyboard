<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13189.4" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="d5D-s8-qUb">
    <device id="retina5_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13165.3"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--MainNavigationController-->
        <scene sceneID="h0k-6e-SBq">
            <objects>
                <navigationController storyboardIdentifier="MainNavigationController" automaticallyAdjustsScrollViewInsets="NO" useStoryboardIdentifierAsRestorationIdentifier="YES" id="d5D-s8-qUb" userLabel="MainNavigationController" customClass="NavigationController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationItem key="navigationItem" title="Branch-TestBed" id="vxy-nF-3Wa"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="GMh-cm-snc">
                        <rect key="frame" x="0.0" y="20" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="AU3-dt-wFP" kind="relationship" relationship="rootViewController" id="d2l-0l-9dU"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0le-mP-k9p" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1312" y="27"/>
        </scene>
        <!--Home-->
        <scene sceneID="TFT-aZ-iB9">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="Home" storyboardName="Home" id="AU3-dt-wFP" sceneMemberID="viewController">
                    <navigationItem key="navigationItem" id="Bno-gA-59C"/>
                </viewControllerPlaceholder>
                <placeholder placeholderIdentifier="IBFirstResponder" id="h5p-1b-7dw" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2030" y="27"/>
        </scene>
    </scenes>
</document>
