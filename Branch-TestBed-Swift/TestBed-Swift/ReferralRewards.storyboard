<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="29h-pU-1Nm">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--TextViewForm-->
        <scene sceneID="xGX-88-Rzf">
            <objects>
                <viewControllerPlaceholder storyboardName="TextViewForm" id="Kk9-pt-FR9" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="l7h-7N-4ua" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1688" y="964.31784107946032"/>
        </scene>
        <!--Referral Rewards-->
        <scene sceneID="SKp-ek-2oK">
            <objects>
                <tableViewController id="PlA-l6-I6h" userLabel="Referral Rewards" customClass="ReferralRewardsTableViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" sectionFooterHeight="18" id="RkL-kk-d6t">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" cocoaTouchSystemColor="groupTableViewBackgroundColor"/>
                        <sections>
                            <tableViewSection headerTitle="Referral Rewards" id="Qcc-cM-WUs">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="Fg0-cr-Ygn" userLabel="rewardsBucketLabel">
                                        <rect key="frame" x="0.0" y="55.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Fg0-cr-Ygn" id="rHW-0A-Kvi">
                                            <rect key="frame" x="0.0" y="0.0" width="342" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards Bucket" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bUJ-jK-KAo" userLabel="rewardsBucketLabel">
                                                    <rect key="frame" x="14" y="11.5" width="123.5" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="default" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="pmS-vB-FTo" userLabel="rewardsBucketTextField">
                                                    <rect key="frame" x="137.5" y="13.5" width="196.5" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="bUJ-jK-KAo" firstAttribute="centerY" secondItem="rHW-0A-Kvi" secondAttribute="centerY" id="EJ9-6r-73j"/>
                                                <constraint firstItem="pmS-vB-FTo" firstAttribute="centerY" secondItem="rHW-0A-Kvi" secondAttribute="centerY" id="ba8-yK-eio"/>
                                                <constraint firstItem="bUJ-jK-KAo" firstAttribute="leading" secondItem="rHW-0A-Kvi" secondAttribute="leadingMargin" constant="6" id="epj-sp-HcB"/>
                                                <constraint firstItem="pmS-vB-FTo" firstAttribute="leading" secondItem="bUJ-jK-KAo" secondAttribute="trailing" id="jkA-V8-c3H"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="pmS-vB-FTo" secondAttribute="trailing" id="yAe-Ln-lWQ"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="QmG-Oj-kmq" userLabel="rewardsBalanceOfBucketLabel">
                                        <rect key="frame" x="0.0" y="99.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="QmG-Oj-kmq" id="UmN-EN-Vro">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards Balance of Bucket" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jZg-9p-ee7" userLabel="rewardsBalanceOfBucketLabel">
                                                    <rect key="frame" x="14" y="11.5" width="208" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <activityIndicatorView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" hidesWhenStopped="YES" animating="YES" style="gray" translatesAutoresizingMaskIntoConstraints="NO" id="erE-AI-Izc" userLabel="activityIndicator">
                                                    <rect key="frame" x="343" y="12" width="24" height="20"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="24" id="93Y-8s-w0c"/>
                                                    </constraints>
                                                    <color key="color" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </activityIndicatorView>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="fww-A1-EDi" userLabel="rewardsBalanceOfBucketTextField">
                                                    <rect key="frame" x="222" y="13.5" width="145" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="fww-A1-EDi" firstAttribute="leading" secondItem="jZg-9p-ee7" secondAttribute="trailing" id="EKX-h0-Hc5"/>
                                                <constraint firstItem="jZg-9p-ee7" firstAttribute="leading" secondItem="UmN-EN-Vro" secondAttribute="leadingMargin" constant="6" id="NL3-80-VXS"/>
                                                <constraint firstItem="jZg-9p-ee7" firstAttribute="centerY" secondItem="UmN-EN-Vro" secondAttribute="centerY" id="cnE-eA-Ues"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="fww-A1-EDi" secondAttribute="trailing" id="hDa-J4-53Y"/>
                                                <constraint firstItem="erE-AI-Izc" firstAttribute="centerY" secondItem="UmN-EN-Vro" secondAttribute="centerY" id="hQH-qo-i2h"/>
                                                <constraint firstItem="fww-A1-EDi" firstAttribute="centerY" secondItem="UmN-EN-Vro" secondAttribute="centerY" id="l4U-DA-B0T"/>
                                                <constraint firstItem="erE-AI-Izc" firstAttribute="trailing" secondItem="UmN-EN-Vro" secondAttribute="trailingMargin" id="qqP-P0-aR0"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="LYv-TR-nwL" userLabel="reloadBalanceButton">
                                        <rect key="frame" x="0.0" y="143.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="LYv-TR-nwL" id="GEc-GI-tct">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ape-Vc-Cy1" userLabel="reloadBalanceButton">
                                                    <rect key="frame" x="14" y="7" width="353" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Reload Balance"/>
                                                    <connections>
                                                        <action selector="reloadBalanceButtonTouchUpInside:" destination="PlA-l6-I6h" eventType="touchUpInside" id="nfn-YO-6w0"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Ape-Vc-Cy1" firstAttribute="trailing" secondItem="GEc-GI-tct" secondAttribute="trailingMargin" id="5Jq-d2-H1Y"/>
                                                <constraint firstItem="Ape-Vc-Cy1" firstAttribute="leading" secondItem="GEc-GI-tct" secondAttribute="leadingMargin" constant="6" id="JbQ-Iv-xYe"/>
                                                <constraint firstItem="Ape-Vc-Cy1" firstAttribute="centerY" secondItem="GEc-GI-tct" secondAttribute="centerY" id="S7n-Vw-qST"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="sW4-3t-ehp" userLabel="rewardPointsToRedeemLabel">
                                        <rect key="frame" x="0.0" y="187.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="sW4-3t-ehp" id="AeL-fT-JsH">
                                            <rect key="frame" x="0.0" y="0.0" width="342" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reward Points to Redeem" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LvH-Ij-Dmq" userLabel="rewardPointsToRedeemLabel">
                                                    <rect key="frame" x="14" y="11.5" width="196" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="5" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="d6e-1w-iA3" userLabel="rewardPointsToRedeemTextField">
                                                    <rect key="frame" x="210" y="13.5" width="124" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="LvH-Ij-Dmq" firstAttribute="centerY" secondItem="AeL-fT-JsH" secondAttribute="centerY" id="3TP-ea-s0y"/>
                                                <constraint firstItem="d6e-1w-iA3" firstAttribute="centerY" secondItem="AeL-fT-JsH" secondAttribute="centerY" id="Gwi-5c-yyx"/>
                                                <constraint firstItem="LvH-Ij-Dmq" firstAttribute="leading" secondItem="AeL-fT-JsH" secondAttribute="leadingMargin" constant="6" id="UUF-ep-T3I"/>
                                                <constraint firstItem="d6e-1w-iA3" firstAttribute="leading" secondItem="LvH-Ij-Dmq" secondAttribute="trailing" id="or9-gz-cLy"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="d6e-1w-iA3" secondAttribute="trailing" id="xIV-nR-4jO"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="rko-NO-Uxg" userLabel="redeemPointsButton">
                                        <rect key="frame" x="0.0" y="231.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="rko-NO-Uxg" id="x4U-vj-fSD">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zKs-jk-h39" userLabel="redeemPointsButton">
                                                    <rect key="frame" x="14" y="7" width="353" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Redeem Points"/>
                                                    <connections>
                                                        <action selector="redeemPointsButtonTouchUpInside:" destination="PlA-l6-I6h" eventType="touchUpInside" id="ntw-7I-PG0"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="zKs-jk-h39" firstAttribute="leading" secondItem="x4U-vj-fSD" secondAttribute="leadingMargin" constant="6" id="riQ-mU-er9"/>
                                                <constraint firstItem="zKs-jk-h39" firstAttribute="trailing" secondItem="x4U-vj-fSD" secondAttribute="trailingMargin" id="tYU-ai-PzN"/>
                                                <constraint firstItem="zKs-jk-h39" firstAttribute="centerY" secondItem="x4U-vj-fSD" secondAttribute="centerY" id="vHF-t4-bAb"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="zgI-4p-Q1g" userLabel="rewardHistoryLabel">
                                        <rect key="frame" x="0.0" y="275.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="zgI-4p-Q1g" id="eBS-dN-uUR">
                                            <rect key="frame" x="0.0" y="0.0" width="342" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards History" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Y5I-xs-4aT" userLabel="rewardHistoryLabel">
                                                    <rect key="frame" x="14" y="11.5" width="320" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Y5I-xs-4aT" firstAttribute="leading" secondItem="eBS-dN-uUR" secondAttribute="leadingMargin" constant="6" id="ITe-aN-fZ1"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="Y5I-xs-4aT" secondAttribute="trailing" id="Ja8-C3-ch5"/>
                                                <constraint firstItem="Y5I-xs-4aT" firstAttribute="centerY" secondItem="eBS-dN-uUR" secondAttribute="centerY" id="MRF-Dz-i0X"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="PlA-l6-I6h" id="uoV-Fw-4h2"/>
                            <outlet property="delegate" destination="PlA-l6-I6h" id="waB-xU-EZ3"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" id="xh3-gf-hzX">
                        <barButtonItem key="leftBarButtonItem" style="done" systemItem="done" id="4ak-Ka-joI">
                            <connections>
                                <segue destination="x1D-Jy-TRc" kind="unwind" identifier="UnwindByBeingDone" unwindAction="unwindByBeingDone:" id="dYs-gg-8nc"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="activityIndicator" destination="erE-AI-Izc" id="4lW-7P-vfz"/>
                        <outlet property="redeemPointsButton" destination="zKs-jk-h39" id="VqL-bU-Kzg"/>
                        <outlet property="reloadBalanceButton" destination="Ape-Vc-Cy1" id="uZ1-rx-fUJ"/>
                        <outlet property="rewardHistoryLabel" destination="Y5I-xs-4aT" id="ue8-lA-3Vo"/>
                        <outlet property="rewardPointsToRedeemTextField" destination="d6e-1w-iA3" id="fSE-gg-F7D"/>
                        <outlet property="rewardsBalanceOfBucketTextField" destination="fww-A1-EDi" id="7tA-WF-uGy"/>
                        <outlet property="rewardsBucketTextField" destination="pmS-vB-FTo" id="8OK-QP-VOz"/>
                        <segue destination="Kk9-pt-FR9" kind="presentation" identifier="TextViewForm" id="Owy-8j-BS0"/>
                        <segue destination="atO-Bb-kh7" kind="show" identifier="CreditHistory" id="enZ-5e-7EF"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gJ6-R2-Mq6" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <exit id="x1D-Jy-TRc" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="1044" y="1020.5397301349326"/>
        </scene>
        <!--CreditHistoryTableView-->
        <scene sceneID="Pu4-iU-pDk">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="CreditHistory" storyboardName="CreditHistoryTableView" id="atO-Bb-kh7" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xYV-w7-jSV" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1735.2" y="1063.2683658170915"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="gvT-x9-0P4">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="29h-pU-1Nm" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="8fu-v4-Cmq">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="PlA-l6-I6h" kind="relationship" relationship="rootViewController" id="UaM-pf-Eku"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xTa-UJ-et3" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="214" y="1021"/>
        </scene>
    </scenes>
</document>
