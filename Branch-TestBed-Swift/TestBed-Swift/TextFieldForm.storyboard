<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="zIj-Eb-eJn">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--TextFieldForm-->
        <scene sceneID="pZU-K5-V7Q">
            <objects>
                <tableViewController storyboardIdentifier="UITableViewController-5e7-2J-ss3" id="5e7-2J-ss3" userLabel="TextFieldForm" customClass="TextFieldFormTableViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="default" allowsSelection="NO" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" id="jPl-dh-hen">
                        <rect key="frame" x="0.0" y="0.0" width="600" height="600"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection id="72O-gi-kol" userLabel="Section">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="uvv-6Y-Gnd">
                                        <rect key="frame" x="0.0" y="35" width="600" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="uvv-6Y-Gnd" id="CYU-kY-iEa">
                                            <rect key="frame" x="0.0" y="0.0" width="600" height="43.5"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="PT5-p8-wFJ" userLabel="textField">
                                                    <rect key="frame" x="14" y="12.5" width="584" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done" enablesReturnKeyAutomatically="YES"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="PT5-p8-wFJ" firstAttribute="leading" secondItem="CYU-kY-iEa" secondAttribute="leadingMargin" constant="6" id="Ag8-7N-DO4"/>
                                                <constraint firstItem="PT5-p8-wFJ" firstAttribute="trailing" secondItem="CYU-kY-iEa" secondAttribute="trailingMargin" constant="6" id="EhZ-Ul-y75"/>
                                                <constraint firstItem="PT5-p8-wFJ" firstAttribute="centerY" secondItem="CYU-kY-iEa" secondAttribute="centerY" id="nRO-1b-bk3"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="5e7-2J-ss3" id="qaT-ik-f2T"/>
                            <outlet property="delegate" destination="5e7-2J-ss3" id="GN7-3F-lLb"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" id="cUi-EA-QPT" userLabel="TextField Form Navigation Item">
                        <barButtonItem key="leftBarButtonItem" systemItem="cancel" id="gXj-cr-h5T" userLabel="cancelButton">
                            <connections>
                                <segue destination="WIF-C2-NOF" kind="unwind" identifier="Cancel" unwindAction="unwindByCancelling:" id="jKp-JW-DEd"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" enabled="NO" systemItem="save" id="2pY-tp-6Fi" userLabel="saveButton">
                            <connections>
                                <segue destination="WIF-C2-NOF" kind="unwind" identifier="Save" unwindAction="unwindTextFieldForm:" id="DlX-R0-TeQ"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="saveButton" destination="2pY-tp-6Fi" id="9h4-dY-6dg"/>
                        <outlet property="textField" destination="PT5-p8-wFJ" id="eda-rU-06Q"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4vS-Ku-U98" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <exit id="WIF-C2-NOF" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="3745" y="1993"/>
        </scene>
        <!--TextFieldFormNavigationController-->
        <scene sceneID="y27-hq-aJF">
            <objects>
                <navigationController storyboardIdentifier="TextFieldFormNavigationController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="zIj-Eb-eJn" userLabel="TextFieldFormNavigationController" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="Dpc-nn-LL1">
                        <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="5e7-2J-ss3" kind="relationship" relationship="rootViewController" id="iXd-jk-6h3"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="erj-EU-s9Z" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2951" y="1993"/>
        </scene>
    </scenes>
</document>
