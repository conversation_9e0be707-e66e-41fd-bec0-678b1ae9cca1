<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13189.4" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="d5D-s8-qUb">
    <device id="retina5_5" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13165.3"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--ContentView-->
        <scene sceneID="seY-8S-64u">
            <objects>
                <viewController storyboardIdentifier="Content" useStoryboardIdentifierAsRestorationIdentifier="YES" id="UBk-EH-q5i" userLabel="ContentView" customClass="ContentViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="WBt-yN-ynz"/>
                        <viewControllerLayoutGuide type="bottom" id="ak0-YR-gE3"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="gLR-CJ-YNU">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6Rz-hr-Ez0">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="7xe-fV-91f">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="33"/>
                                        <subviews>
                                            <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Whe-4R-JeV" userLabel="imageView">
                                                <rect key="frame" x="107" y="0.0" width="200" height="0.0"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" priority="999" constant="200" id="EJH-ZP-nTb"/>
                                                    <constraint firstAttribute="width" constant="200" id="v5c-Ya-jen"/>
                                                </constraints>
                                            </imageView>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="lhb-hx-zKl" userLabel="contentTextView">
                                                <rect key="frame" x="0.0" y="0.0" width="414" height="33"/>
                                                <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="lhb-hx-zKl" firstAttribute="leading" secondItem="7xe-fV-91f" secondAttribute="leading" id="5BW-de-bzD"/>
                                            <constraint firstItem="lhb-hx-zKl" firstAttribute="trailing" secondItem="7xe-fV-91f" secondAttribute="trailing" id="QxA-UZ-Wc7"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="7xe-fV-91f" firstAttribute="top" secondItem="6Rz-hr-Ez0" secondAttribute="top" id="0Wz-N1-HTW"/>
                                    <constraint firstItem="7xe-fV-91f" firstAttribute="trailing" secondItem="6Rz-hr-Ez0" secondAttribute="trailing" id="Nks-Bb-CiM"/>
                                    <constraint firstItem="7xe-fV-91f" firstAttribute="bottom" secondItem="6Rz-hr-Ez0" secondAttribute="bottom" id="bcy-Zi-ESm"/>
                                    <constraint firstItem="7xe-fV-91f" firstAttribute="centerX" secondItem="6Rz-hr-Ez0" secondAttribute="centerX" id="ena-di-pd0"/>
                                    <constraint firstItem="7xe-fV-91f" firstAttribute="leading" secondItem="6Rz-hr-Ez0" secondAttribute="leading" id="iW7-XO-cgw"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="6Rz-hr-Ez0" firstAttribute="bottom" secondItem="gLR-CJ-YNU" secondAttribute="bottom" id="KC9-JI-yb6"/>
                            <constraint firstItem="6Rz-hr-Ez0" firstAttribute="top" secondItem="gLR-CJ-YNU" secondAttribute="top" id="NxY-Fe-Tnf"/>
                            <constraint firstItem="6Rz-hr-Ez0" firstAttribute="trailing" secondItem="gLR-CJ-YNU" secondAttribute="trailing" id="Q13-wc-4EG"/>
                            <constraint firstItem="6Rz-hr-Ez0" firstAttribute="leading" secondItem="gLR-CJ-YNU" secondAttribute="leading" id="p6R-Ht-W2Z"/>
                            <constraint firstItem="7xe-fV-91f" firstAttribute="width" secondItem="gLR-CJ-YNU" secondAttribute="width" id="tzw-hT-sVt"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="contentTextView" destination="lhb-hx-zKl" id="DbE-sJ-rb5"/>
                        <outlet property="imageView" destination="Whe-4R-JeV" id="Ta5-QP-as5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Kaj-aA-5Uu" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2333.5" y="-1135.5"/>
        </scene>
        <!--TestBed-SwiftTableView-->
        <scene sceneID="zgf-4b-gWH">
            <objects>
                <tableViewController storyboardIdentifier="ViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="3bi-5k-7qj" userLabel="TestBed-SwiftTableView" customClass="ViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="top" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="default" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" id="PqX-IA-oIC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection headerTitle="User ID" id="M3D-Op-5C4">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="9PD-Xf-pMv">
                                        <rect key="frame" x="0.0" y="55.333333333333336" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="9PD-Xf-pMv" id="oy4-nh-VrW">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="User ID" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fEr-U8-kO6" userLabel="userIDLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="57" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Anonymous" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Zqg-wL-Xby" userLabel="userIDTextField">
                                                    <rect key="frame" x="71" y="13.666666666666664" width="302" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="fEr-U8-kO6" firstAttribute="leading" secondItem="oy4-nh-VrW" secondAttribute="leadingMargin" constant="6" id="ASm-Av-CIz"/>
                                                <constraint firstItem="Zqg-wL-Xby" firstAttribute="leading" secondItem="fEr-U8-kO6" secondAttribute="trailing" id="VI9-t6-vUv"/>
                                                <constraint firstItem="Zqg-wL-Xby" firstAttribute="centerY" secondItem="oy4-nh-VrW" secondAttribute="centerY" id="qYV-3T-zg5"/>
                                                <constraint firstItem="Zqg-wL-Xby" firstAttribute="trailing" secondItem="oy4-nh-VrW" secondAttribute="trailingMargin" id="s0r-WE-OlZ"/>
                                                <constraint firstItem="fEr-U8-kO6" firstAttribute="centerY" secondItem="oy4-nh-VrW" secondAttribute="centerY" id="sLd-0R-tEX"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Branch Link" id="YQE-y9-kVv" userLabel="Branch Link">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="SvS-QW-z9B" userLabel="loadLinkPropertiesButton">
                                        <rect key="frame" x="0.0" y="155.33333333333334" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="SvS-QW-z9B" id="L7a-G4-QJ9">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Knu-zU-SC4" userLabel="loadLinkPropertiesButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Load Link Properties from Referring Link"/>
                                                    <connections>
                                                        <action selector="loadLinkPropertiesButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="Mmp-zs-fhP"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Knu-zU-SC4" firstAttribute="trailing" secondItem="L7a-G4-QJ9" secondAttribute="trailingMargin" id="aQ2-2f-xWM"/>
                                                <constraint firstItem="Knu-zU-SC4" firstAttribute="leading" secondItem="L7a-G4-QJ9" secondAttribute="leadingMargin" constant="6" id="n8v-c8-rIV"/>
                                                <constraint firstItem="Knu-zU-SC4" firstAttribute="centerY" secondItem="L7a-G4-QJ9" secondAttribute="centerY" id="y6j-4d-Gtg"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="QtH-si-2sI" userLabel="loadObjectPropertiesButton">
                                        <rect key="frame" x="0.0" y="199.33333333333334" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="QtH-si-2sI" id="sAU-P6-hNZ">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wtf-SJ-zcY" userLabel="loadObjectPropertiesButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Load Object Properties from Referring Link"/>
                                                    <connections>
                                                        <action selector="loadObjectPropertiesButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="Iny-hr-m5Y"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="wtf-SJ-zcY" firstAttribute="trailing" secondItem="sAU-P6-hNZ" secondAttribute="trailingMargin" id="Zff-na-7b3"/>
                                                <constraint firstItem="wtf-SJ-zcY" firstAttribute="leading" secondItem="sAU-P6-hNZ" secondAttribute="leadingMargin" constant="6" id="oVF-dC-yQM"/>
                                                <constraint firstItem="wtf-SJ-zcY" firstAttribute="centerY" secondItem="sAU-P6-hNZ" secondAttribute="centerY" id="xo0-tf-TcU"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="CK1-FW-dDc" userLabel="linkPropertiesLabel">
                                        <rect key="frame" x="0.0" y="243.33333333333334" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="CK1-FW-dDc" id="6oC-U9-o3e">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Link Properties" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="85Q-J4-YOW" userLabel="linkPropertiesLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="365" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="85Q-J4-YOW" firstAttribute="centerY" secondItem="6oC-U9-o3e" secondAttribute="centerY" id="0i8-ZK-U5r"/>
                                                <constraint firstItem="85Q-J4-YOW" firstAttribute="trailing" secondItem="6oC-U9-o3e" secondAttribute="trailingMargin" constant="6" id="IMD-eI-OHg"/>
                                                <constraint firstItem="85Q-J4-YOW" firstAttribute="leading" secondItem="6oC-U9-o3e" secondAttribute="leadingMargin" constant="6" id="Yjq-Kx-Ukn"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="kxS-nO-VHD" userLabel="branchUniversalObjectPropertiesLabel">
                                        <rect key="frame" x="0.0" y="287.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="kxS-nO-VHD" id="x1S-7R-laO">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Branch Universal Object Properties" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5kd-9O-rOf" userLabel="branchUniversalObjectPropertiesLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="365" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="5kd-9O-rOf" firstAttribute="leading" secondItem="x1S-7R-laO" secondAttribute="leadingMargin" constant="6" id="QyA-Tn-wC5"/>
                                                <constraint firstItem="5kd-9O-rOf" firstAttribute="trailing" secondItem="x1S-7R-laO" secondAttribute="trailingMargin" constant="6" id="Yv4-j7-dhc"/>
                                                <constraint firstItem="5kd-9O-rOf" firstAttribute="centerY" secondItem="x1S-7R-laO" secondAttribute="centerY" id="nwA-50-ync"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="6Qn-rk-4ru" userLabel="linkLabel">
                                        <rect key="frame" x="0.0" y="331.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="6Qn-rk-4ru" id="sHq-Fk-nk7">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Branch Link" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7cX-Cx-jOD" userLabel="linkLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="90.666666666666671" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no branch link" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="HbG-sF-3ao" userLabel="linkTextField">
                                                    <rect key="frame" x="104.66666666666666" y="13.666666666666664" width="295.33333333333337" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="7cX-Cx-jOD" firstAttribute="leading" secondItem="sHq-Fk-nk7" secondAttribute="leadingMargin" constant="6" id="8N3-7u-NSQ"/>
                                                <constraint firstItem="HbG-sF-3ao" firstAttribute="leading" secondItem="7cX-Cx-jOD" secondAttribute="trailing" id="Dac-iM-0xx"/>
                                                <constraint firstItem="HbG-sF-3ao" firstAttribute="centerY" secondItem="sHq-Fk-nk7" secondAttribute="centerY" id="Jir-we-OYI"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="HbG-sF-3ao" secondAttribute="trailing" constant="6" id="WzK-9R-xcb"/>
                                                <constraint firstItem="7cX-Cx-jOD" firstAttribute="centerY" secondItem="sHq-Fk-nk7" secondAttribute="centerY" id="t75-2K-R8q"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="wnH-VD-kfe" userLabel="createBranchLinkButton">
                                        <rect key="frame" x="0.0" y="375.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="wnH-VD-kfe" id="W7I-cV-hNt">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tIM-t5-eOr" userLabel="createBranchLinkButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Create Branch Link"/>
                                                    <connections>
                                                        <action selector="createBranchLinkButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="MqE-JV-LhY"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="tIM-t5-eOr" firstAttribute="centerY" secondItem="W7I-cV-hNt" secondAttribute="centerY" id="8gm-oz-LTC"/>
                                                <constraint firstItem="tIM-t5-eOr" firstAttribute="leading" secondItem="W7I-cV-hNt" secondAttribute="leadingMargin" constant="6" id="UJM-sx-QI2"/>
                                                <constraint firstItem="tIM-t5-eOr" firstAttribute="trailing" secondItem="W7I-cV-hNt" secondAttribute="trailingMargin" id="eh7-Ed-dIO"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="kql-1c-PEo" userLabel="createBranchLinkButton">
                                        <rect key="frame" x="0.0" y="419.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="kql-1c-PEo" id="FD0-1E-QM2">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YXt-O2-UJm" userLabel="createBranchLinkButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Share Alias Branch Link"/>
                                                    <connections>
                                                        <action selector="shareAliasActivityViewController:" destination="3bi-5k-7qj" eventType="touchUpInside" id="ibr-Hq-JNL"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="YXt-O2-UJm" firstAttribute="centerY" secondItem="FD0-1E-QM2" secondAttribute="centerY" id="7ja-KL-Tpd"/>
                                                <constraint firstItem="YXt-O2-UJm" firstAttribute="trailing" secondItem="FD0-1E-QM2" secondAttribute="trailingMargin" id="MLd-ah-TdL"/>
                                                <constraint firstItem="YXt-O2-UJm" firstAttribute="leading" secondItem="FD0-1E-QM2" secondAttribute="leadingMargin" constant="6" id="Yr3-gL-Cc8"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Referral Rewards" id="48j-Sc-YF0">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="N1G-oH-hDh">
                                        <rect key="frame" x="0.0" y="519.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="N1G-oH-hDh" id="ENN-Nm-OiH">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards Bucket" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tjd-Pb-122" userLabel="rewardsBucketLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="123.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="default" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="bDu-6h-17H" userLabel="rewardsBucketTextField">
                                                    <rect key="frame" x="137.66666666666663" y="13.666666666666664" width="235.33333333333337" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="bDu-6h-17H" firstAttribute="leading" secondItem="Tjd-Pb-122" secondAttribute="trailing" id="18g-Tx-5JP"/>
                                                <constraint firstItem="Tjd-Pb-122" firstAttribute="centerY" secondItem="ENN-Nm-OiH" secondAttribute="centerY" id="QjF-R4-nBx"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="bDu-6h-17H" secondAttribute="trailing" id="k7o-pt-13z"/>
                                                <constraint firstItem="bDu-6h-17H" firstAttribute="centerY" secondItem="ENN-Nm-OiH" secondAttribute="centerY" id="mpn-hN-6wj"/>
                                                <constraint firstItem="Tjd-Pb-122" firstAttribute="leading" secondItem="ENN-Nm-OiH" secondAttribute="leadingMargin" constant="6" id="vfG-Ws-lVz"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="Onc-wf-G4D">
                                        <rect key="frame" x="0.0" y="563.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Onc-wf-G4D" id="Uw6-OV-oTw">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards Balance of Bucket" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vva-kI-ICr" userLabel="rewardsBalanceOfBucketLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="208" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <activityIndicatorView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" hidesWhenStopped="YES" animating="YES" style="gray" translatesAutoresizingMaskIntoConstraints="NO" id="iWm-9a-YrS" userLabel="activityIndicator">
                                                    <rect key="frame" x="382" y="12" width="24" height="20"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="24" id="yEQ-Y5-3bS"/>
                                                    </constraints>
                                                    <color key="color" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </activityIndicatorView>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="o6J-3u-u1J" userLabel="rewardsBalanceOfBucketTextField">
                                                    <rect key="frame" x="222" y="13.666666666666664" width="184" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="o6J-3u-u1J" firstAttribute="leading" secondItem="Vva-kI-ICr" secondAttribute="trailing" id="5B8-2l-wQV"/>
                                                <constraint firstItem="iWm-9a-YrS" firstAttribute="trailing" secondItem="Uw6-OV-oTw" secondAttribute="trailingMargin" id="H64-NF-2Zh"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="o6J-3u-u1J" secondAttribute="trailing" id="Ifo-MF-hBr"/>
                                                <constraint firstItem="o6J-3u-u1J" firstAttribute="centerY" secondItem="Uw6-OV-oTw" secondAttribute="centerY" id="SIa-JB-nX6"/>
                                                <constraint firstItem="Vva-kI-ICr" firstAttribute="centerY" secondItem="Uw6-OV-oTw" secondAttribute="centerY" id="nlf-Eh-RRg"/>
                                                <constraint firstItem="iWm-9a-YrS" firstAttribute="centerY" secondItem="Uw6-OV-oTw" secondAttribute="centerY" id="rqm-Ud-x5E"/>
                                                <constraint firstItem="Vva-kI-ICr" firstAttribute="leading" secondItem="Uw6-OV-oTw" secondAttribute="leadingMargin" constant="6" id="sAd-vR-WSA"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="xcc-rK-2xk">
                                        <rect key="frame" x="0.0" y="607.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="xcc-rK-2xk" id="MAR-OW-qjj">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="StD-HT-u4H" userLabel="reloadBalanceButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Reload Balance"/>
                                                    <connections>
                                                        <action selector="reloadBalanceButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="g8t-aH-HpI"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="StD-HT-u4H" firstAttribute="centerY" secondItem="MAR-OW-qjj" secondAttribute="centerY" id="E0C-Vq-cYC"/>
                                                <constraint firstItem="StD-HT-u4H" firstAttribute="leading" secondItem="MAR-OW-qjj" secondAttribute="leadingMargin" constant="6" id="kPk-hn-fI4"/>
                                                <constraint firstItem="StD-HT-u4H" firstAttribute="trailing" secondItem="MAR-OW-qjj" secondAttribute="trailingMargin" id="uBM-ru-mY2"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="zOh-9n-jJa">
                                        <rect key="frame" x="0.0" y="651.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="zOh-9n-jJa" id="7Rc-f6-aFE">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reward Points to Redeem" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kRf-7P-KD1" userLabel="rewardPointsToRedeemLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="196" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="5" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="fVR-7S-q91" userLabel="rewardPointsToRedeemTextField">
                                                    <rect key="frame" x="210" y="13.666666666666664" width="163" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="kRf-7P-KD1" firstAttribute="centerY" secondItem="7Rc-f6-aFE" secondAttribute="centerY" id="2nf-lJ-1JU"/>
                                                <constraint firstItem="fVR-7S-q91" firstAttribute="leading" secondItem="kRf-7P-KD1" secondAttribute="trailing" id="3JB-fB-qMJ"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="fVR-7S-q91" secondAttribute="trailing" id="RlS-Nc-9NY"/>
                                                <constraint firstItem="kRf-7P-KD1" firstAttribute="leading" secondItem="7Rc-f6-aFE" secondAttribute="leadingMargin" constant="6" id="XB1-d6-GBW"/>
                                                <constraint firstItem="fVR-7S-q91" firstAttribute="centerY" secondItem="7Rc-f6-aFE" secondAttribute="centerY" id="euJ-Ul-96H"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="jWa-4n-4sH">
                                        <rect key="frame" x="0.0" y="695.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="jWa-4n-4sH" id="9fg-IC-bWp">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OmL-5h-bC6" userLabel="redeemPointsButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Redeem Points"/>
                                                    <connections>
                                                        <action selector="redeemPointsButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="uK1-TH-w8N"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="OmL-5h-bC6" firstAttribute="trailing" secondItem="9fg-IC-bWp" secondAttribute="trailingMargin" id="7HQ-3N-j73"/>
                                                <constraint firstItem="OmL-5h-bC6" firstAttribute="leading" secondItem="9fg-IC-bWp" secondAttribute="leadingMargin" constant="6" id="FDf-LJ-HP5"/>
                                                <constraint firstItem="OmL-5h-bC6" firstAttribute="centerY" secondItem="9fg-IC-bWp" secondAttribute="centerY" id="UsU-Yy-5ju"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="beT-dr-Lmz">
                                        <rect key="frame" x="0.0" y="739.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="beT-dr-Lmz" id="57t-UB-z4S">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rewards History" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bmx-Gi-qdh">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="359" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="bmx-Gi-qdh" secondAttribute="trailing" id="A65-ZL-SO0"/>
                                                <constraint firstItem="bmx-Gi-qdh" firstAttribute="leading" secondItem="57t-UB-z4S" secondAttribute="leadingMargin" constant="6" id="SVM-lE-5IP"/>
                                                <constraint firstItem="bmx-Gi-qdh" firstAttribute="centerY" secondItem="57t-UB-z4S" secondAttribute="centerY" id="dHg-b4-Rnh"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Application Events" id="2wG-zu-ZBM" userLabel="Custom Events">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="R2s-j3-arJ">
                                        <rect key="frame" x="0.0" y="839.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="R2s-j3-arJ" id="A1p-DN-2gS">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Custom Event Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B34-U2-drr" userLabel="customEventNameLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="156.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="default = 'button'" textAlignment="right" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="qqT-nj-v61" userLabel="customEventNameTextField">
                                                    <rect key="frame" x="170.66666666666663" y="13.666666666666664" width="202.33333333333337" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="qqT-nj-v61" secondAttribute="trailing" id="Rez-cL-n3r"/>
                                                <constraint firstItem="B34-U2-drr" firstAttribute="leading" secondItem="A1p-DN-2gS" secondAttribute="leadingMargin" constant="6" id="SZ7-tp-ap5"/>
                                                <constraint firstItem="B34-U2-drr" firstAttribute="centerY" secondItem="A1p-DN-2gS" secondAttribute="centerY" id="eLv-2L-3T2"/>
                                                <constraint firstItem="qqT-nj-v61" firstAttribute="centerY" secondItem="A1p-DN-2gS" secondAttribute="centerY" id="fxu-Ju-hhj"/>
                                                <constraint firstItem="qqT-nj-v61" firstAttribute="leading" secondItem="B34-U2-drr" secondAttribute="trailing" id="t8R-9g-RPI"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" rowHeight="98" id="RhC-hK-F1P">
                                        <rect key="frame" x="0.0" y="883.33333333333337" width="414" height="98"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="RhC-hK-F1P" id="da2-hP-iyc">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="97.666666666666671"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Custom Event Metadata" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oFj-sv-YSF" userLabel="customEventMetadataLabel">
                                                    <rect key="frame" x="14" y="8.0000000000000018" width="359" height="20.666666666666671"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" userInteractionEnabled="NO" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="3MJ-Nk-tjQ" userLabel="customEventMetadataTextView">
                                                    <rect key="frame" x="14" y="28.666666666666671" width="359" height="61"/>
                                                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                </textView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="3MJ-Nk-tjQ" firstAttribute="bottom" secondItem="da2-hP-iyc" secondAttribute="bottomMargin" id="4W3-ej-Rpx"/>
                                                <constraint firstItem="oFj-sv-YSF" firstAttribute="leading" secondItem="da2-hP-iyc" secondAttribute="leadingMargin" constant="6" id="4qb-67-FxA"/>
                                                <constraint firstItem="oFj-sv-YSF" firstAttribute="trailing" secondItem="da2-hP-iyc" secondAttribute="trailingMargin" id="KMT-QK-uKR"/>
                                                <constraint firstItem="3MJ-Nk-tjQ" firstAttribute="top" secondItem="oFj-sv-YSF" secondAttribute="bottom" id="THk-G7-tV2"/>
                                                <constraint firstItem="oFj-sv-YSF" firstAttribute="top" secondItem="da2-hP-iyc" secondAttribute="topMargin" id="WP7-2U-GzE"/>
                                                <constraint firstItem="3MJ-Nk-tjQ" firstAttribute="trailing" secondItem="da2-hP-iyc" secondAttribute="trailingMargin" id="f0B-j5-bTN"/>
                                                <constraint firstItem="3MJ-Nk-tjQ" firstAttribute="leading" secondItem="da2-hP-iyc" secondAttribute="leadingMargin" constant="6" id="fTw-pp-ipS"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="4h4-mT-vbL">
                                        <rect key="frame" x="0.0" y="981.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="4h4-mT-vbL" id="uzM-rl-pnG">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XEx-Pg-EvB" userLabel="sendCustomEventButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Send Custom Event"/>
                                                    <connections>
                                                        <action selector="sendEventButtonTouchUpInside:" destination="3bi-5k-7qj" eventType="touchUpInside" id="ZJc-jT-k0Z"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="XEx-Pg-EvB" firstAttribute="centerY" secondItem="uzM-rl-pnG" secondAttribute="centerY" id="LlV-t2-QEG"/>
                                                <constraint firstItem="XEx-Pg-EvB" firstAttribute="trailing" secondItem="uzM-rl-pnG" secondAttribute="trailingMargin" id="Qa0-tP-dTs"/>
                                                <constraint firstItem="XEx-Pg-EvB" firstAttribute="leading" secondItem="uzM-rl-pnG" secondAttribute="leadingMargin" constant="6" id="pnv-Ev-bWh"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Commerce Events" id="dgF-Kk-bpY" userLabel="Commerce Events">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="5JK-zr-9yS">
                                        <rect key="frame" x="0.0" y="1081.3333333333335" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="5JK-zr-9yS" id="Yib-ZL-gK8">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Commerce Event Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pUj-J7-dAQ" userLabel="commerceEventDetailsLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="359" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="pUj-J7-dAQ" firstAttribute="centerY" secondItem="Yib-ZL-gK8" secondAttribute="centerY" id="77s-fY-wMF"/>
                                                <constraint firstItem="pUj-J7-dAQ" firstAttribute="leading" secondItem="Yib-ZL-gK8" secondAttribute="leadingMargin" constant="6" id="7PW-qI-joZ"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="pUj-J7-dAQ" secondAttribute="trailing" id="gWI-gQ-T8Q"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" rowHeight="98" id="ZCD-Ch-cSv">
                                        <rect key="frame" x="0.0" y="1125.3333333333335" width="414" height="98"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ZCD-Ch-cSv" id="9zk-n5-fio">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="97.666666666666671"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Commerce Event Custom Metadata" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Nuh-Ny-4Lu" userLabel="commerceEventCustomMetadataLabel">
                                                    <rect key="frame" x="14" y="8.0000000000000018" width="359" height="20.666666666666671"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" userInteractionEnabled="NO" contentMode="scaleToFill" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="G51-Jy-DTk" userLabel="commerceEventCustomMetadataTextView">
                                                    <rect key="frame" x="14" y="28.666666666666671" width="359" height="61"/>
                                                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                </textView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="G51-Jy-DTk" firstAttribute="leading" secondItem="9zk-n5-fio" secondAttribute="leadingMargin" constant="6" id="F3Y-XU-qVo"/>
                                                <constraint firstItem="G51-Jy-DTk" firstAttribute="top" secondItem="Nuh-Ny-4Lu" secondAttribute="bottom" id="MPH-2E-vkI"/>
                                                <constraint firstItem="Nuh-Ny-4Lu" firstAttribute="top" secondItem="9zk-n5-fio" secondAttribute="topMargin" id="Mse-AW-2rA"/>
                                                <constraint firstItem="Nuh-Ny-4Lu" firstAttribute="leading" secondItem="9zk-n5-fio" secondAttribute="leadingMargin" constant="6" id="NAT-jG-iqn"/>
                                                <constraint firstItem="G51-Jy-DTk" firstAttribute="bottom" secondItem="9zk-n5-fio" secondAttribute="bottomMargin" id="NfH-3Y-gR3"/>
                                                <constraint firstItem="G51-Jy-DTk" firstAttribute="trailing" secondItem="9zk-n5-fio" secondAttribute="trailingMargin" id="lde-De-r5f"/>
                                                <constraint firstItem="Nuh-Ny-4Lu" firstAttribute="trailing" secondItem="9zk-n5-fio" secondAttribute="trailingMargin" id="vxP-p2-Q7U"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="GQj-Hz-cGA">
                                        <rect key="frame" x="0.0" y="1223.3333333333335" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="GQj-Hz-cGA" id="mCv-Wa-b74">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RBN-p6-5Jp" userLabel="sendCommerceEventButton">
                                                    <rect key="frame" x="14" y="7" width="392" height="30"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                    <state key="normal" title="Send Commerce Event"/>
                                                    <connections>
                                                        <action selector="sendCommerceEvent:" destination="3bi-5k-7qj" eventType="touchUpInside" id="3do-IZ-CAY"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="RBN-p6-5Jp" firstAttribute="leading" secondItem="mCv-Wa-b74" secondAttribute="leadingMargin" constant="6" id="0zX-dx-PWY"/>
                                                <constraint firstItem="RBN-p6-5Jp" firstAttribute="centerY" secondItem="mCv-Wa-b74" secondAttribute="centerY" id="oBb-i7-5c8"/>
                                                <constraint firstItem="RBN-p6-5Jp" firstAttribute="trailing" secondItem="mCv-Wa-b74" secondAttribute="trailingMargin" id="qKX-82-rpX"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Session Data" id="wTB-pX-vJF" userLabel="Session Data">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="2bg-hb-tyv">
                                        <rect key="frame" x="0.0" y="1323.3333333333335" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="2bg-hb-tyv" id="ZZW-75-6KI">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Referred session parameters" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WjO-nN-MvI" userLabel="LatestReferringParamsLabel">
                                                    <rect key="frame" x="16" y="11.666666666666668" width="357" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="WjO-nN-MvI" firstAttribute="leading" secondItem="ZZW-75-6KI" secondAttribute="leadingMargin" constant="8" id="00s-PH-Bdt"/>
                                                <constraint firstItem="WjO-nN-MvI" firstAttribute="centerY" secondItem="ZZW-75-6KI" secondAttribute="centerY" id="1W2-Jr-wdy"/>
                                                <constraint firstItem="WjO-nN-MvI" firstAttribute="trailing" secondItem="ZZW-75-6KI" secondAttribute="trailingMargin" id="kfb-xG-FxF"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="lGg-oI-a6C">
                                        <rect key="frame" x="0.0" y="1367.3333333333335" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="lGg-oI-a6C" id="e5n-P8-PRj">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="First referred session parameters" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aa5-ZX-lSd" userLabel="FirstReferringParams">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="359" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="aa5-ZX-lSd" firstAttribute="centerY" secondItem="e5n-P8-PRj" secondAttribute="centerY" id="E7P-Vz-liv"/>
                                                <constraint firstItem="aa5-ZX-lSd" firstAttribute="trailing" secondItem="e5n-P8-PRj" secondAttribute="trailingMargin" id="QNQ-Wf-1fz"/>
                                                <constraint firstItem="aa5-ZX-lSd" firstAttribute="leading" secondItem="e5n-P8-PRj" secondAttribute="leadingMargin" constant="6" id="V92-1g-V0I"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Active Startup Options" footerTitle="These are the startup options that the application is currently using." id="0TG-eo-h1f" userLabel="Active Startup Options">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="r0n-Xp-wMV" userLabel="Active Branch Key">
                                        <rect key="frame" x="0.0" y="1474.6666666666667" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="r0n-Xp-wMV" id="bZi-ev-ens">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="mN2-PD-5pa" userLabel="activeBranchKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="398" height="17"/>
                                                    <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="calibratedWhite"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="mN2-PD-5pa" firstAttribute="leading" secondItem="bZi-ev-ens" secondAttribute="leadingMargin" constant="6" id="Ut4-2L-clY"/>
                                                <constraint firstItem="mN2-PD-5pa" firstAttribute="centerY" secondItem="bZi-ev-ens" secondAttribute="centerY" id="tid-D2-uSl"/>
                                                <constraint firstItem="mN2-PD-5pa" firstAttribute="trailing" secondItem="bZi-ev-ens" secondAttribute="trailingMargin" constant="6" id="zGd-Pz-chf"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="AYK-ty-BGD" userLabel="Active setDebug Enabled">
                                        <rect key="frame" x="0.0" y="1518.6666666666667" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="AYK-ty-BGD" id="jkD-jr-gTL">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" enabled="NO" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tNm-4D-Ldz" userLabel="activeSetDebugEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="yt2-UI-cDp" userLabel="activeSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="tNm-4D-Ldz" firstAttribute="centerY" secondItem="jkD-jr-gTL" secondAttribute="centerY" id="0QK-zh-3fu"/>
                                                <constraint firstItem="yt2-UI-cDp" firstAttribute="centerY" secondItem="jkD-jr-gTL" secondAttribute="centerY" id="gXb-sD-p96"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="yt2-UI-cDp" secondAttribute="trailing" constant="6" id="usd-xB-Rzg"/>
                                                <constraint firstItem="tNm-4D-Ldz" firstAttribute="leading" secondItem="jkD-jr-gTL" secondAttribute="leadingMargin" constant="6" id="w0Z-1r-tCv"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="Pending Startup Options" footerTitle="These are the startup options that will be aplied when the app is next launched." id="eJq-ig-o16" userLabel="Pending Startup Opions">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="17n-BE-k8Q" userLabel="Pending Branch Key">
                                        <rect key="frame" x="0.0" y="1654" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="17n-BE-k8Q" id="Kpp-bt-Olk">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="hrG-pm-CNP" userLabel="pendingBranchKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="hrG-pm-CNP" firstAttribute="leading" secondItem="Kpp-bt-Olk" secondAttribute="leadingMargin" constant="6" id="VP6-Nb-LnQ"/>
                                                <constraint firstItem="hrG-pm-CNP" firstAttribute="centerY" secondItem="Kpp-bt-Olk" secondAttribute="centerY" id="bmO-tb-ROu"/>
                                                <constraint firstItem="hrG-pm-CNP" firstAttribute="trailing" secondItem="Kpp-bt-Olk" secondAttribute="trailingMargin" constant="6" id="vXm-fH-ati"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="LLY-sm-ax6" userLabel="Pending setDebug Enabled">
                                        <rect key="frame" x="0.0" y="1698" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="LLY-sm-ax6" id="5Zg-Rm-cAc">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TpH-f3-hRP" userLabel="pendingSetDebugEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="mif-Bj-0lk" userLabel="pendingSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="H4c-Rs-uKJ"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="TpH-f3-hRP" firstAttribute="centerY" secondItem="5Zg-Rm-cAc" secondAttribute="centerY" id="5sY-qK-8RP"/>
                                                <constraint firstItem="TpH-f3-hRP" firstAttribute="leading" secondItem="5Zg-Rm-cAc" secondAttribute="leadingMargin" constant="6" id="hO2-5i-EuI"/>
                                                <constraint firstItem="mif-Bj-0lk" firstAttribute="centerY" secondItem="5Zg-Rm-cAc" secondAttribute="centerY" id="kDs-Pk-l68"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="mif-Bj-0lk" secondAttribute="trailing" constant="6" id="pcn-fz-TIW"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="fIl-tv-AMc" userLabel="Pending Adjust Key">
                                        <rect key="frame" x="0.0" y="1742" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="fIl-tv-AMc" id="dGu-Gb-vDh">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="m1i-n9-eq7" userLabel="pendingAdjustKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="m1i-n9-eq7" firstAttribute="centerY" secondItem="dGu-Gb-vDh" secondAttribute="centerY" id="OIW-U1-0ft"/>
                                                <constraint firstItem="m1i-n9-eq7" firstAttribute="trailing" secondItem="dGu-Gb-vDh" secondAttribute="trailingMargin" constant="6" id="Qru-3X-fLI"/>
                                                <constraint firstItem="m1i-n9-eq7" firstAttribute="leading" secondItem="dGu-Gb-vDh" secondAttribute="leadingMargin" constant="6" id="xZG-75-7k6"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="BP4-VR-e2E" userLabel="Pending Adjust Enabled">
                                        <rect key="frame" x="0.0" y="1786" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="BP4-VR-e2E" id="kLb-C0-8ym">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C0A-Jk-qid" userLabel="pendingAdjustEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="cc0-C0-tQh" userLabel="pendingAdjustEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="x28-5q-wLn"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="C0A-Jk-qid" firstAttribute="leading" secondItem="kLb-C0-8ym" secondAttribute="leadingMargin" constant="6" id="2Bm-R8-rg1"/>
                                                <constraint firstItem="C0A-Jk-qid" firstAttribute="centerY" secondItem="kLb-C0-8ym" secondAttribute="centerY" id="Rrb-RT-a9o"/>
                                                <constraint firstItem="cc0-C0-tQh" firstAttribute="centerY" secondItem="kLb-C0-8ym" secondAttribute="centerY" id="jdO-nt-Qdc"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="cc0-C0-tQh" secondAttribute="trailing" constant="6" id="rkM-kv-Sku"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="ygK-FT-aLI" userLabel="Pending Adobe Key">
                                        <rect key="frame" x="0.0" y="1830" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ygK-FT-aLI" id="vQR-Bo-Cnj">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="yEM-tk-Xqp" userLabel="pendingAdobeKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="yEM-tk-Xqp" firstAttribute="trailing" secondItem="vQR-Bo-Cnj" secondAttribute="trailingMargin" constant="6" id="A5X-r4-Dnv"/>
                                                <constraint firstItem="yEM-tk-Xqp" firstAttribute="leading" secondItem="vQR-Bo-Cnj" secondAttribute="leadingMargin" constant="6" id="gUj-pU-ca6"/>
                                                <constraint firstItem="yEM-tk-Xqp" firstAttribute="centerY" secondItem="vQR-Bo-Cnj" secondAttribute="centerY" id="n5L-CN-OiP"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="c7B-st-BVD" userLabel="Pending Adobe Enabled">
                                        <rect key="frame" x="0.0" y="1874" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="c7B-st-BVD" id="7qp-dx-hx0">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nzo-rq-ZWV" userLabel="pendingAdobeEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="w45-pf-xkX" userLabel="pendingSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="V1g-NT-Lz0"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="w45-pf-xkX" firstAttribute="centerY" secondItem="7qp-dx-hx0" secondAttribute="centerY" id="8Nf-V7-EQl"/>
                                                <constraint firstItem="nzo-rq-ZWV" firstAttribute="leading" secondItem="7qp-dx-hx0" secondAttribute="leadingMargin" constant="6" id="8uN-sM-bVf"/>
                                                <constraint firstItem="nzo-rq-ZWV" firstAttribute="centerY" secondItem="7qp-dx-hx0" secondAttribute="centerY" id="IW8-8N-lG8"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="w45-pf-xkX" secondAttribute="trailing" constant="6" id="p8Z-Yd-26N"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="NHa-nY-fk3" userLabel="Pending Amplitude Key">
                                        <rect key="frame" x="0.0" y="1918" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="NHa-nY-fk3" id="zvb-jX-FtA">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="YN6-eI-MUw" userLabel="pendingAmplitudeKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="YN6-eI-MUw" firstAttribute="centerY" secondItem="zvb-jX-FtA" secondAttribute="centerY" id="EVF-XK-JSW"/>
                                                <constraint firstItem="YN6-eI-MUw" firstAttribute="trailing" secondItem="zvb-jX-FtA" secondAttribute="trailingMargin" constant="6" id="d7S-rD-wLR"/>
                                                <constraint firstItem="YN6-eI-MUw" firstAttribute="leading" secondItem="zvb-jX-FtA" secondAttribute="leadingMargin" constant="6" id="eST-RK-FmV"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="5tS-6l-s9Z" userLabel="Pending Amplitude Enabled">
                                        <rect key="frame" x="0.0" y="1962" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="5tS-6l-s9Z" id="aLH-eH-rbv">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Nd-er-2He" userLabel="pendingAmplitudeEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="HaX-EW-JTR" userLabel="pendingAmplitudeEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="PHz-5G-o9R"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="HaX-EW-JTR" secondAttribute="trailing" constant="6" id="IG0-LE-ml6"/>
                                                <constraint firstItem="2Nd-er-2He" firstAttribute="leading" secondItem="aLH-eH-rbv" secondAttribute="leadingMargin" constant="6" id="u2a-3f-b2o"/>
                                                <constraint firstItem="2Nd-er-2He" firstAttribute="centerY" secondItem="aLH-eH-rbv" secondAttribute="centerY" id="ugQ-eA-Z5M"/>
                                                <constraint firstItem="HaX-EW-JTR" firstAttribute="centerY" secondItem="aLH-eH-rbv" secondAttribute="centerY" id="yW7-jT-4ty"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="EEu-Le-QgN" userLabel="Pending Appsflyer Key">
                                        <rect key="frame" x="0.0" y="2006" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="EEu-Le-QgN" id="IIQ-N2-VjO">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="CPv-l1-kAZ" userLabel="pendingAppsflyerKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="CPv-l1-kAZ" firstAttribute="leading" secondItem="IIQ-N2-VjO" secondAttribute="leadingMargin" constant="6" id="6AT-fY-rdw"/>
                                                <constraint firstItem="CPv-l1-kAZ" firstAttribute="centerY" secondItem="IIQ-N2-VjO" secondAttribute="centerY" id="Xx1-1z-Is9"/>
                                                <constraint firstItem="CPv-l1-kAZ" firstAttribute="trailing" secondItem="IIQ-N2-VjO" secondAttribute="trailingMargin" constant="6" id="e9B-st-aJ6"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="S7s-wt-Tij" userLabel="Pending Appsflyer Enabled">
                                        <rect key="frame" x="0.0" y="2050" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="S7s-wt-Tij" id="oTh-1e-dM9">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FjF-we-6Er" userLabel="pendingAppsflyerEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="Xgc-lE-HSb" userLabel="pendingSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="mRR-vh-D7f"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="Xgc-lE-HSb" secondAttribute="trailing" constant="6" id="DD7-Bb-hGh"/>
                                                <constraint firstItem="FjF-we-6Er" firstAttribute="centerY" secondItem="oTh-1e-dM9" secondAttribute="centerY" id="LHZ-pi-MgL"/>
                                                <constraint firstItem="Xgc-lE-HSb" firstAttribute="centerY" secondItem="oTh-1e-dM9" secondAttribute="centerY" id="Zhm-iR-1S1"/>
                                                <constraint firstItem="FjF-we-6Er" firstAttribute="leading" secondItem="oTh-1e-dM9" secondAttribute="leadingMargin" constant="6" id="b4w-sr-2lp"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="PQM-xo-bp1" userLabel="Pending Mixpanel Key">
                                        <rect key="frame" x="0.0" y="2094" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="PQM-xo-bp1" id="LZS-2p-fG4">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="bcV-sG-Qvw" userLabel="pendingBranchKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="bcV-sG-Qvw" firstAttribute="trailing" secondItem="LZS-2p-fG4" secondAttribute="trailingMargin" constant="6" id="ZRP-9p-Usl"/>
                                                <constraint firstItem="bcV-sG-Qvw" firstAttribute="leading" secondItem="LZS-2p-fG4" secondAttribute="leadingMargin" constant="6" id="fu3-dP-ygd"/>
                                                <constraint firstItem="bcV-sG-Qvw" firstAttribute="centerY" secondItem="LZS-2p-fG4" secondAttribute="centerY" id="z9F-QP-p9g"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="e0s-dG-lCZ" userLabel="Pending Mixpanel Enabled">
                                        <rect key="frame" x="0.0" y="2138" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="e0s-dG-lCZ" id="FO1-oT-YvO">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g76-b4-X9g" userLabel="pendingSetDebugEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="Ndx-Wu-KKz" userLabel="pendingSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="t0F-4r-THR"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Ndx-Wu-KKz" firstAttribute="centerY" secondItem="FO1-oT-YvO" secondAttribute="centerY" id="SZS-ec-83b"/>
                                                <constraint firstItem="g76-b4-X9g" firstAttribute="leading" secondItem="FO1-oT-YvO" secondAttribute="leadingMargin" constant="6" id="UBR-o5-kS2"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="Ndx-Wu-KKz" secondAttribute="trailing" constant="6" id="eGl-FP-0al"/>
                                                <constraint firstItem="g76-b4-X9g" firstAttribute="centerY" secondItem="FO1-oT-YvO" secondAttribute="centerY" id="f5g-tM-YLO"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationWidth="10" reuseIdentifier="TableViewCell" id="CrJ-5M-aBa" userLabel="Pending Tune Key">
                                        <rect key="frame" x="0.0" y="2182" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="CrJ-5M-aBa" id="mbs-Vk-ag8">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Branch Key" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="DBe-97-1Tm" userLabel="pendingBranchKeyTextField">
                                                    <rect key="frame" x="14" y="13.666666666666664" width="365" height="17"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="DBe-97-1Tm" firstAttribute="leading" secondItem="mbs-Vk-ag8" secondAttribute="leadingMargin" constant="6" id="2t0-f9-jMB"/>
                                                <constraint firstItem="DBe-97-1Tm" firstAttribute="trailing" secondItem="mbs-Vk-ag8" secondAttribute="trailingMargin" constant="6" id="fb6-LL-D4b"/>
                                                <constraint firstItem="DBe-97-1Tm" firstAttribute="centerY" secondItem="mbs-Vk-ag8" secondAttribute="centerY" id="pns-U7-eOO"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="m7Y-j2-ofQ" userLabel="Pending Tune Enabled">
                                        <rect key="frame" x="0.0" y="2226" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="m7Y-j2-ofQ" id="fPQ-hB-XSG">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="setDebug Enabled" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ozq-kB-ViK" userLabel="pendingSetDebugEnabledLabel">
                                                    <rect key="frame" x="14" y="11.666666666666668" width="140.66666666666666" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="6Om-nB-5Dk" userLabel="pendingSetDebugEnabledSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                    <connections>
                                                        <action selector="pendingSetDebugEnabledButtonValueChanged:" destination="3bi-5k-7qj" eventType="valueChanged" id="0xI-Bg-e4r"/>
                                                    </connections>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="ozq-kB-ViK" firstAttribute="leading" secondItem="fPQ-hB-XSG" secondAttribute="leadingMargin" constant="6" id="2CY-YV-8fY"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="6Om-nB-5Dk" secondAttribute="trailing" constant="6" id="8ML-mj-aHZ"/>
                                                <constraint firstItem="6Om-nB-5Dk" firstAttribute="centerY" secondItem="fPQ-hB-XSG" secondAttribute="centerY" id="S4Z-Ar-Ewl"/>
                                                <constraint firstItem="ozq-kB-ViK" firstAttribute="centerY" secondItem="fPQ-hB-XSG" secondAttribute="centerY" id="pIc-ai-vam"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="3bi-5k-7qj" id="pcM-rQ-x2d"/>
                            <outlet property="delegate" destination="3bi-5k-7qj" id="y6n-As-JYl"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="TestBed-Swift" id="yvH-no-ZUh" userLabel="Branch-TestBed">
                        <barButtonItem key="rightBarButtonItem" systemItem="action" id="JKY-IE-iMg" userLabel="actionButton">
                            <connections>
                                <action selector="shareBranchLinkAction:" destination="3bi-5k-7qj" id="KiG-de-g3F"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="actionButton" destination="JKY-IE-iMg" id="IVY-XA-sKp"/>
                        <outlet property="activeBranchKeyTextField" destination="mN2-PD-5pa" id="Dy5-Xa-oPZ"/>
                        <outlet property="activeSetDebugEnabledSwitch" destination="yt2-UI-cDp" id="WBE-mX-FFr"/>
                        <outlet property="activityIndicator" destination="iWm-9a-YrS" id="rgY-wp-brq"/>
                        <outlet property="commerceEventCustomMetadataTextView" destination="G51-Jy-DTk" id="jV9-kW-fDJ"/>
                        <outlet property="customEventMetadataTextView" destination="3MJ-Nk-tjQ" id="xv6-06-sD9"/>
                        <outlet property="customEventNameTextField" destination="qqT-nj-v61" id="WzD-du-fH5"/>
                        <outlet property="linkTextField" destination="HbG-sF-3ao" id="PZV-Kl-bBm"/>
                        <outlet property="loadLinkPropertiesButton" destination="Knu-zU-SC4" id="zAs-nR-vBJ"/>
                        <outlet property="loadObjectPropertiesButton" destination="wtf-SJ-zcY" id="c1C-qE-QQq"/>
                        <outlet property="pendingBranchKeyTextField" destination="hrG-pm-CNP" id="eJl-fj-uDq"/>
                        <outlet property="pendingSetDebugEnabledSwitch" destination="mif-Bj-0lk" id="vTq-SK-f9j"/>
                        <outlet property="rewardPointsToRedeemTextField" destination="fVR-7S-q91" id="YLa-p0-Z19"/>
                        <outlet property="rewardsBalanceOfBucketTextField" destination="o6J-3u-u1J" id="pJR-Km-nPD"/>
                        <outlet property="rewardsBucketTextField" destination="bDu-6h-17H" id="6Vd-Gj-Ksf"/>
                        <outlet property="userIDTextField" destination="Zqg-wL-Xby" id="Yqk-PY-xpm"/>
                        <segue destination="hjV-EB-GfF" kind="show" identifier="ShowCreditHistoryTableView" id="eyz-BQ-5N5">
                            <nil key="action"/>
                        </segue>
                        <segue destination="UBk-EH-q5i" kind="show" identifier="ShowContentView" id="IG7-3K-NVU">
                            <nil key="action"/>
                        </segue>
                        <segue destination="QY6-4D-Gxe" kind="show" identifier="ShowLinkPropertiesTableView" id="1R6-Nu-a4f"/>
                        <segue destination="WPU-Ft-SWB" kind="presentation" identifier="ShowTextViewFormNavigationBar" id="wdJ-h5-nCf"/>
                        <segue destination="lNP-uJ-cup" kind="show" identifier="ShowDictionaryTableView" id="fTI-yI-jdX"/>
                        <segue destination="fpK-rx-X9D" kind="show" identifier="ShowBranchUniversalObjectPropertiesTableView" id="Omb-KL-HJx"/>
                        <segue destination="Aqu-O9-8Kl" kind="show" identifier="ShowTextViewFormTableView" id="7TN-Vi-Tcv"/>
                        <segue destination="l7T-5a-FG6" kind="show" identifier="ShowCommerceEventDetailsTableView" id="Aop-9C-bja"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="rM7-0l-bIh" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2295.1999999999998" y="26.53673163418291"/>
        </scene>
        <!--LinkPropertiesTableView-->
        <scene sceneID="cV0-cg-vVF">
            <objects>
                <tableViewController storyboardIdentifier="LinkProperties" useStoryboardIdentifierAsRestorationIdentifier="YES" id="QY6-4D-Gxe" userLabel="LinkPropertiesTableView" customClass="LinkPropertiesTableViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="top" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="none" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" id="NXA-2i-shU">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection id="EKi-k1-qSM" userLabel="clearAllValues">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="ZQ5-85-OUb">
                                        <rect key="frame" x="0.0" y="35" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ZQ5-85-OUb" id="Sup-gj-nMj">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xaO-ts-SYE" userLabel="clearAllValuesButton">
                                                    <rect key="frame" x="14" y="7" width="386" height="30"/>
                                                    <state key="normal" title="Clear all values"/>
                                                    <connections>
                                                        <action selector="clearAllValuesTouchUpInside:" destination="QY6-4D-Gxe" eventType="touchUpInside" id="FEg-39-iyS"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="xaO-ts-SYE" secondAttribute="trailing" constant="6" id="Fvb-zM-MHB"/>
                                                <constraint firstItem="xaO-ts-SYE" firstAttribute="leading" secondItem="Sup-gj-nMj" secondAttribute="leadingMargin" constant="6" id="NFm-9v-qUr"/>
                                                <constraint firstItem="xaO-ts-SYE" firstAttribute="centerY" secondItem="Sup-gj-nMj" secondAttribute="centerY" id="aad-8P-3mc"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="channel" id="7i1-Mo-GVf" userLabel="channel">
                                <string key="footerTitle">Use channel to tag the route that your link reaches users. For example, tag links with ‘Facebook’ or ‘LinkedIn’ to help track clicks and installs through those paths separately.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="7xK-3f-h7M">
                                        <rect key="frame" x="0.0" y="142.33333333333334" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="7xK-3f-h7M" id="6y9-vg-xRl">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="2Uy-GD-t6z" userLabel="channelTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="2Uy-GD-t6z" firstAttribute="centerY" secondItem="6y9-vg-xRl" secondAttribute="centerY" id="6Mp-f6-ZCi"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="2Uy-GD-t6z" secondAttribute="trailing" constant="6" id="HNx-MM-3T5"/>
                                                <constraint firstItem="2Uy-GD-t6z" firstAttribute="leading" secondItem="6y9-vg-xRl" secondAttribute="leadingMargin" constant="6" id="LsR-Rd-rTR"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="feature" id="afB-2q-xg9" userLabel="feature">
                                <string key="footerTitle">This is the feature of your app that the link might be associated with. For example, if you had built a referral program, you would label links with the feature: 'referral'</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Ggg-qN-hjW">
                                        <rect key="frame" x="0.0" y="293.66666666666669" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Ggg-qN-hjW" id="kCh-Ht-Z8Q">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="xs7-5a-g2w" userLabel="featureTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="xs7-5a-g2w" firstAttribute="centerY" secondItem="kCh-Ht-Z8Q" secondAttribute="centerY" id="LyJ-k1-HMw"/>
                                                <constraint firstItem="xs7-5a-g2w" firstAttribute="leading" secondItem="kCh-Ht-Z8Q" secondAttribute="leadingMargin" constant="6" id="fZA-3U-g58"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="xs7-5a-g2w" secondAttribute="trailing" constant="6" id="gCF-NN-04S"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="campaign" id="4Vh-xe-lxD" userLabel="campaign">
                                <string key="footerTitle">Use this field to organize the links by marketing campaign. For example, if you launched a new feature or product and want to run a campaign around that</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="aLS-Le-CA2">
                                        <rect key="frame" x="0.0" y="445" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="aLS-Le-CA2" id="Nq9-WT-bQj">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="5ya-nz-Zg6" userLabel="campaignTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="5ya-nz-Zg6" firstAttribute="centerY" secondItem="Nq9-WT-bQj" secondAttribute="centerY" id="e2W-Na-nG7"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="5ya-nz-Zg6" secondAttribute="trailing" constant="6" id="fmd-Hd-FMN"/>
                                                <constraint firstItem="5ya-nz-Zg6" firstAttribute="leading" secondItem="Nq9-WT-bQj" secondAttribute="leadingMargin" constant="6" id="mBW-Qy-TmZ"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="stage" id="TFg-M4-MGB" userLabel="stage">
                                <string key="footerTitle">Use this to categorize the progress or category of a user when the link was generated. For example, if you had an invite system accessible on level 1, level 3 and 5, you could differentiate links generated at each level with this parameter</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="rGi-aV-FEc">
                                        <rect key="frame" x="0.0" y="596.33333333333337" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="rGi-aV-FEc" id="8ib-uo-Z71">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="RKf-cA-BTP" userLabel="stageTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="398" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="leading" secondItem="8ib-uo-Z71" secondAttribute="leadingMargin" constant="6" id="E6k-I8-id3"/>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="centerY" secondItem="8ib-uo-Z71" secondAttribute="centerY" id="fAT-bb-KOK"/>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="trailing" secondItem="8ib-uo-Z71" secondAttribute="trailingMargin" constant="6" id="omG-fC-fXe"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="tags" id="Err-LV-0GX" userLabel="tags">
                                <string key="footerTitle">This is a free form entry with unlimited values. Use it to organize your link data with labels that don’t fit within the bounds of the above.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" accessoryType="disclosureIndicator" indentationWidth="10" reuseIdentifier="Table View Cell" rowHeight="98" id="APe-MJ-HaR">
                                        <rect key="frame" x="0.0" y="763.66666666666674" width="414" height="98"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="APe-MJ-HaR" id="LjI-5M-9ae">
                                            <rect key="frame" x="0.0" y="0.0" width="381" height="98"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" userInteractionEnabled="NO" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="gyD-DA-0yx" userLabel="tagsTextView">
                                                    <rect key="frame" x="14" y="8" width="353" height="82"/>
                                                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                </textView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottomMargin" secondItem="gyD-DA-0yx" secondAttribute="bottom" id="KFz-Ko-1zL"/>
                                                <constraint firstItem="gyD-DA-0yx" firstAttribute="leading" secondItem="LjI-5M-9ae" secondAttribute="leadingMargin" constant="6" id="Uv1-ii-8gj"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="gyD-DA-0yx" secondAttribute="trailing" constant="6" id="kgR-Eg-Ltb"/>
                                                <constraint firstItem="gyD-DA-0yx" firstAttribute="top" secondItem="LjI-5M-9ae" secondAttribute="topMargin" id="njj-ON-Y9J"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="alias" id="LXz-Cj-rY4" userLabel="alias">
                                <string key="footerTitle">Specify a link alias in place of the standard encoded short URL (e.g., [branchsubdomain]/youralias or yourdomain.co/youralias). Link aliases are unique, immutable objects that cannot be deleted.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="0CU-dh-acg">
                                        <rect key="frame" x="0.0" y="969.00000000000011" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="0CU-dh-acg" id="DR7-gC-lNM">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="V5w-iZ-snS" userLabel="aliasTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="V5w-iZ-snS" secondAttribute="trailing" constant="6" id="LOh-Kc-Z90"/>
                                                <constraint firstItem="V5w-iZ-snS" firstAttribute="centerY" secondItem="DR7-gC-lNM" secondAttribute="centerY" id="lGd-yU-26G"/>
                                                <constraint firstItem="V5w-iZ-snS" firstAttribute="leading" secondItem="DR7-gC-lNM" secondAttribute="leadingMargin" constant="6" id="zEt-LH-Lzu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$fallback_url" id="byU-yu-B8J" userLabel="$fallback_url">
                                <string key="footerTitle">For all platforms, where to send the user when the app is not installed. Note that Branch will forward all robots to this URL, overriding any OG tags entered in the link.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="zfQ-mA-UsS">
                                        <rect key="frame" x="0.0" y="1136.3333333333333" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="zfQ-mA-UsS" id="U7b-AE-TvB">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="uqh-Zw-oed" userLabel="fallbackURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="398" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="leading" secondItem="U7b-AE-TvB" secondAttribute="leadingMargin" constant="6" id="HMD-ZN-U48"/>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="trailing" secondItem="U7b-AE-TvB" secondAttribute="trailingMargin" constant="6" id="Vwd-h2-gUV"/>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="centerY" secondItem="U7b-AE-TvB" secondAttribute="centerY" id="y6b-dm-NG1"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$desktop_url" footerTitle="Where to send the user on a desktop or laptop. If not set, this defaults to the Branch-hosted text-me-the-app page." id="mav-4d-wu7" userLabel="$desktop_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="15Y-39-rVu">
                                        <rect key="frame" x="0.0" y="1287.6666666666665" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="15Y-39-rVu" id="h8a-Jo-3BI">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses text-me-the-app page if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="aVa-wl-KRF" userLabel="desktopURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="aVa-wl-KRF" firstAttribute="leading" secondItem="h8a-Jo-3BI" secondAttribute="leadingMargin" constant="6" id="66Q-Mu-B8l"/>
                                                <constraint firstItem="aVa-wl-KRF" firstAttribute="centerY" secondItem="h8a-Jo-3BI" secondAttribute="centerY" id="B0G-ED-KqC"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="aVa-wl-KRF" secondAttribute="trailing" constant="6" id="gkW-Oc-31A"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_url" footerTitle="If set, iPhone users who do not have the app installed will be redirected to this URL instead of the App Store" id="bDA-1G-xNs" userLabel="$ios_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="xg7-fZ-sky">
                                        <rect key="frame" x="0.0" y="1422.9999999999998" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="xg7-fZ-sky" id="NhZ-dh-h3x">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="xtG-OZ-0tp" userLabel="iosURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="xtG-OZ-0tp" firstAttribute="centerY" secondItem="NhZ-dh-h3x" secondAttribute="centerY" id="AJU-LD-90D"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="xtG-OZ-0tp" secondAttribute="trailing" constant="6" id="JC6-TL-YYc"/>
                                                <constraint firstItem="xtG-OZ-0tp" firstAttribute="leading" secondItem="NhZ-dh-h3x" secondAttribute="leadingMargin" constant="6" id="cAB-G3-bon"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ipad_url" id="HfC-OP-Dtg" userLabel="$ipad_url">
                                <string key="footerTitle">iPad users who do not have the app installed will be redirected to this URL instead of the App Store. Defaults to $ios_url if not specified.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="btX-iu-MeG">
                                        <rect key="frame" x="0.0" y="1558.333333333333" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="btX-iu-MeG" id="qMO-mh-mfo">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="UV6-Os-JFv" userLabel="ipadURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="UV6-Os-JFv" firstAttribute="centerY" secondItem="qMO-mh-mfo" secondAttribute="centerY" id="6kb-1S-Kdm"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="UV6-Os-JFv" secondAttribute="trailing" constant="6" id="VEl-Bb-paj"/>
                                                <constraint firstItem="UV6-Os-JFv" firstAttribute="leading" secondItem="qMO-mh-mfo" secondAttribute="leadingMargin" constant="6" id="iv4-zd-Ti7"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_url" footerTitle="If set, Android users who do not have the app installed will be redirected to this URL instead of the Play Store" id="SCe-A0-nai" userLabel="$android_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Qns-Za-x3O">
                                        <rect key="frame" x="0.0" y="1709.6666666666663" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Qns-Za-x3O" id="NIe-mw-1VA">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="6aC-yM-ART" userLabel="androidURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="6aC-yM-ART" firstAttribute="centerY" secondItem="NIe-mw-1VA" secondAttribute="centerY" id="Fr0-jq-sks"/>
                                                <constraint firstItem="6aC-yM-ART" firstAttribute="leading" secondItem="NIe-mw-1VA" secondAttribute="leadingMargin" constant="6" id="KQy-fc-tYf"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="6aC-yM-ART" secondAttribute="trailing" constant="6" id="uxe-pO-nJm"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$windows_phone_url" footerTitle="If set, Windows phone users who do not have the app installed will be redirected to this URL" id="Mlo-wj-AFO" userLabel="$windows_phone_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="59F-yN-gxT">
                                        <rect key="frame" x="0.0" y="1844.9999999999995" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="59F-yN-gxT" id="2CZ-hn-TTz">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="W9Y-Kz-ydz" userLabel="windowsPhoneURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="W9Y-Kz-ydz" secondAttribute="trailing" constant="6" id="4ts-PH-GhC"/>
                                                <constraint firstItem="W9Y-Kz-ydz" firstAttribute="leading" secondItem="2CZ-hn-TTz" secondAttribute="leadingMargin" constant="6" id="CFw-dJ-24u"/>
                                                <constraint firstItem="W9Y-Kz-ydz" firstAttribute="centerY" secondItem="2CZ-hn-TTz" secondAttribute="centerY" id="cxf-5y-Mt5"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$blackberry_url" footerTitle="If set, Blackberry users who do not have the app installed will be redirected to this URL" id="O0i-QT-fCA" userLabel="$blackberry_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="pMa-C6-b2W">
                                        <rect key="frame" x="0.0" y="1980.3333333333328" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="pMa-C6-b2W" id="XhU-Ip-UTp">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="0L3-oL-gau" userLabel="blackberryURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="0L3-oL-gau" secondAttribute="trailing" constant="6" id="Jdt-4F-IWt"/>
                                                <constraint firstItem="0L3-oL-gau" firstAttribute="leading" secondItem="XhU-Ip-UTp" secondAttribute="leadingMargin" constant="6" id="kcc-Oe-QmX"/>
                                                <constraint firstItem="0L3-oL-gau" firstAttribute="centerY" secondItem="XhU-Ip-UTp" secondAttribute="centerY" id="vtt-b2-b9i"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$fire_url" footerTitle="If set, Amazon Fire users who do not have the app installed will be redirected to this URL" id="w7n-c0-n0S" userLabel="$fire_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Z3k-dM-uIW">
                                        <rect key="frame" x="0.0" y="2115.6666666666665" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Z3k-dM-uIW" id="fXr-qw-CzT">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="qv3-9n-yTX" userLabel="fireURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="qv3-9n-yTX" firstAttribute="centerY" secondItem="fXr-qw-CzT" secondAttribute="centerY" id="9B1-ZL-MJu"/>
                                                <constraint firstItem="qv3-9n-yTX" firstAttribute="leading" secondItem="fXr-qw-CzT" secondAttribute="leadingMargin" constant="6" id="lhc-GS-8cR"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="qv3-9n-yTX" secondAttribute="trailing" constant="6" id="m6H-Sq-hMR"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_wechat_url" footerTitle="Change the redirect endpoint for WeChat on iOS devices." id="sjT-KP-uMM" userLabel="$ios_wechat_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="WMg-PC-rjl">
                                        <rect key="frame" x="0.0" y="2251" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="WMg-PC-rjl" id="tbh-ps-0ZA">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="B5l-lH-KbQ" userLabel="iosWeChatURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="B5l-lH-KbQ" secondAttribute="trailing" constant="6" id="9iY-7L-Y7m"/>
                                                <constraint firstItem="B5l-lH-KbQ" firstAttribute="centerY" secondItem="tbh-ps-0ZA" secondAttribute="centerY" id="Enm-Zn-LId"/>
                                                <constraint firstItem="B5l-lH-KbQ" firstAttribute="leading" secondItem="tbh-ps-0ZA" secondAttribute="leadingMargin" constant="6" id="mtG-8L-l2U"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_weibo_url" footerTitle="Change the redirect endpoint for Weibo on iOS devices." id="9Wh-og-Q5D" userLabel="$ios_weibo_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="oau-oZ-equ">
                                        <rect key="frame" x="0.0" y="2370.3333333333335" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="oau-oZ-equ" id="m3u-4y-Lnt">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="z2M-Gb-GHr" userLabel="iosWeiboURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="z2M-Gb-GHr" firstAttribute="centerY" secondItem="m3u-4y-Lnt" secondAttribute="centerY" id="d4Z-5f-nUx"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="z2M-Gb-GHr" secondAttribute="trailing" constant="6" id="i5o-ny-6cg"/>
                                                <constraint firstItem="z2M-Gb-GHr" firstAttribute="leading" secondItem="m3u-4y-Lnt" secondAttribute="leadingMargin" constant="6" id="wyI-uV-wKi"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$after_click_url" id="dvM-KR-Q9Z" userLabel="$after_click_url">
                                <string key="footerTitle">When a user returns to the browser after the app was successfully opened they will be redirected to this URL. This currently works for iOS only; Android support is coming soon.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="PD6-90-h16">
                                        <rect key="frame" x="0.0" y="2489.666666666667" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="PD6-90-h16" id="3Pa-m9-yOi">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="axt-rS-OdB" userLabel="afterClickURLTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="axt-rS-OdB" firstAttribute="centerY" secondItem="3Pa-m9-yOi" secondAttribute="centerY" id="TT3-nI-wQz"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="axt-rS-OdB" secondAttribute="trailing" constant="6" id="jdA-gP-as2"/>
                                                <constraint firstItem="axt-rS-OdB" firstAttribute="leading" secondItem="3Pa-m9-yOi" secondAttribute="leadingMargin" constant="6" id="xAC-FM-Vwu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$web_only" id="Wkl-Ez-ZJ9" userLabel="$web_only">
                                <string key="footerTitle">This lets you direct the user to the web, even if they have the app installed. When creating the link, add $web_only: true to the deep link data.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="ab3-wA-rVV">
                                        <rect key="frame" x="0.0" y="2641.0000000000005" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ab3-wA-rVV" id="UxC-Ph-OHb">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$web_only" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HH5-XO-0GK" userLabel="webOnlyLabel">
                                                    <rect key="frame" x="14" y="12" width="81.666666666666671" height="20.666666666666664"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="2ZF-xX-urF" userLabel="webOnlySwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="2ZF-xX-urF" secondAttribute="trailing" constant="6" id="J29-QF-R98"/>
                                                <constraint firstItem="2ZF-xX-urF" firstAttribute="centerY" secondItem="UxC-Ph-OHb" secondAttribute="centerY" id="Uas-Ps-l5i"/>
                                                <constraint firstItem="HH5-XO-0GK" firstAttribute="leading" secondItem="UxC-Ph-OHb" secondAttribute="leadingMargin" constant="6" id="Z5k-o7-x7h"/>
                                                <constraint firstItem="HH5-XO-0GK" firstAttribute="centerY" secondItem="UxC-Ph-OHb" secondAttribute="centerY" id="xgP-Xh-hQ4"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$deeplink_path" id="tTv-KE-3Du" userLabel="$deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier".

Universal Links and Spotlight do not support deep linking via URI paths. We recommend not using $deeplink_path as your only deep link routing method.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="fCK-Qh-CtF">
                                        <rect key="frame" x="0.0" y="2792.*************" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="fCK-Qh-CtF" id="Bwk-qj-sH3">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="open?link_click_id={Branch link ID}" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="ZXD-aO-bzl" userLabel="deeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="ZXD-aO-bzl" firstAttribute="centerY" secondItem="Bwk-qj-sH3" secondAttribute="centerY" id="J94-yz-aer"/>
                                                <constraint firstItem="ZXD-aO-bzl" firstAttribute="leading" secondItem="Bwk-qj-sH3" secondAttribute="leadingMargin" constant="6" id="JsA-wp-Apg"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="ZXD-aO-bzl" secondAttribute="trailing" constant="6" id="SnQ-N6-RGu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_deeplink_path" id="c6z-mL-1q7" userLabel="$android_deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme on Android. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier". This is primarily for supporting legacy deep linking infrastructure.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="jWJ-o0-iwq">
                                        <rect key="frame" x="0.0" y="3039.6666666666674" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="jWJ-o0-iwq" id="kfw-Yq-NeY">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $deeplink_path if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="1ml-G3-TTc" userLabel="androidDeeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="1ml-G3-TTc" firstAttribute="centerY" secondItem="kfw-Yq-NeY" secondAttribute="centerY" id="BjK-YC-LdE"/>
                                                <constraint firstItem="1ml-G3-TTc" firstAttribute="leading" secondItem="kfw-Yq-NeY" secondAttribute="leadingMargin" constant="6" id="duY-y2-MqP"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="1ml-G3-TTc" secondAttribute="trailing" constant="6" id="wh2-RF-7rW"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_deeplink_path" id="WDS-uN-hVP" userLabel="$ios_deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme on iOS. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier". This is primarily for supporting legacy deep linking infrastructure.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="q88-TY-fdl">
                                        <rect key="frame" x="0.0" y="3239.0000000000009" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="q88-TY-fdl" id="wWM-jY-wSi">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $deeplink_path if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="LAh-Yf-YoI" userLabel="iosDeeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="LAh-Yf-YoI" firstAttribute="leading" secondItem="wWM-jY-wSi" secondAttribute="leadingMargin" constant="6" id="7aq-up-U0E"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="LAh-Yf-YoI" secondAttribute="trailing" constant="6" id="AOj-nr-RYo"/>
                                                <constraint firstItem="LAh-Yf-YoI" firstAttribute="centerY" secondItem="wWM-jY-wSi" secondAttribute="centerY" id="H4Y-Wm-yiG"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$match_duration" footerTitle="The attribution window, in seconds, for clicks coming from this link." id="qLw-wc-jH4" userLabel="$match_duration">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="lG2-MY-xYa">
                                        <rect key="frame" x="0.0" y="3438.3333333333344" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="lG2-MY-xYa" id="gWE-sE-iim">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="7200" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="AbC-iA-R9W" userLabel="matchDurationTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="AbC-iA-R9W" secondAttribute="trailing" constant="6" id="C1W-VU-PKl"/>
                                                <constraint firstItem="AbC-iA-R9W" firstAttribute="leading" secondItem="gWE-sE-iim" secondAttribute="leadingMargin" constant="6" id="nWf-cC-Pjb"/>
                                                <constraint firstItem="AbC-iA-R9W" firstAttribute="centerY" secondItem="gWE-sE-iim" secondAttribute="centerY" id="uQN-kW-oYt"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$always_deeplink" id="vrQ-KC-Olf" userLabel="$always_deeplink">
                                <string key="footerTitle">If true, an attempt to open the app will be made, even if Branch has not seen the device before and is therefore unsure whether the user has the app installed or not. If the app is not installed, we fall back to the respective app store or $platform_url key. By default, we only open the app if we've seen a user initiate a session in your app from a Branch link (has been cookied and deep linked by Branch).</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="jku-gt-pm6">
                                        <rect key="frame" x="0.0" y="3573.6666666666679" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="jku-gt-pm6" id="Iny-NN-Sb7">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$always_deeplink" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7dv-Cr-65l" userLabel="alwaysDeeplinkLabel">
                                                    <rect key="frame" x="14" y="12" width="133.66666666666666" height="20.666666666666664"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="DNt-GX-Ykb" userLabel="alwaysDeeplinkSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="7dv-Cr-65l" firstAttribute="centerY" secondItem="Iny-NN-Sb7" secondAttribute="centerY" id="9jQ-Go-AKZ"/>
                                                <constraint firstItem="7dv-Cr-65l" firstAttribute="leading" secondItem="Iny-NN-Sb7" secondAttribute="leadingMargin" constant="6" id="KVD-7c-Gbg"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="DNt-GX-Ykb" secondAttribute="trailing" constant="6" id="Os9-lP-LOa"/>
                                                <constraint firstItem="DNt-GX-Ykb" firstAttribute="centerY" secondItem="Iny-NN-Sb7" secondAttribute="centerY" id="oUs-ra-EVm"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_redirect_timeout" id="efw-QV-XuG" userLabel="$ios_redirect_timeout">
                                <string key="footerTitle">Control the timeout that the client-side JS waits after trying to open up the app before redirecting to the App Store. Specified in milliseconds.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="AZi-ak-ckt">
                                        <rect key="frame" x="0.0" y="3789.0000000000014" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="AZi-ak-ckt" id="9X8-eK-Beo">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="750" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="5mb-VU-Zzb" userLabel="iosRedirectTimeoutTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="5mb-VU-Zzb" secondAttribute="trailing" constant="6" id="Rqo-Tk-3LD"/>
                                                <constraint firstItem="5mb-VU-Zzb" firstAttribute="centerY" secondItem="9X8-eK-Beo" secondAttribute="centerY" id="afW-VR-t9z"/>
                                                <constraint firstItem="5mb-VU-Zzb" firstAttribute="leading" secondItem="9X8-eK-Beo" secondAttribute="leadingMargin" constant="6" id="uZV-17-m5e"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_redirect_timeout" id="E6q-bs-YYv" userLabel="$android_redirect_timeout">
                                <string key="footerTitle">Control the timeout that the clientside JS waits after trying to open up the app before redirecting to the Play Store. Specified in milliseconds.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="9hk-bs-r2Z">
                                        <rect key="frame" x="0.0" y="3940.3333333333348" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="9hk-bs-r2Z" id="hyA-t0-5YY">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="750" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="048-Ef-qL4" userLabel="androidRedirectTimeoutTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="048-Ef-qL4" firstAttribute="leading" secondItem="hyA-t0-5YY" secondAttribute="leadingMargin" constant="6" id="XIp-q4-7Pc"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="048-Ef-qL4" secondAttribute="trailing" constant="6" id="cbl-xX-IM9"/>
                                                <constraint firstItem="048-Ef-qL4" firstAttribute="centerY" secondItem="hyA-t0-5YY" secondAttribute="centerY" id="iu5-z4-2zG"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$one_time_use" id="RRr-ar-pIJ" userLabel="$one_time_use">
                                <string key="footerTitle">Set to ‘true’ to limit deep linking behavior of the generated link to a single use. Can also be set using type.

Default value: 0 (false)</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Z8G-hj-ozT">
                                        <rect key="frame" x="0.0" y="4091.6666666666683" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Z8G-hj-ozT" id="oyh-Zv-C5b">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$one_time_use" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L8j-nr-P9v" userLabel="oneTimeUseLabel">
                                                    <rect key="frame" x="14.000000000000007" y="12" width="113.66666666666669" height="20.666666666666664"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="d0s-hS-3km" userLabel="oneTimeUseSwitch">
                                                    <rect key="frame" x="351" y="6.6666666666666661" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="d0s-hS-3km" secondAttribute="trailing" constant="6" id="3sc-FB-1rr"/>
                                                <constraint firstItem="d0s-hS-3km" firstAttribute="centerY" secondItem="oyh-Zv-C5b" secondAttribute="centerY" id="FmD-JH-KGL"/>
                                                <constraint firstItem="L8j-nr-P9v" firstAttribute="leading" secondItem="oyh-Zv-C5b" secondAttribute="leadingMargin" constant="6" id="VF8-pQ-X1y"/>
                                                <constraint firstItem="L8j-nr-P9v" firstAttribute="centerY" secondItem="oyh-Zv-C5b" secondAttribute="centerY" id="kod-FV-eqs"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_deepview" id="jDR-kd-f3X" userLabel="$ios_deepview">
                                <string key="footerTitle">The name of the deepview template to use for iOS.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="J3z-Yx-eTT">
                                        <rect key="frame" x="0.0" y="4259.0000000000009" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="J3z-Yx-eTT" id="eNJ-3E-4SV">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="XC3-ID-lnH" userLabel="iosDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="XC3-ID-lnH" firstAttribute="centerY" secondItem="eNJ-3E-4SV" secondAttribute="centerY" id="Xn8-ey-pYS"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="XC3-ID-lnH" secondAttribute="trailing" constant="6" id="bUg-Iz-nbO"/>
                                                <constraint firstItem="XC3-ID-lnH" firstAttribute="leading" secondItem="eNJ-3E-4SV" secondAttribute="leadingMargin" constant="6" id="eQc-BQ-kIs"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_deepview" id="ocl-Uw-aZW" userLabel="$android_deepview">
                                <string key="footerTitle">The name of the deepview template to use for Android.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="faI-g8-zgh">
                                        <rect key="frame" x="0.0" y="4410.*************" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="faI-g8-zgh" id="3DS-SY-mWZ">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="NKL-am-Cdc" userLabel="androidDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="NKL-am-Cdc" firstAttribute="centerY" secondItem="3DS-SY-mWZ" secondAttribute="centerY" id="CUJ-ph-E6q"/>
                                                <constraint firstItem="NKL-am-Cdc" firstAttribute="leading" secondItem="3DS-SY-mWZ" secondAttribute="leadingMargin" constant="6" id="Caz-Z5-A7y"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="NKL-am-Cdc" secondAttribute="trailing" constant="6" id="za1-oB-PwZ"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$desktop_deepview" id="Yel-XV-DyC" userLabel="$desktop_deepview">
                                <string key="footerTitle">The name of the deepview template to use for the desktop.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="pBe-Th-LlF">
                                        <rect key="frame" x="0.0" y="4561.666666666667" width="414" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="pBe-Th-LlF" id="fiw-fr-V4a">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="GW9-Xl-Trt" userLabel="desktopDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.666666666666664" width="386" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="GW9-Xl-Trt" firstAttribute="leading" secondItem="fiw-fr-V4a" secondAttribute="leadingMargin" constant="6" id="Dsa-us-AoH"/>
                                                <constraint firstItem="GW9-Xl-Trt" firstAttribute="centerY" secondItem="fiw-fr-V4a" secondAttribute="centerY" id="ENP-wY-7zi"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="GW9-Xl-Trt" secondAttribute="trailing" constant="6" id="iV5-zM-dvB"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="QY6-4D-Gxe" id="fMa-uC-22T"/>
                            <outlet property="delegate" destination="QY6-4D-Gxe" id="MQS-cu-wiD"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="Link Properties" id="NAQ-sQ-Xwv">
                        <barButtonItem key="leftBarButtonItem" style="done" systemItem="done" id="YhY-0C-Kip">
                            <connections>
                                <segue destination="dIA-gQ-gYI" kind="unwind" identifier="UnwindLinkPropertiesTableViewController" unwindAction="unwindLinkPropertiesTableViewController:" id="xeQ-O0-lQj"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="afterClickURLTextField" destination="axt-rS-OdB" id="LEZ-GX-kmN"/>
                        <outlet property="aliasTextField" destination="V5w-iZ-snS" id="Vhu-Qv-c0w"/>
                        <outlet property="alwaysDeeplinkSwitch" destination="DNt-GX-Ykb" id="BLx-x8-1bI"/>
                        <outlet property="androidDeeplinkPathTextField" destination="1ml-G3-TTc" id="xo7-NN-NJN"/>
                        <outlet property="androidDeepviewTextField" destination="NKL-am-Cdc" id="jKx-1y-abH"/>
                        <outlet property="androidRedirectTimeoutTextField" destination="048-Ef-qL4" id="Ln5-XS-qeo"/>
                        <outlet property="androidURLTextField" destination="6aC-yM-ART" id="gHo-yY-05G"/>
                        <outlet property="blackberryURLTextField" destination="0L3-oL-gau" id="ipc-pU-Eq7"/>
                        <outlet property="campaignTextField" destination="5ya-nz-Zg6" id="WIz-3f-oOi"/>
                        <outlet property="channelTextField" destination="2Uy-GD-t6z" id="8qh-zy-b6o"/>
                        <outlet property="clearAllValuesButton" destination="xaO-ts-SYE" id="lGn-l2-9Hy"/>
                        <outlet property="deeplinkPathTextField" destination="ZXD-aO-bzl" id="bEw-PX-7jv"/>
                        <outlet property="desktopDeepviewTextField" destination="GW9-Xl-Trt" id="Odd-mq-2Bl"/>
                        <outlet property="desktopURLTextField" destination="aVa-wl-KRF" id="zqH-TX-jqw"/>
                        <outlet property="fallbackURLTextField" destination="uqh-Zw-oed" id="1yG-9I-LWN"/>
                        <outlet property="featureTextField" destination="xs7-5a-g2w" id="TLD-ru-YJ3"/>
                        <outlet property="fireURLTextField" destination="qv3-9n-yTX" id="dhE-eu-cyO"/>
                        <outlet property="iosDeeplinkPathTextField" destination="LAh-Yf-YoI" id="xXC-aG-iRV"/>
                        <outlet property="iosDeepviewTextField" destination="XC3-ID-lnH" id="eq3-Aq-DDo"/>
                        <outlet property="iosRedirectTimeoutTextField" destination="5mb-VU-Zzb" id="sOL-tw-Dyg"/>
                        <outlet property="iosURLTextField" destination="xtG-OZ-0tp" id="VFO-6e-V5c"/>
                        <outlet property="iosWeChatURLTextField" destination="B5l-lH-KbQ" id="7dU-lR-Igc"/>
                        <outlet property="iosWeiboURLTextField" destination="z2M-Gb-GHr" id="7iF-y7-yIQ"/>
                        <outlet property="ipadURLTextField" destination="UV6-Os-JFv" id="vat-mt-vj0"/>
                        <outlet property="matchDurationTextField" destination="AbC-iA-R9W" id="H83-tq-taA"/>
                        <outlet property="oneTimeUseSwitch" destination="d0s-hS-3km" id="6ZJ-Vb-K0k"/>
                        <outlet property="stageTextField" destination="RKf-cA-BTP" id="vGt-Zz-8V0"/>
                        <outlet property="tagsTextView" destination="gyD-DA-0yx" id="M4q-Bk-C78"/>
                        <outlet property="webOnlySwitch" destination="2ZF-xX-urF" id="txn-aT-SXp"/>
                        <outlet property="windowsPhoneURLTextField" destination="W9Y-Kz-ydz" id="U4x-ck-J4a"/>
                        <segue destination="9cY-W1-idP" kind="show" identifier="ShowTags" id="Qus-RU-z7K"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ISL-Qe-gS1" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <exit id="dIA-gQ-gYI" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="1839" y="1042"/>
        </scene>
        <!--CreditHistoryTableView-->
        <scene sceneID="GXE-mS-r85">
            <objects>
                <tableViewController storyboardIdentifier="CreditHistory" useStoryboardIdentifierAsRestorationIdentifier="YES" id="hjV-EB-GfF" userLabel="CreditHistoryTableView" customClass="CreditHistoryViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" id="VsT-Pt-L2W">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <prototypes>
                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="CreditTransactionRow" textLabel="OBJ-4n-FGf" detailTextLabel="Lma-wY-lV4" style="IBUITableViewCellStyleSubtitle" id="4FF-PW-otK">
                                <rect key="frame" x="0.0" y="22" width="414" height="44"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="4FF-PW-otK" id="guV-GI-En6">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="43.666666666666664"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="OBJ-4n-FGf">
                                            <rect key="frame" x="20" y="6" width="31.666666666666668" height="19.333333333333332"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" text="Subtitle" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="Lma-wY-lV4">
                                            <rect key="frame" x="19.999999999999996" y="25.333333333333332" width="40.666666666666664" height="13.333333333333334"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </tableViewCellContentView>
                            </tableViewCell>
                        </prototypes>
                        <connections>
                            <outlet property="dataSource" destination="hjV-EB-GfF" id="YkG-J9-boX"/>
                            <outlet property="delegate" destination="hjV-EB-GfF" id="tz5-dB-Sax"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" id="Bo8-EB-Mq6"/>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="bsi-kT-aDq" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1531" y="-917"/>
        </scene>
        <!--TestBed-SwiftNavigationController-->
        <scene sceneID="h0k-6e-SBq">
            <objects>
                <navigationController storyboardIdentifier="NavigationController" automaticallyAdjustsScrollViewInsets="NO" useStoryboardIdentifierAsRestorationIdentifier="YES" id="d5D-s8-qUb" userLabel="TestBed-SwiftNavigationController" customClass="NavigationController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationItem key="navigationItem" title="Branch-TestBed" id="vxy-nF-3Wa"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="GMh-cm-snc">
                        <rect key="frame" x="0.0" y="20" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="3bi-5k-7qj" kind="relationship" relationship="rootViewController" id="d2l-0l-9dU"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0le-mP-k9p" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1312" y="27"/>
        </scene>
        <!--TextViewForm-->
        <scene sceneID="4av-qW-gdO">
            <objects>
                <tableViewController id="Aqu-O9-8Kl" userLabel="TextViewForm" customClass="TextViewFormTableViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="default" allowsSelection="NO" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" id="bNk-SI-3yX">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection id="cD0-jb-3GD" userLabel="Section">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" placeholderIntrinsicWidth="600" placeholderIntrinsicHeight="88" selectionStyle="default" indentationWidth="10" rowHeight="98" id="fkP-XX-NcM" userLabel="TableViewCell">
                                        <rect key="frame" x="0.0" y="35" width="414" height="98"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="fkP-XX-NcM" id="5OY-9g-dIW">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="97.666666666666671"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="bBo-K7-Axw">
                                                    <rect key="frame" x="8" y="8" width="398" height="81.666666666666671"/>
                                                    <subviews>
                                                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="top" translatesAutoresizingMaskIntoConstraints="NO" id="rpg-0S-e9Y" userLabel="textView">
                                                            <rect key="frame" x="0.0" y="0.0" width="378" height="81.666666666666671"/>
                                                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <accessibility key="accessibilityConfiguration" identifier="textView" label="textView"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                        </textView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dxb-kZ-oCy" userLabel="clearButton">
                                                            <rect key="frame" x="378" y="30.999999999999996" width="20" height="19.666666666666661"/>
                                                            <accessibility key="accessibilityConfiguration" identifier="clearButton" label="clearButton"/>
                                                            <state key="normal" image="cancel-28.png"/>
                                                            <connections>
                                                                <action selector="clearButtonTouchUpInside:" destination="Aqu-O9-8Kl" eventType="touchUpInside" id="1l2-wX-nL6"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstItem="rpg-0S-e9Y" firstAttribute="top" secondItem="bBo-K7-Axw" secondAttribute="top" id="PCH-NQ-cl1"/>
                                                        <constraint firstAttribute="trailing" secondItem="rpg-0S-e9Y" secondAttribute="trailing" constant="20" symbolic="YES" id="oXg-fb-l24"/>
                                                        <constraint firstItem="dxb-kZ-oCy" firstAttribute="top" secondItem="bBo-K7-Axw" secondAttribute="top" constant="31" id="qg4-yp-dDS"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstItem="bBo-K7-Axw" firstAttribute="trailing" secondItem="5OY-9g-dIW" secondAttribute="trailingMargin" id="DRk-wE-wHd"/>
                                                <constraint firstItem="bBo-K7-Axw" firstAttribute="bottom" secondItem="5OY-9g-dIW" secondAttribute="bottomMargin" id="VNs-IF-rc1"/>
                                                <constraint firstItem="bBo-K7-Axw" firstAttribute="leading" secondItem="5OY-9g-dIW" secondAttribute="leadingMargin" id="a1B-uD-QNu"/>
                                                <constraint firstItem="bBo-K7-Axw" firstAttribute="top" secondItem="5OY-9g-dIW" secondAttribute="topMargin" id="vCL-gX-xwo"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="Aqu-O9-8Kl" id="gt1-rn-ezo"/>
                            <outlet property="delegate" destination="Aqu-O9-8Kl" id="dtj-xG-ZDH"/>
                        </connections>
                    </tableView>
                    <toolbarItems/>
                    <navigationItem key="navigationItem" id="n9M-oY-MLO" userLabel="TextView Form Navigation Item">
                        <barButtonItem key="leftBarButtonItem" systemItem="cancel" id="gWe-Na-7BQ">
                            <connections>
                                <segue destination="ZFQ-QM-Boa" kind="unwind" identifier="Cancel" unwindAction="unwindByCancelling:" id="hQS-pm-K0f"/>
                            </connections>
                        </barButtonItem>
                        <barButtonItem key="rightBarButtonItem" enabled="NO" systemItem="save" id="qUx-K8-QwN">
                            <connections>
                                <segue destination="ZFQ-QM-Boa" kind="unwind" identifier="Save" unwindAction="unwindTextViewFormTableViewController:" id="do3-3e-hHK"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="clearButton" destination="dxb-kZ-oCy" id="Mpr-W2-kEX"/>
                        <outlet property="saveButton" destination="qUx-K8-QwN" id="aOL-Bg-kFP"/>
                        <outlet property="textView" destination="rpg-0S-e9Y" id="ieH-Ll-ACU"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Yn1-1u-XeI" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <exit id="ZFQ-QM-Boa" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="3815" y="-339"/>
        </scene>
        <!--TextViewNavigationController-->
        <scene sceneID="DdE-pi-1cw">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="WPU-Ft-SWB" userLabel="TextViewNavigationController" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="CEI-uw-UIX">
                        <rect key="frame" x="0.0" y="20" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="Aqu-O9-8Kl" kind="relationship" relationship="rootViewController" id="UT0-7m-xoG"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Og6-u7-Vxn" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3031" y="-808"/>
        </scene>
        <!--ArrayTableViewController-->
        <scene sceneID="w3R-5M-dX3">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="ArrayTableViewController" storyboardName="ArrayTableView" referencedIdentifier="ArrayTableViewController" id="9cY-W1-idP" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="l3w-PA-uof" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1838" y="1993"/>
        </scene>
        <!--CommerceEventDetails-->
        <scene sceneID="xLA-3T-Zy5">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="CommerceEventDetails" storyboardName="CommerceEvents" referencedIdentifier="CommerceEventDetails" id="l7T-5a-FG6" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZOW-of-mh6" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1132" y="1042"/>
        </scene>
        <!--Product-->
        <scene sceneID="IPK-CI-wty">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="Product" storyboardName="CommerceEvents" referencedIdentifier="Product" id="HHu-Di-hfx" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="TLP-3i-bBY" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1132" y="1993"/>
        </scene>
        <!--ProductArrayTableViewController-->
        <scene sceneID="IWy-kd-I3O">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="ProductArrayTableViewController" storyboardName="CommerceEvents" referencedIdentifier="ProductArrayTableViewController" id="JC3-4R-VBv" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YzZ-C8-5hT" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="155" y="1992"/>
        </scene>
        <!--UITableViewController-f9P-iX-WYi-->
        <scene sceneID="98s-V3-bSO">
            <objects>
                <viewControllerPlaceholder storyboardName="UniversalObject" referencedIdentifier="UITableViewController-f9P-iX-WYi" id="lNP-uJ-cup" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tOH-Pa-cEO" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4936" y="685"/>
        </scene>
        <!--BranchUniversalObjectProperties-->
        <scene sceneID="Axl-xF-gPr">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="BranchUniversalObjectProperties" storyboardName="UniversalObject" referencedIdentifier="BranchUniversalObjectProperties" id="fpK-rx-X9D" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="sNm-1Q-EjD" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3471" y="1042"/>
        </scene>
    </scenes>
    <resources>
        <image name="cancel-28.png" width="28" height="28"/>
    </resources>
    <inferredMetricsTieBreakers>
        <segue reference="7TN-Vi-Tcv"/>
    </inferredMetricsTieBreakers>
</document>
