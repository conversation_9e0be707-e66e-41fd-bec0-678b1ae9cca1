<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="QY6-4D-Gxe">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--LinkPropertiesTableView-->
        <scene sceneID="cV0-cg-vVF">
            <objects>
                <tableViewController storyboardIdentifier="LinkProperties" useStoryboardIdentifierAsRestorationIdentifier="YES" id="QY6-4D-Gxe" userLabel="LinkPropertiesTableView" customClass="LinkPropertiesTableViewController" customModule="TestBed_Swift" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="top" alwaysBounceVertical="YES" dataMode="static" style="grouped" separatorStyle="none" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" id="NXA-2i-shU">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.93725490199999995" green="0.93725490199999995" blue="0.95686274510000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection id="EKi-k1-qSM" userLabel="clearAllValues">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="ZQ5-85-OUb">
                                        <rect key="frame" x="0.0" y="35" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ZQ5-85-OUb" id="Sup-gj-nMj">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xaO-ts-SYE" userLabel="clearAllValuesButton">
                                                    <rect key="frame" x="26" y="7" width="548" height="30"/>
                                                    <state key="normal" title="Clear all values"/>
                                                    <connections>
                                                        <action selector="clearAllValuesTouchUpInside:" destination="QY6-4D-Gxe" eventType="touchUpInside" id="FEg-39-iyS"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="xaO-ts-SYE" secondAttribute="trailing" constant="6" id="Fvb-zM-MHB"/>
                                                <constraint firstItem="xaO-ts-SYE" firstAttribute="leading" secondItem="Sup-gj-nMj" secondAttribute="leadingMargin" constant="6" id="NFm-9v-qUr"/>
                                                <constraint firstItem="xaO-ts-SYE" firstAttribute="centerY" secondItem="Sup-gj-nMj" secondAttribute="centerY" id="aad-8P-3mc"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="channel" id="7i1-Mo-GVf" userLabel="channel">
                                <string key="footerTitle">Use channel to tag the route that your link reaches users. For example, tag links with ‘Facebook’ or ‘LinkedIn’ to help track clicks and installs through those paths separately.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="7xK-3f-h7M">
                                        <rect key="frame" x="0.0" y="142.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="7xK-3f-h7M" id="6y9-vg-xRl">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="2Uy-GD-t6z" userLabel="channelTextField">
                                                    <rect key="frame" x="26" y="12.5" width="548" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="2Uy-GD-t6z" firstAttribute="centerY" secondItem="6y9-vg-xRl" secondAttribute="centerY" id="6Mp-f6-ZCi"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="2Uy-GD-t6z" secondAttribute="trailing" constant="6" id="HNx-MM-3T5"/>
                                                <constraint firstItem="2Uy-GD-t6z" firstAttribute="leading" secondItem="6y9-vg-xRl" secondAttribute="leadingMargin" constant="6" id="LsR-Rd-rTR"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="feature" id="afB-2q-xg9" userLabel="feature">
                                <string key="footerTitle">This is the feature of your app that the link might be associated with. For example, if you had built a referral program, you would label links with the feature: 'referral'</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Ggg-qN-hjW">
                                        <rect key="frame" x="0.0" y="310" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Ggg-qN-hjW" id="kCh-Ht-Z8Q">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="xs7-5a-g2w" userLabel="featureTextField">
                                                    <rect key="frame" x="26" y="12.5" width="548" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="xs7-5a-g2w" firstAttribute="centerY" secondItem="kCh-Ht-Z8Q" secondAttribute="centerY" id="LyJ-k1-HMw"/>
                                                <constraint firstItem="xs7-5a-g2w" firstAttribute="leading" secondItem="kCh-Ht-Z8Q" secondAttribute="leadingMargin" constant="6" id="fZA-3U-g58"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="xs7-5a-g2w" secondAttribute="trailing" constant="6" id="gCF-NN-04S"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="campaign" id="4Vh-xe-lxD" userLabel="campaign">
                                <string key="footerTitle">Use this field to organize the links by marketing campaign. For example, if you launched a new feature or product and want to run a campaign around that</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="aLS-Le-CA2">
                                        <rect key="frame" x="0.0" y="461.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="aLS-Le-CA2" id="Nq9-WT-bQj">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="5ya-nz-Zg6" userLabel="campaignTextField">
                                                    <rect key="frame" x="26" y="12.5" width="548" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="5ya-nz-Zg6" firstAttribute="centerY" secondItem="Nq9-WT-bQj" secondAttribute="centerY" id="e2W-Na-nG7"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="5ya-nz-Zg6" secondAttribute="trailing" constant="6" id="fmd-Hd-FMN"/>
                                                <constraint firstItem="5ya-nz-Zg6" firstAttribute="leading" secondItem="Nq9-WT-bQj" secondAttribute="leadingMargin" constant="6" id="mBW-Qy-TmZ"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="stage" id="TFg-M4-MGB" userLabel="stage">
                                <string key="footerTitle">Use this to categorize the progress or category of a user when the link was generated. For example, if you had an invite system accessible on level 1, level 3 and 5, you could differentiate links generated at each level with this parameter</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="rGi-aV-FEc">
                                        <rect key="frame" x="0.0" y="613" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="rGi-aV-FEc" id="8ib-uo-Z71">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="RKf-cA-BTP" userLabel="stageTextField">
                                                    <rect key="frame" x="26" y="12.5" width="560" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="leading" secondItem="8ib-uo-Z71" secondAttribute="leadingMargin" constant="6" id="E6k-I8-id3"/>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="centerY" secondItem="8ib-uo-Z71" secondAttribute="centerY" id="fAT-bb-KOK"/>
                                                <constraint firstItem="RKf-cA-BTP" firstAttribute="trailing" secondItem="8ib-uo-Z71" secondAttribute="trailingMargin" constant="6" id="omG-fC-fXe"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="tags" id="Err-LV-0GX" userLabel="tags">
                                <string key="footerTitle">This is a free form entry with unlimited values. Use it to organize your link data with labels that don’t fit within the bounds of the above.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" indentationWidth="10" reuseIdentifier="Table View Cell" rowHeight="98" id="APe-MJ-HaR">
                                        <rect key="frame" x="0.0" y="796.5" width="375" height="98"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="APe-MJ-HaR" id="LjI-5M-9ae">
                                            <rect key="frame" x="0.0" y="0.0" width="342" height="98"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textView clipsSubviews="YES" multipleTouchEnabled="YES" userInteractionEnabled="NO" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="gyD-DA-0yx" userLabel="tagsTextView">
                                                    <rect key="frame" x="14" y="11" width="539" height="76"/>
                                                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                </textView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottomMargin" secondItem="gyD-DA-0yx" secondAttribute="bottom" id="KFz-Ko-1zL"/>
                                                <constraint firstItem="gyD-DA-0yx" firstAttribute="leading" secondItem="LjI-5M-9ae" secondAttribute="leadingMargin" constant="6" id="Uv1-ii-8gj"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="gyD-DA-0yx" secondAttribute="trailing" constant="6" id="kgR-Eg-Ltb"/>
                                                <constraint firstItem="gyD-DA-0yx" firstAttribute="top" secondItem="LjI-5M-9ae" secondAttribute="topMargin" id="njj-ON-Y9J"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="alias" id="LXz-Cj-rY4" userLabel="alias">
                                <string key="footerTitle">Specify a link alias in place of the standard encoded short URL (e.g., [branchsubdomain]/youralias or yourdomain.co/youralias). Link aliases are unique, immutable objects that cannot be deleted.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="0CU-dh-acg">
                                        <rect key="frame" x="0.0" y="1002" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="0CU-dh-acg" id="DR7-gC-lNM">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="no default value" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="V5w-iZ-snS" userLabel="aliasTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="V5w-iZ-snS" secondAttribute="trailing" constant="6" id="LOh-Kc-Z90"/>
                                                <constraint firstItem="V5w-iZ-snS" firstAttribute="centerY" secondItem="DR7-gC-lNM" secondAttribute="centerY" id="lGd-yU-26G"/>
                                                <constraint firstItem="V5w-iZ-snS" firstAttribute="leading" secondItem="DR7-gC-lNM" secondAttribute="leadingMargin" constant="6" id="zEt-LH-Lzu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$fallback_url" id="byU-yu-B8J" userLabel="$fallback_url">
                                <string key="footerTitle">For all platforms, where to send the user when the app is not installed. Note that Branch will forward all robots to this URL, overriding any OG tags entered in the link.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="zfQ-mA-UsS">
                                        <rect key="frame" x="0.0" y="1169.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="zfQ-mA-UsS" id="U7b-AE-TvB">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="uqh-Zw-oed" userLabel="fallbackURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="584" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="leading" secondItem="U7b-AE-TvB" secondAttribute="leadingMargin" constant="6" id="HMD-ZN-U48"/>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="trailing" secondItem="U7b-AE-TvB" secondAttribute="trailingMargin" constant="6" id="Vwd-h2-gUV"/>
                                                <constraint firstItem="uqh-Zw-oed" firstAttribute="centerY" secondItem="U7b-AE-TvB" secondAttribute="centerY" id="y6b-dm-NG1"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$desktop_url" footerTitle="Where to send the user on a desktop or laptop. If not set, this defaults to the Branch-hosted text-me-the-app page." id="mav-4d-wu7" userLabel="$desktop_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="15Y-39-rVu">
                                        <rect key="frame" x="0.0" y="1321" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="15Y-39-rVu" id="h8a-Jo-3BI">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses text-me-the-app page if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="aVa-wl-KRF" userLabel="desktopURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="aVa-wl-KRF" firstAttribute="leading" secondItem="h8a-Jo-3BI" secondAttribute="leadingMargin" constant="6" id="66Q-Mu-B8l"/>
                                                <constraint firstItem="aVa-wl-KRF" firstAttribute="centerY" secondItem="h8a-Jo-3BI" secondAttribute="centerY" id="B0G-ED-KqC"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="aVa-wl-KRF" secondAttribute="trailing" constant="6" id="gkW-Oc-31A"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_url" footerTitle="If set, iPhone users who do not have the app installed will be redirected to this URL instead of the App Store" id="bDA-1G-xNs" userLabel="$ios_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="xg7-fZ-sky">
                                        <rect key="frame" x="0.0" y="1472.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="xg7-fZ-sky" id="NhZ-dh-h3x">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="xtG-OZ-0tp" userLabel="iosURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="xtG-OZ-0tp" firstAttribute="centerY" secondItem="NhZ-dh-h3x" secondAttribute="centerY" id="AJU-LD-90D"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="xtG-OZ-0tp" secondAttribute="trailing" constant="6" id="JC6-TL-YYc"/>
                                                <constraint firstItem="xtG-OZ-0tp" firstAttribute="leading" secondItem="NhZ-dh-h3x" secondAttribute="leadingMargin" constant="6" id="cAB-G3-bon"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ipad_url" id="HfC-OP-Dtg" userLabel="$ipad_url">
                                <string key="footerTitle">iPad users who do not have the app installed will be redirected to this URL instead of the App Store. Defaults to $ios_url if not specified.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="btX-iu-MeG">
                                        <rect key="frame" x="0.0" y="1608" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="btX-iu-MeG" id="qMO-mh-mfo">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="UV6-Os-JFv" userLabel="ipadURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="UV6-Os-JFv" firstAttribute="centerY" secondItem="qMO-mh-mfo" secondAttribute="centerY" id="6kb-1S-Kdm"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="UV6-Os-JFv" secondAttribute="trailing" constant="6" id="VEl-Bb-paj"/>
                                                <constraint firstItem="UV6-Os-JFv" firstAttribute="leading" secondItem="qMO-mh-mfo" secondAttribute="leadingMargin" constant="6" id="iv4-zd-Ti7"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_url" footerTitle="If set, Android users who do not have the app installed will be redirected to this URL instead of the Play Store" id="SCe-A0-nai" userLabel="$android_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Qns-Za-x3O">
                                        <rect key="frame" x="0.0" y="1759.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Qns-Za-x3O" id="NIe-mw-1VA">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="6aC-yM-ART" userLabel="androidURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="6aC-yM-ART" firstAttribute="centerY" secondItem="NIe-mw-1VA" secondAttribute="centerY" id="Fr0-jq-sks"/>
                                                <constraint firstItem="6aC-yM-ART" firstAttribute="leading" secondItem="NIe-mw-1VA" secondAttribute="leadingMargin" constant="6" id="KQy-fc-tYf"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="6aC-yM-ART" secondAttribute="trailing" constant="6" id="uxe-pO-nJm"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$windows_phone_url" footerTitle="If set, Windows phone users who do not have the app installed will be redirected to this URL" id="Mlo-wj-AFO" userLabel="$windows_phone_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="59F-yN-gxT">
                                        <rect key="frame" x="0.0" y="1895" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="59F-yN-gxT" id="2CZ-hn-TTz">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="W9Y-Kz-ydz" userLabel="windowsPhoneURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="W9Y-Kz-ydz" secondAttribute="trailing" constant="6" id="4ts-PH-GhC"/>
                                                <constraint firstItem="W9Y-Kz-ydz" firstAttribute="leading" secondItem="2CZ-hn-TTz" secondAttribute="leadingMargin" constant="6" id="CFw-dJ-24u"/>
                                                <constraint firstItem="W9Y-Kz-ydz" firstAttribute="centerY" secondItem="2CZ-hn-TTz" secondAttribute="centerY" id="cxf-5y-Mt5"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$blackberry_url" footerTitle="If set, Blackberry users who do not have the app installed will be redirected to this URL" id="O0i-QT-fCA" userLabel="$blackberry_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="pMa-C6-b2W">
                                        <rect key="frame" x="0.0" y="2030.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="pMa-C6-b2W" id="XhU-Ip-UTp">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="0L3-oL-gau" userLabel="blackberryURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="0L3-oL-gau" secondAttribute="trailing" constant="6" id="Jdt-4F-IWt"/>
                                                <constraint firstItem="0L3-oL-gau" firstAttribute="leading" secondItem="XhU-Ip-UTp" secondAttribute="leadingMargin" constant="6" id="kcc-Oe-QmX"/>
                                                <constraint firstItem="0L3-oL-gau" firstAttribute="centerY" secondItem="XhU-Ip-UTp" secondAttribute="centerY" id="vtt-b2-b9i"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$fire_url" footerTitle="If set, Amazon Fire users who do not have the app installed will be redirected to this URL" id="w7n-c0-n0S" userLabel="$fire_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Z3k-dM-uIW">
                                        <rect key="frame" x="0.0" y="2166" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Z3k-dM-uIW" id="fXr-qw-CzT">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="qv3-9n-yTX" userLabel="fireURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="qv3-9n-yTX" firstAttribute="centerY" secondItem="fXr-qw-CzT" secondAttribute="centerY" id="9B1-ZL-MJu"/>
                                                <constraint firstItem="qv3-9n-yTX" firstAttribute="leading" secondItem="fXr-qw-CzT" secondAttribute="leadingMargin" constant="6" id="lhc-GS-8cR"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="qv3-9n-yTX" secondAttribute="trailing" constant="6" id="m6H-Sq-hMR"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_wechat_url" footerTitle="Change the redirect endpoint for WeChat on iOS devices." id="sjT-KP-uMM" userLabel="$ios_wechat_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="WMg-PC-rjl">
                                        <rect key="frame" x="0.0" y="2301.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="WMg-PC-rjl" id="tbh-ps-0ZA">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="B5l-lH-KbQ" userLabel="iosWeChatURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="B5l-lH-KbQ" secondAttribute="trailing" constant="6" id="9iY-7L-Y7m"/>
                                                <constraint firstItem="B5l-lH-KbQ" firstAttribute="centerY" secondItem="tbh-ps-0ZA" secondAttribute="centerY" id="Enm-Zn-LId"/>
                                                <constraint firstItem="B5l-lH-KbQ" firstAttribute="leading" secondItem="tbh-ps-0ZA" secondAttribute="leadingMargin" constant="6" id="mtG-8L-l2U"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_weibo_url" footerTitle="Change the redirect endpoint for Weibo on iOS devices." id="9Wh-og-Q5D" userLabel="$ios_weibo_url">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="oau-oZ-equ">
                                        <rect key="frame" x="0.0" y="2437" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="oau-oZ-equ" id="m3u-4y-Lnt">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $ios_url if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="z2M-Gb-GHr" userLabel="iosWeiboURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="z2M-Gb-GHr" firstAttribute="centerY" secondItem="m3u-4y-Lnt" secondAttribute="centerY" id="d4Z-5f-nUx"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="z2M-Gb-GHr" secondAttribute="trailing" constant="6" id="i5o-ny-6cg"/>
                                                <constraint firstItem="z2M-Gb-GHr" firstAttribute="leading" secondItem="m3u-4y-Lnt" secondAttribute="leadingMargin" constant="6" id="wyI-uV-wKi"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$after_click_url" id="dvM-KR-Q9Z" userLabel="$after_click_url">
                                <string key="footerTitle">When a user returns to the browser after the app was successfully opened they will be redirected to this URL. This currently works for iOS only; Android support is coming soon.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="PD6-90-h16">
                                        <rect key="frame" x="0.0" y="2556.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="PD6-90-h16" id="3Pa-m9-yOi">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="must begin with http:// or https://" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="axt-rS-OdB" userLabel="afterClickURLTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="axt-rS-OdB" firstAttribute="centerY" secondItem="3Pa-m9-yOi" secondAttribute="centerY" id="TT3-nI-wQz"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="axt-rS-OdB" secondAttribute="trailing" constant="6" id="jdA-gP-as2"/>
                                                <constraint firstItem="axt-rS-OdB" firstAttribute="leading" secondItem="3Pa-m9-yOi" secondAttribute="leadingMargin" constant="6" id="xAC-FM-Vwu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$web_only" id="Wkl-Ez-ZJ9" userLabel="$web_only">
                                <string key="footerTitle">This lets you direct the user to the web, even if they have the app installed. When creating the link, add $web_only: true to the deep link data.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="ab3-wA-rVV">
                                        <rect key="frame" x="0.0" y="2724" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="ab3-wA-rVV" id="UxC-Ph-OHb">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$web_only" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HH5-XO-0GK" userLabel="webOnlyLabel">
                                                    <rect key="frame" x="14" y="12" width="81.5" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="2ZF-xX-urF" userLabel="webOnlySwitch">
                                                    <rect key="frame" x="537" y="6.5" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="2ZF-xX-urF" secondAttribute="trailing" constant="6" id="J29-QF-R98"/>
                                                <constraint firstItem="2ZF-xX-urF" firstAttribute="centerY" secondItem="UxC-Ph-OHb" secondAttribute="centerY" id="Uas-Ps-l5i"/>
                                                <constraint firstItem="HH5-XO-0GK" firstAttribute="leading" secondItem="UxC-Ph-OHb" secondAttribute="leadingMargin" constant="6" id="Z5k-o7-x7h"/>
                                                <constraint firstItem="HH5-XO-0GK" firstAttribute="centerY" secondItem="UxC-Ph-OHb" secondAttribute="centerY" id="xgP-Xh-hQ4"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$deeplink_path" id="tTv-KE-3Du" userLabel="$deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier".

Universal Links and Spotlight do not support deep linking via URI paths. We recommend not using $deeplink_path as your only deep link routing method.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="fCK-Qh-CtF">
                                        <rect key="frame" x="0.0" y="2875.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="fCK-Qh-CtF" id="Bwk-qj-sH3">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="open?link_click_id={Branch link ID}" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="ZXD-aO-bzl" userLabel="deeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="ZXD-aO-bzl" firstAttribute="centerY" secondItem="Bwk-qj-sH3" secondAttribute="centerY" id="J94-yz-aer"/>
                                                <constraint firstItem="ZXD-aO-bzl" firstAttribute="leading" secondItem="Bwk-qj-sH3" secondAttribute="leadingMargin" constant="6" id="JsA-wp-Apg"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="ZXD-aO-bzl" secondAttribute="trailing" constant="6" id="SnQ-N6-RGu"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_deeplink_path" id="c6z-mL-1q7" userLabel="$android_deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme on Android. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier". This is primarily for supporting legacy deep linking infrastructure.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="jWJ-o0-iwq">
                                        <rect key="frame" x="0.0" y="3123" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="jWJ-o0-iwq" id="kfw-Yq-NeY">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $deeplink_path if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="1ml-G3-TTc" userLabel="androidDeeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="1ml-G3-TTc" firstAttribute="centerY" secondItem="kfw-Yq-NeY" secondAttribute="centerY" id="BjK-YC-LdE"/>
                                                <constraint firstItem="1ml-G3-TTc" firstAttribute="leading" secondItem="kfw-Yq-NeY" secondAttribute="leadingMargin" constant="6" id="duY-y2-MqP"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="1ml-G3-TTc" secondAttribute="trailing" constant="6" id="wh2-RF-7rW"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_deeplink_path" id="WDS-uN-hVP" userLabel="$ios_deeplink_path">
                                <string key="footerTitle">The value of the deep link path that you'd like us to append to your URI Scheme on iOS. For example, you could specify "$deeplink_path": "radio/station/456" and we'll open the app with the URI "yourapp://radio/station/456?link_click_id=branch-identifier". This is primarily for supporting legacy deep linking infrastructure.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="q88-TY-fdl">
                                        <rect key="frame" x="0.0" y="3338.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="q88-TY-fdl" id="wWM-jY-wSi">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="uses $deeplink_path if none specified" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="LAh-Yf-YoI" userLabel="iosDeeplinkPathTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="LAh-Yf-YoI" firstAttribute="leading" secondItem="wWM-jY-wSi" secondAttribute="leadingMargin" constant="6" id="7aq-up-U0E"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="LAh-Yf-YoI" secondAttribute="trailing" constant="6" id="AOj-nr-RYo"/>
                                                <constraint firstItem="LAh-Yf-YoI" firstAttribute="centerY" secondItem="wWM-jY-wSi" secondAttribute="centerY" id="H4Y-Wm-yiG"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$match_duration" footerTitle="The attribution window, in seconds, for clicks coming from this link." id="qLw-wc-jH4" userLabel="$match_duration">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="lG2-MY-xYa">
                                        <rect key="frame" x="0.0" y="3538" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="lG2-MY-xYa" id="gWE-sE-iim">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="7200" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="AbC-iA-R9W" userLabel="matchDurationTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="AbC-iA-R9W" secondAttribute="trailing" constant="6" id="C1W-VU-PKl"/>
                                                <constraint firstItem="AbC-iA-R9W" firstAttribute="leading" secondItem="gWE-sE-iim" secondAttribute="leadingMargin" constant="6" id="nWf-cC-Pjb"/>
                                                <constraint firstItem="AbC-iA-R9W" firstAttribute="centerY" secondItem="gWE-sE-iim" secondAttribute="centerY" id="uQN-kW-oYt"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$always_deeplink" id="vrQ-KC-Olf" userLabel="$always_deeplink">
                                <string key="footerTitle">If true, an attempt to open the app will be made, even if Branch has not seen the device before and is therefore unsure whether the user has the app installed or not. If the app is not installed, we fall back to the respective app store or $platform_url key. By default, we only open the app if we've seen a user initiate a session in your app from a Branch link (has been cookied and deep linked by Branch).</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="jku-gt-pm6">
                                        <rect key="frame" x="0.0" y="3673.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="jku-gt-pm6" id="Iny-NN-Sb7">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$always_deeplink" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7dv-Cr-65l" userLabel="alwaysDeeplinkLabel">
                                                    <rect key="frame" x="14" y="12" width="133.5" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="DNt-GX-Ykb" userLabel="alwaysDeeplinkSwitch">
                                                    <rect key="frame" x="537" y="6.5" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="7dv-Cr-65l" firstAttribute="centerY" secondItem="Iny-NN-Sb7" secondAttribute="centerY" id="9jQ-Go-AKZ"/>
                                                <constraint firstItem="7dv-Cr-65l" firstAttribute="leading" secondItem="Iny-NN-Sb7" secondAttribute="leadingMargin" constant="6" id="KVD-7c-Gbg"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="DNt-GX-Ykb" secondAttribute="trailing" constant="6" id="Os9-lP-LOa"/>
                                                <constraint firstItem="DNt-GX-Ykb" firstAttribute="centerY" secondItem="Iny-NN-Sb7" secondAttribute="centerY" id="oUs-ra-EVm"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_redirect_timeout" id="efw-QV-XuG" userLabel="$ios_redirect_timeout">
                                <string key="footerTitle">Control the timeout that the client-side JS waits after trying to open up the app before redirecting to the App Store. Specified in milliseconds.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="AZi-ak-ckt">
                                        <rect key="frame" x="0.0" y="3905" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="AZi-ak-ckt" id="9X8-eK-Beo">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="750" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="5mb-VU-Zzb" userLabel="iosRedirectTimeoutTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="5mb-VU-Zzb" secondAttribute="trailing" constant="6" id="Rqo-Tk-3LD"/>
                                                <constraint firstItem="5mb-VU-Zzb" firstAttribute="centerY" secondItem="9X8-eK-Beo" secondAttribute="centerY" id="afW-VR-t9z"/>
                                                <constraint firstItem="5mb-VU-Zzb" firstAttribute="leading" secondItem="9X8-eK-Beo" secondAttribute="leadingMargin" constant="6" id="uZV-17-m5e"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_redirect_timeout" id="E6q-bs-YYv" userLabel="$android_redirect_timeout">
                                <string key="footerTitle">Control the timeout that the clientside JS waits after trying to open up the app before redirecting to the Play Store. Specified in milliseconds.</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="9hk-bs-r2Z">
                                        <rect key="frame" x="0.0" y="4056.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="9hk-bs-r2Z" id="hyA-t0-5YY">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="750" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="048-Ef-qL4" userLabel="androidRedirectTimeoutTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="numberPad" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="048-Ef-qL4" firstAttribute="leading" secondItem="hyA-t0-5YY" secondAttribute="leadingMargin" constant="6" id="XIp-q4-7Pc"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="048-Ef-qL4" secondAttribute="trailing" constant="6" id="cbl-xX-IM9"/>
                                                <constraint firstItem="048-Ef-qL4" firstAttribute="centerY" secondItem="hyA-t0-5YY" secondAttribute="centerY" id="iu5-z4-2zG"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$one_time_use" id="RRr-ar-pIJ" userLabel="$one_time_use">
                                <string key="footerTitle">Set to ‘true’ to limit deep linking behavior of the generated link to a single use. Can also be set using type.

Default value: 0 (false)</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="Z8G-hj-ozT">
                                        <rect key="frame" x="0.0" y="4208" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Z8G-hj-ozT" id="oyh-Zv-C5b">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="$one_time_use" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L8j-nr-P9v" userLabel="oneTimeUseLabel">
                                                    <rect key="frame" x="14" y="12" width="113.5" height="20.5"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="d0s-hS-3km" userLabel="oneTimeUseSwitch">
                                                    <rect key="frame" x="537" y="6.5" width="51" height="31"/>
                                                </switch>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailingMargin" secondItem="d0s-hS-3km" secondAttribute="trailing" constant="6" id="3sc-FB-1rr"/>
                                                <constraint firstItem="d0s-hS-3km" firstAttribute="centerY" secondItem="oyh-Zv-C5b" secondAttribute="centerY" id="FmD-JH-KGL"/>
                                                <constraint firstItem="L8j-nr-P9v" firstAttribute="leading" secondItem="oyh-Zv-C5b" secondAttribute="leadingMargin" constant="6" id="VF8-pQ-X1y"/>
                                                <constraint firstItem="L8j-nr-P9v" firstAttribute="centerY" secondItem="oyh-Zv-C5b" secondAttribute="centerY" id="kod-FV-eqs"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$ios_deepview" id="jDR-kd-f3X" userLabel="$ios_deepview">
                                <string key="footerTitle">The name of the deepview template to use for iOS.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="J3z-Yx-eTT">
                                        <rect key="frame" x="0.0" y="4391.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="J3z-Yx-eTT" id="eNJ-3E-4SV">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="XC3-ID-lnH" userLabel="iosDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="XC3-ID-lnH" firstAttribute="centerY" secondItem="eNJ-3E-4SV" secondAttribute="centerY" id="Xn8-ey-pYS"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="XC3-ID-lnH" secondAttribute="trailing" constant="6" id="bUg-Iz-nbO"/>
                                                <constraint firstItem="XC3-ID-lnH" firstAttribute="leading" secondItem="eNJ-3E-4SV" secondAttribute="leadingMargin" constant="6" id="eQc-BQ-kIs"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$android_deepview" id="ocl-Uw-aZW" userLabel="$android_deepview">
                                <string key="footerTitle">The name of the deepview template to use for Android.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="faI-g8-zgh">
                                        <rect key="frame" x="0.0" y="4543" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="faI-g8-zgh" id="3DS-SY-mWZ">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="NKL-am-Cdc" userLabel="androidDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="NKL-am-Cdc" firstAttribute="centerY" secondItem="3DS-SY-mWZ" secondAttribute="centerY" id="CUJ-ph-E6q"/>
                                                <constraint firstItem="NKL-am-Cdc" firstAttribute="leading" secondItem="3DS-SY-mWZ" secondAttribute="leadingMargin" constant="6" id="Caz-Z5-A7y"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="NKL-am-Cdc" secondAttribute="trailing" constant="6" id="za1-oB-PwZ"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                            <tableViewSection headerTitle="$desktop_deepview" id="Yel-XV-DyC" userLabel="$desktop_deepview">
                                <string key="footerTitle">The name of the deepview template to use for the desktop.

Default value: branch_default</string>
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="pBe-Th-LlF">
                                        <rect key="frame" x="0.0" y="4694.5" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="pBe-Th-LlF" id="fiw-fr-V4a">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="branch_default" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="GW9-Xl-Trt" userLabel="desktopDeepviewTextField">
                                                    <rect key="frame" x="14" y="12.5" width="572" height="19"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" keyboardType="URL" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="GW9-Xl-Trt" firstAttribute="leading" secondItem="fiw-fr-V4a" secondAttribute="leadingMargin" constant="6" id="Dsa-us-AoH"/>
                                                <constraint firstItem="GW9-Xl-Trt" firstAttribute="centerY" secondItem="fiw-fr-V4a" secondAttribute="centerY" id="ENP-wY-7zi"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="GW9-Xl-Trt" secondAttribute="trailing" constant="6" id="iV5-zM-dvB"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="QY6-4D-Gxe" id="fMa-uC-22T"/>
                            <outlet property="delegate" destination="QY6-4D-Gxe" id="MQS-cu-wiD"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="Link Properties" id="NAQ-sQ-Xwv">
                        <barButtonItem key="leftBarButtonItem" style="done" systemItem="done" id="YhY-0C-Kip">
                            <connections>
                                <segue destination="dIA-gQ-gYI" kind="unwind" identifier="UnwindLinkProperties" unwindAction="unwindLinkProperties:" id="xeQ-O0-lQj"/>
                            </connections>
                        </barButtonItem>
                    </navigationItem>
                    <connections>
                        <outlet property="afterClickURLTextField" destination="axt-rS-OdB" id="LEZ-GX-kmN"/>
                        <outlet property="aliasTextField" destination="V5w-iZ-snS" id="Vhu-Qv-c0w"/>
                        <outlet property="alwaysDeeplinkSwitch" destination="DNt-GX-Ykb" id="BLx-x8-1bI"/>
                        <outlet property="androidDeeplinkPathTextField" destination="1ml-G3-TTc" id="xo7-NN-NJN"/>
                        <outlet property="androidDeepviewTextField" destination="NKL-am-Cdc" id="jKx-1y-abH"/>
                        <outlet property="androidRedirectTimeoutTextField" destination="048-Ef-qL4" id="Ln5-XS-qeo"/>
                        <outlet property="androidURLTextField" destination="6aC-yM-ART" id="gHo-yY-05G"/>
                        <outlet property="blackberryURLTextField" destination="0L3-oL-gau" id="ipc-pU-Eq7"/>
                        <outlet property="campaignTextField" destination="5ya-nz-Zg6" id="WIz-3f-oOi"/>
                        <outlet property="channelTextField" destination="2Uy-GD-t6z" id="8qh-zy-b6o"/>
                        <outlet property="clearAllValuesButton" destination="xaO-ts-SYE" id="lGn-l2-9Hy"/>
                        <outlet property="deeplinkPathTextField" destination="ZXD-aO-bzl" id="bEw-PX-7jv"/>
                        <outlet property="desktopDeepviewTextField" destination="GW9-Xl-Trt" id="Odd-mq-2Bl"/>
                        <outlet property="desktopURLTextField" destination="aVa-wl-KRF" id="zqH-TX-jqw"/>
                        <outlet property="fallbackURLTextField" destination="uqh-Zw-oed" id="1yG-9I-LWN"/>
                        <outlet property="featureTextField" destination="xs7-5a-g2w" id="TLD-ru-YJ3"/>
                        <outlet property="fireURLTextField" destination="qv3-9n-yTX" id="dhE-eu-cyO"/>
                        <outlet property="iosDeeplinkPathTextField" destination="LAh-Yf-YoI" id="xXC-aG-iRV"/>
                        <outlet property="iosDeepviewTextField" destination="XC3-ID-lnH" id="eq3-Aq-DDo"/>
                        <outlet property="iosRedirectTimeoutTextField" destination="5mb-VU-Zzb" id="sOL-tw-Dyg"/>
                        <outlet property="iosURLTextField" destination="xtG-OZ-0tp" id="VFO-6e-V5c"/>
                        <outlet property="iosWeChatURLTextField" destination="B5l-lH-KbQ" id="7dU-lR-Igc"/>
                        <outlet property="iosWeiboURLTextField" destination="z2M-Gb-GHr" id="7iF-y7-yIQ"/>
                        <outlet property="ipadURLTextField" destination="UV6-Os-JFv" id="vat-mt-vj0"/>
                        <outlet property="matchDurationTextField" destination="AbC-iA-R9W" id="H83-tq-taA"/>
                        <outlet property="oneTimeUseSwitch" destination="d0s-hS-3km" id="6ZJ-Vb-K0k"/>
                        <outlet property="stageTextField" destination="RKf-cA-BTP" id="vGt-Zz-8V0"/>
                        <outlet property="tagsTextView" destination="gyD-DA-0yx" id="M4q-Bk-C78"/>
                        <outlet property="webOnlySwitch" destination="2ZF-xX-urF" id="txn-aT-SXp"/>
                        <outlet property="windowsPhoneURLTextField" destination="W9Y-Kz-ydz" id="U4x-ck-J4a"/>
                        <segue destination="9cY-W1-idP" kind="show" identifier="Array" id="Qus-RU-z7K"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ISL-Qe-gS1" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <exit id="dIA-gQ-gYI" userLabel="Exit" sceneMemberID="exit"/>
            </objects>
            <point key="canvasLocation" x="1839" y="1042"/>
        </scene>
        <!--Array TableView-->
        <scene sceneID="w3R-5M-dX3">
            <objects>
                <viewControllerPlaceholder storyboardIdentifier="ArrayTableViewController" storyboardName="ArrayTableView" id="9cY-W1-idP" userLabel="Array TableView" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="l3w-PA-uof" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2594" y="1041"/>
        </scene>
    </scenes>
</document>
