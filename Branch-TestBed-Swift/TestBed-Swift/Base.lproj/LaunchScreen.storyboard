<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="iVK-ts-LAG">
    <device id="ipad12_9" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="zJf-mn-dfc">
            <objects>
                <viewController id="iVK-ts-LAG" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="6JR-1h-cOj"/>
                        <viewControllerLayoutGuide type="bottom" id="hYX-Ej-gOA"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="17V-19-0Jt">
                        <rect key="frame" x="0.0" y="0.0" width="1024" height="1366"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Icon-120" translatesAutoresizingMaskIntoConstraints="NO" id="eTK-Ev-i0z">
                                <rect key="frame" x="392" y="563" width="240" height="240"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="240" id="BgE-vN-XLs"/>
                                    <constraint firstAttribute="height" constant="240" id="GT1-eJ-GNc"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="eTK-Ev-i0z" firstAttribute="centerX" secondItem="17V-19-0Jt" secondAttribute="centerX" id="9N5-Cd-eH8"/>
                            <constraint firstItem="eTK-Ev-i0z" firstAttribute="centerY" secondItem="17V-19-0Jt" secondAttribute="centerY" id="Y5g-9S-ehx"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="MJ2-7l-5Xb" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="940" y="-25"/>
        </scene>
    </scenes>
    <resources>
        <image name="Icon-120" width="120" height="120"/>
    </resources>
</document>
