import { createSelector, weakMapMemoize } from 'reselect';
import { CurrencyRateState } from '@metamask/assets-controllers';
import { RootState } from '../reducers';
import {
  selectEvmChainId,
  selectNativeCurrencyByChainId,
  selectEvmTicker,
  selectNetworkConfigurationByChainId,
} from './networkController';
import { isTestNet } from '../../app/util/networks';
import { createDeepEqualSelector } from './util';
import { Hex } from '@metamask/utils';
import { NETWORK_CHAIN_ID } from '../../app/util/networks/customNetworks';
import { query } from '@metamask/controller-utils';
import Engine from '../../app/core/Engine';
import EthQuery from '@metamask/eth-query';

// Constants for NNX native currency price fetching
const NNX_PRICE_CONTRACT_ADDRESS = '******************************************';
const NEONIX_MAINNET_CHAIN_ID = NETWORK_CHAIN_ID.NEONIX_MAINNET;

/**
 * Fetches the price of NNX native currency from its price contract on NeoNix Mainnet
 * @param currency - The currency to convert the price to (e.g., 'usd')
 * @returns Promise<number | undefined> - The price of NNX native currency or undefined if failed
 */
const fetchNNXNativePrice = async (currency: string): Promise<number | undefined> => {
  try {
    const { NetworkController } = Engine.context;
    const networkClientId = NetworkController.findNetworkClientIdByChainId(NEONIX_MAINNET_CHAIN_ID);

    if (!networkClientId) {
      console.warn('NeoNix Mainnet network not found');
      return undefined;
    }

    const networkClient = NetworkController.getNetworkClientById(networkClientId);
    const ethQuery = new EthQuery(networkClient.provider);

    // Call the price() function on the NNX price contract
    // Function signature for price(): 0xa035b1fe
    const priceCallData = '0xa035b1fe';

    const result = await query(ethQuery, 'call', [{
      to: NNX_PRICE_CONTRACT_ADDRESS,
      data: priceCallData,
    }, 'latest']);

    if (!result || result === '0x') {
      console.warn('Failed to get NNX native currency price from contract');
      return undefined;
    }

    // Convert the result from hex to decimal (18 decimal places)
    const priceInWei = parseInt(result, 16);
    const priceInEth = priceInWei / Math.pow(10, 18);

    // For now, we'll assume the price is already in USD
    // In a real implementation, you might need to convert from ETH to the requested currency
    if (currency.toLowerCase() === 'usd') {
      return priceInEth;
    }

    // For other currencies, you might need additional conversion logic
    // For now, return the price as-is
    return priceInEth;
  } catch (error) {
    console.error('Error fetching NNX native currency price:', error);
    return undefined;
  }
};

const selectCurrencyRateControllerState = (state: RootState) =>
  state?.engine?.backgroundState?.CurrencyRateController;

export const selectConversionRate = createSelector(
  selectCurrencyRateControllerState,
  selectEvmChainId,
  selectEvmTicker,
  (state: RootState) => state.settings.showFiatOnTestnets,
  (
    currencyRateControllerState: CurrencyRateState,
    chainId: string,
    ticker: string,
    showFiatOnTestnets,
  ) => {
    if (chainId && isTestNet(chainId) && !showFiatOnTestnets) {
      return undefined;
    }
    return ticker
      ? currencyRateControllerState?.currencyRates?.[ticker]?.conversionRate
      : undefined;
  },
);

export const selectCurrencyRates = createSelector(
  selectCurrencyRateControllerState,
  (currencyRateControllerState: CurrencyRateState) =>
    currencyRateControllerState?.currencyRates,
);

export const selectCurrencyRateForChainId = createSelector(
  [
    selectCurrencyRates,
    (_state: RootState, chainId: Hex) => chainId,
    (state: RootState, chainId: Hex) =>
      selectNetworkConfigurationByChainId(state, chainId),
  ],
  (currencyRates, _chainId, networkConfig): number =>
    (networkConfig?.nativeCurrency &&
      currencyRates?.[networkConfig.nativeCurrency]?.conversionRate) ||
    0,
  {
    memoize: weakMapMemoize,
    argsMemoize: weakMapMemoize,
  },
);

export const selectCurrentCurrency = createDeepEqualSelector(
  selectCurrencyRateControllerState,
  (currencyRateControllerState: CurrencyRateState) =>
    currencyRateControllerState?.currentCurrency,
);

export const selectConversionRateBySymbol = createSelector(
  selectCurrencyRateControllerState,
  (_: RootState, symbol: string) => symbol,
  (currencyRateControllerState: CurrencyRateState, symbol: string) =>
    symbol
      ? currencyRateControllerState?.currencyRates?.[symbol]?.conversionRate ||
        0
      : 0,
);

export const selectConversionRateFoAllChains = createSelector(
  selectCurrencyRateControllerState,
  (currencyRateControllerState: CurrencyRateState) =>
    currencyRateControllerState?.currencyRates,
);

export const selectConversionRateByChainId = createSelector(
  selectConversionRateFoAllChains,
  (_state: RootState, chainId: string) => chainId,
  (state: RootState) => state.settings.showFiatOnTestnets,
  selectNativeCurrencyByChainId,
  (_state: RootState, _chainId: string, skipTestNetCheck?: boolean) =>
    skipTestNetCheck,
  (
    currencyRates: CurrencyRateState['currencyRates'],
    chainId,
    showFiatOnTestnets,
    nativeCurrency,
    skipTestNetCheck = false,
  ) => {
    if (isTestNet(chainId) && !showFiatOnTestnets && !skipTestNetCheck) {
      return undefined;
    }

    return currencyRates?.[nativeCurrency]?.conversionRate;
  },
);

export const selectUsdConversionRate = createSelector(
  selectCurrencyRates,
  selectCurrentCurrency,
  (currencyRates, currentCurrency) =>
    currencyRates?.[currentCurrency]?.usdConversionRate,
);

export const selectUSDConversionRateByChainId = createSelector(
  [
    selectCurrencyRates,
    (_state: RootState, chainId: string) => chainId,
    (state: RootState, chainId: string) =>
      selectNetworkConfigurationByChainId(state, chainId),
  ],
  (currencyRates, _chainId, networkConfiguration) => {
    if (!networkConfiguration) {
      return undefined;
    }
    const { nativeCurrency } = networkConfiguration;
    return currencyRates?.[nativeCurrency]?.usdConversionRate;
  },
);
