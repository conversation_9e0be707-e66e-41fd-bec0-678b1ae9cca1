import { query } from '@metamask/controller-utils';
import Engine from '../core/Engine';
import EthQuery from '@metamask/eth-query';
import { NETWORK_CHAIN_ID } from './networks/customNetworks';

// Constants for NNX native currency price fetching
const NNX_PRICE_CONTRACT_ADDRESS = '******************************************';
const NEONIX_MAINNET_CHAIN_ID = NETWORK_CHAIN_ID.NEONIX_MAINNET;

/**
 * Fetches the price of NNX native currency from its price contract on NeoNix Mainnet
 * @param currency - The currency to convert the price to (e.g., 'usd')
 * @returns Promise<number | undefined> - The price of NNX native currency or undefined if failed
 */
export const fetchNNXNativePrice = async (currency: string = 'usd'): Promise<number | undefined> => {
  try {
    const { NetworkController } = Engine.context;
    const networkClientId = NetworkController.findNetworkClientIdByChainId(NEONIX_MAINNET_CHAIN_ID);
    
    if (!networkClientId) {
      console.warn('NeoNix Mainnet network not found');
      return undefined;
    }

    const networkClient = NetworkController.getNetworkClientById(networkClientId);
    const ethQuery = new EthQuery(networkClient.provider);

    // Call the price() function on the NNX price contract
    // Function signature for price(): 0xa035b1fe
    const priceCallData = '0xa035b1fe';
    
    const result = await query(ethQuery, 'call', [{
      to: NNX_PRICE_CONTRACT_ADDRESS,
      data: priceCallData,
    }, 'latest']);

    if (!result || result === '0x') {
      console.warn('Failed to get NNX native currency price from contract');
      return undefined;
    }

    // Convert the result from hex to decimal (18 decimal places)
    const priceInWei = parseInt(result, 16);
    const priceInEth = priceInWei / Math.pow(10, 18);

    // For now, we'll assume the price is already in USD
    // In a real implementation, you might need to convert from ETH to the requested currency
    if (currency.toLowerCase() === 'usd') {
      return priceInEth;
    }

    // For other currencies, you might need additional conversion logic
    // For now, return the price as-is
    return priceInEth;
  } catch (error) {
    console.error('Error fetching NNX native currency price:', error);
    return undefined;
  }
};

/**
 * Checks if a given chain ID is NeoNix Mainnet
 * @param chainId - The chain ID to check
 * @returns boolean - True if it's NeoNix Mainnet
 */
export const isNeoNixMainnet = (chainId: string): boolean => {
  return chainId === NEONIX_MAINNET_CHAIN_ID;
};

/**
 * Checks if a token address represents a native token (null, undefined, or zero address)
 * @param address - The token address to check
 * @returns boolean - True if it's a native token
 */
export const isNativeToken = (address?: string): boolean => {
  return !address || address === '******************************************' || address === '0x0';
};

/**
 * Gets the NNX price contract address
 * @returns string - The contract address
 */
export const getNNXPriceContractAddress = (): string => {
  return NNX_PRICE_CONTRACT_ADDRESS;
};

/**
 * Gets the NeoNix Mainnet chain ID
 * @returns string - The chain ID
 */
export const getNeoNixMainnetChainId = (): string => {
  return NEONIX_MAINNET_CHAIN_ID;
};
