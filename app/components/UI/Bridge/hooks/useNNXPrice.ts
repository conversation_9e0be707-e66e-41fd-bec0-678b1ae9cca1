import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentCurrency } from '../../../../selectors/currencyRateController';
import { fetchNNXNativePrice, isNeoNixMainnet, isNativeToken } from '../../../../util/nnx-price-utils';
import { BridgeToken } from '../types';

/**
 * Custom hook to fetch NNX native currency price for NeoNix Mainnet
 * @param token - The bridge token to check and potentially fetch price for
 * @returns Object with price, loading state, and error
 */
export const useNNXPrice = (token?: BridgeToken) => {
  const [price, setPrice] = useState<number | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>(undefined);
  const currentCurrency = useSelector(selectCurrentCurrency);

  useEffect(() => {
    // Only fetch price if this is NNX native token on NeoNix Mainnet
    if (!token || !isNeoNixMainnet(token.chainId) || !isNativeToken(token.address)) {
      setPrice(undefined);
      setIsLoading(false);
      setError(undefined);
      return;
    }

    // If we already have a price in the token, use it
    if (token.currencyExchangeRate) {
      setPrice(token.currencyExchangeRate);
      setIsLoading(false);
      setError(undefined);
      return;
    }

    // Fetch the price from the contract
    const fetchPrice = async () => {
      setIsLoading(true);
      setError(undefined);
      
      try {
        const nnxPrice = await fetchNNXNativePrice(currentCurrency);
        setPrice(nnxPrice);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch NNX price'));
        setPrice(undefined);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPrice();
  }, [token?.chainId, token?.address, token?.currencyExchangeRate, currentCurrency]);

  return {
    price,
    isLoading,
    error,
    isNNXToken: token ? isNeoNixMainnet(token.chainId) && isNativeToken(token.address) : false,
  };
};

/**
 * Hook to get an enhanced bridge token with NNX price populated if applicable
 * @param token - The original bridge token
 * @returns Enhanced bridge token with NNX price if applicable
 */
export const useEnhancedBridgeToken = (token?: BridgeToken): BridgeToken | undefined => {
  const { price, isNNXToken } = useNNXPrice(token);

  if (!token) {
    return undefined;
  }

  // If this is an NNX token and we have a price, enhance the token
  if (isNNXToken && price !== undefined && !token.currencyExchangeRate) {
    return {
      ...token,
      currencyExchangeRate: price,
    };
  }

  return token;
};
