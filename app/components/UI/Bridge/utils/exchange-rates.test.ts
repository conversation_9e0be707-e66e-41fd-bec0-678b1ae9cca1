import { fetchTokenExchangeRates, getTokenExchangeRate } from './exchange-rates';
import { NETWORK_CHAIN_ID } from '../../../../util/networks/customNetworks';
import { query } from '@metamask/controller-utils';
import Engine from '../../../../core/Engine';

// Mock dependencies
jest.mock('@metamask/controller-utils');
jest.mock('../../../../core/Engine');
jest.mock('@metamask/eth-query');
jest.mock('@metamask/assets-controllers', () => ({
  CodefiTokenPricesServiceV2: jest.fn(),
  fetchTokenContractExchangeRates: jest.fn(),
}));

const mockQuery = query as jest.MockedFunction<typeof query>;
const mockEngine = Engine as jest.Mocked<typeof Engine>;

describe('NNX Token Price Fetching', () => {
  const NNX_TOKEN_ADDRESS = '******************************************';
  const NEONIX_MAINNET_CHAIN_ID = NETWORK_CHAIN_ID.NEONIX_MAINNET;
  const mockNetworkClientId = 'neonix-mainnet-client';
  const mockProvider = {};
  const mockEthQuery = {};

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Engine.context.NetworkController
    mockEngine.context = {
      NetworkController: {
        findNetworkClientIdByChainId: jest.fn().mockReturnValue(mockNetworkClientId),
        getNetworkClientById: jest.fn().mockReturnValue({
          provider: mockProvider,
        }),
      },
    } as any;
  });

  describe('fetchTokenExchangeRates', () => {
    it('should fetch NNX price from contract when on NeoNix Mainnet', async () => {
      // Mock the contract call to return a price (1.5 ETH in wei)
      const priceInWei = '0x14d1120d7b160000'; // 1.5 * 10^18 in hex
      mockQuery.mockResolvedValue(priceInWei);

      const result = await fetchTokenExchangeRates(
        NEONIX_MAINNET_CHAIN_ID,
        'usd',
        NNX_TOKEN_ADDRESS
      );

      expect(mockEngine.context.NetworkController.findNetworkClientIdByChainId)
        .toHaveBeenCalledWith(NEONIX_MAINNET_CHAIN_ID);
      expect(mockEngine.context.NetworkController.getNetworkClientById)
        .toHaveBeenCalledWith(mockNetworkClientId);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.anything(),
        'call',
        [{
          to: NNX_TOKEN_ADDRESS,
          data: '0xa035b1fe', // price() function signature
        }, 'latest']
      );

      // Should return the price converted from wei (1.5)
      expect(result[NNX_TOKEN_ADDRESS.toLowerCase()]).toBe(1.5);
    });

    it('should handle contract call failure gracefully', async () => {
      // Mock the contract call to return empty result
      mockQuery.mockResolvedValue('0x');

      const result = await fetchTokenExchangeRates(
        NEONIX_MAINNET_CHAIN_ID,
        'usd',
        NNX_TOKEN_ADDRESS
      );

      expect(result[NNX_TOKEN_ADDRESS.toLowerCase()]).toBeUndefined();
    });

    it('should handle network not found gracefully', async () => {
      // Mock network not found
      mockEngine.context.NetworkController.findNetworkClientIdByChainId
        .mockReturnValue(null);

      const result = await fetchTokenExchangeRates(
        NEONIX_MAINNET_CHAIN_ID,
        'usd',
        NNX_TOKEN_ADDRESS
      );

      expect(result[NNX_TOKEN_ADDRESS.toLowerCase()]).toBeUndefined();
    });

    it('should use standard fetching for non-NNX tokens on NeoNix Mainnet', async () => {
      const { fetchTokenContractExchangeRates } = require('@metamask/assets-controllers');
      const mockStandardResult = { '0x123': 2.5 };
      fetchTokenContractExchangeRates.mockResolvedValue(mockStandardResult);

      const result = await fetchTokenExchangeRates(
        NEONIX_MAINNET_CHAIN_ID,
        'usd',
        '0x123'
      );

      expect(fetchTokenContractExchangeRates).toHaveBeenCalled();
      expect(result['0x123']).toBe(2.5);
    });

    it('should use standard fetching for other networks', async () => {
      const { fetchTokenContractExchangeRates } = require('@metamask/assets-controllers');
      const mockStandardResult = { [NNX_TOKEN_ADDRESS]: 3.0 };
      fetchTokenContractExchangeRates.mockResolvedValue(mockStandardResult);

      const result = await fetchTokenExchangeRates(
        '0x1', // Ethereum mainnet
        'usd',
        NNX_TOKEN_ADDRESS
      );

      expect(fetchTokenContractExchangeRates).toHaveBeenCalled();
      expect(mockQuery).not.toHaveBeenCalled();
      expect(result[NNX_TOKEN_ADDRESS]).toBe(3.0);
    });
  });

  describe('getTokenExchangeRate', () => {
    it('should fetch NNX price directly for NNX token on NeoNix Mainnet', async () => {
      const priceInWei = '0x1bc16d674ec80000'; // 2.0 * 10^18 in hex
      mockQuery.mockResolvedValue(priceInWei);

      const result = await getTokenExchangeRate({
        chainId: NEONIX_MAINNET_CHAIN_ID,
        tokenAddress: NNX_TOKEN_ADDRESS,
        currency: 'usd',
      });

      expect(result).toBe(2.0);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.anything(),
        'call',
        [{
          to: NNX_TOKEN_ADDRESS,
          data: '0xa035b1fe',
        }, 'latest']
      );
    });

    it('should use standard fetching for non-NNX tokens', async () => {
      const { fetchTokenContractExchangeRates } = require('@metamask/assets-controllers');
      const mockStandardResult = { '0x456': 4.0 };
      fetchTokenContractExchangeRates.mockResolvedValue(mockStandardResult);

      const result = await getTokenExchangeRate({
        chainId: NEONIX_MAINNET_CHAIN_ID,
        tokenAddress: '0x456',
        currency: 'usd',
      });

      expect(fetchTokenContractExchangeRates).toHaveBeenCalled();
      expect(mockQuery).not.toHaveBeenCalled();
    });
  });
});
