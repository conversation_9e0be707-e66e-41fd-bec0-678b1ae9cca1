import { useAsyncResultOrThrow } from '../../../hooks/useAsyncResult';
import { safeToChecksumAddress } from '../../../../util/address';
import {
  CodefiTokenPricesServiceV2,
  fetchTokenContractExchangeRates,
  ContractExchangeRates,
} from '@metamask/assets-controllers';
import { Quote } from '@metamask/swaps-controller/dist/types';
import { Hex } from '@metamask/utils';


/**
 * Fetches the price of NNX token from its price contract on NeoNix Mainnet
 * @param currency - The currency to convert the price to (e.g., 'usd')
 * @returns Promise<number | undefined> - The price of NNX token or undefined if failed
 */
const fetchNNXTokenPrice = async (currency: string): Promise<number | undefined> => {
  try {
    const { NetworkController } = Engine.context;
    const networkClientId = NetworkController.findNetworkClientIdByChainId(NEONIX_MAINNET_CHAIN_ID);

    if (!networkClientId) {
      console.warn('NeoNix Mainnet network not found');
      return undefined;
    }

    const networkClient = NetworkController.getNetworkClientById(networkClientId);
    const ethQuery = new EthQuery(networkClient.provider);

    // Call the price() function on the NNX price contract
    // Function signature for price(): 0xa035b1fe
    const priceCallData = '0xa035b1fe';

    const result = await query(ethQuery, 'call', [{
      to: NNX_TOKEN_ADDRESS,
      data: priceCallData,
    }, 'latest']);

    if (!result || result === '0x') {
      console.warn('Failed to get NNX price from contract');
      return undefined;
    }

    // Convert the result from hex to decimal (18 decimal places)
    const priceInWei = parseInt(result, 16);
    const priceInEth = priceInWei / Math.pow(10, 18);

    // For now, we'll assume the price is already in USD
    // In a real implementation, you might need to convert from ETH to the requested currency
    if (currency.toLowerCase() === 'usd') {
      return priceInEth;
    }

    // For other currencies, you might need additional conversion logic
    // For now, return the price as-is
    return priceInEth;
  } catch (error) {
    console.error('Error fetching NNX token price:', error);
    return undefined;
  }
};

interface TokenFee {
  token?: {
    address: string;
    decimals: number;
    symbol: string;
  };
  balanceNeededToken?: string;
}

interface FiatConversionRatesParams {
  canUseGasIncludedSwap: boolean;
  selectedQuote: Quote | null;
  tradeTxTokenFee: TokenFee;
  currentCurrency: string;
  chainId: Hex;
}

/**
 * Custom hook to fetch fiat conversion rates for token fees in gas-included swaps
 * @param {Object} options - The options for fetching conversion rates
 * @param {boolean} options.canUseGasIncludedSwap - Whether gas-included swap feature is available
 * @param {Object} options.selectedQuote - The currently selected quote
 * @param {Object} options.tradeTxTokenFee - The token fee information for the trade
 * @param {string} options.currentCurrency - The current currency
 * @param {string} options.chainId - The chain ID
 * @returns {Object|undefined} The token exchange rates or undefined if not applicable
 */
export function useFiatConversionRates({
  canUseGasIncludedSwap,
  selectedQuote,
  tradeTxTokenFee,
  currentCurrency,
  chainId,
}: FiatConversionRatesParams) {
  return useAsyncResultOrThrow<ContractExchangeRates | undefined>(async () => {
    if (!canUseGasIncludedSwap || !selectedQuote?.trade) {
      return undefined;
    }

    const { token, balanceNeededToken } = tradeTxTokenFee;
    if (!token?.decimals || !token?.address || !balanceNeededToken) {
      return undefined;
    }

    const checksumAddress = safeToChecksumAddress(token.address);
    if (!checksumAddress) return undefined;

    // Special handling for NNX token on NeoNix Mainnet
    const isNeoNixMainnet = chainId === NEONIX_MAINNET_CHAIN_ID;
    const isNNXToken = checksumAddress.toLowerCase() === NNX_TOKEN_ADDRESS.toLowerCase();

    if (isNeoNixMainnet && isNNXToken) {
      const nnxPrice = await fetchNNXTokenPrice(currentCurrency);
      if (nnxPrice !== undefined) {
        return { [checksumAddress]: nnxPrice };
      }
      return {};
    }

    return fetchTokenContractExchangeRates({
      tokenPricesService: new CodefiTokenPricesServiceV2(),
      nativeCurrency: currentCurrency,
      tokenAddresses: [checksumAddress],
      chainId,
    });
  }, [
    canUseGasIncludedSwap,
    selectedQuote?.trade,
    tradeTxTokenFee,
    currentCurrency,
    chainId,
  ]);
}
