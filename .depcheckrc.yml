# List things here that *are - 'used, that depcheck is wrong about'
ignores:
  - '@metamask/oss-attribution-generator'
  - '@metamask/test-dapp-multichain'
  - 'webpack-cli'
  - '@react-native-community/datetimepicker'
  - '@react-native-community/slider'
  - 'patch-package'
  - '@lavamoat/allow-scripts'
  - '@lavamoat/git-safe-dependencies'
  - 'babel-plugin-inline-import'
  # This is used in index.d.ts
  - '@sentry/react'
  # These dependencies are needed for react-compiler - https://react.dev/learn/react-compiler#using-react-compiler-with-react-17-or-18
  - 'react-compiler-runtime'
  # This is used on the patch for TokenRatesController of Assets controllers, for we to be able to use the last version of it
  - cockatiel
  # This is used for the buffer polyfills
  - '@craftzdog/react-native-buffer'
  # These are used in metro.config.js to setup Hardened JavaScript via @lavamoat/react-native-lockdown
  - '@react-native/js-polyfills'
  - 'reflect-metadata'
  # Husky is used for pre-commit hooks
  - 'husky'
  # Remove this once it's used in the project
  - 'rive-react-native'
  # Appwright will be used in a follow up PR. We will remove once PR is ready
  - 'appwright'
  # Appium drivers are used by Appwright for mobile automation
  - 'appium-uiautomator2-driver'
  - 'appium-xcuitest-driver'

  # Note: Everything below this line should be removed after investigation
  # TODO: Investigate each dependency to see whether it's used

  ## Unused dependencies to investigate
  - '@babel/preset-env'
  - '@babel/runtime'
  - '@cucumber/message-streams'
  - '@cucumber/messages'
  - '@metamask/mobile-provider'
  - '@rpii/wdio-html-reporter'
  - '@testing-library/react'
  - '@testing-library/react-hooks'
  - '@types/jest'
  - '@types/react-native-video'
  - '@wdio/appium-service'
  - '@wdio/browserstack-service'
  - '@wdio/junit-reporter'
  - '@wdio/local-runner'
  - '@wdio/spec-reporter'
  - 'appium'
  - 'assert'
  - 'babel-core'
  - 'babel-loader'
  - 'chromedriver'
  - 'eslint-config-prettier'
  - 'eslint-plugin-prettier'
  - 'eslint-plugin-react-native'
  - 'execa'
  - 'fbjs-scripts'
  - 'improved-yarn-audit'
  - 'jetifier'
  - 'metro-react-native-babel-preset'
  - 'prettier-plugin-gherkin'
  - 'react-native-svg-asset-plugin'
  - 'regenerator-runtime'

  ## Unused devDependencies to investigate
  - '@metamask/swappable-obj-proxy'
  - '@segment/sovran-react-native'
  - '@tradle/react-native-http'
  - 'asyncstorage-down'
  - 'buffer'
  - 'd3-shape'
  - 'eciesjs'
  - 'eth-block-tracker'
  - 'eth-json-rpc-infura'
  - 'events'
  - 'https-browserify'
  - 'path'
  - 'pbkdf2'
  - 'pify'
  - 'punycode'
  - 'randomfill'
  - 'react-native-aes-crypto'
  - 'react-native-aes-crypto-forked'
  - 'react-native-crypto'
  - 'react-native-level-fs'
  - 'react-native-os'
  - 'react-native-randombytes'
  - 'react-native-swipe-gestures'
  - 'react-native-tcp'
  - 'socket.io-client'
  - 'stream-browserify'
  - 'url'
  - 'vm-browserify'
  - 'react-native-cli'
  - 'babel-plugin-module-resolver'

  ## Missing dependencies to investigate
  - '@react-navigation/core'
  - 'app'
  - 'i18n-js'
  - 'images'

  ## Expo
  - '@config-plugins/detox'
  - 'cross-spawn'
  - 'expo-build-properties'
  - 'expo-dev-client'

  ## react native
  - '@react-native-community/cli'
  - '@react-native-community/cli-platform-android'
  - '@react-native-community/cli-platform-ios'
  - '@react-native-community/cli-server-api'
  - '@react-native/typescript-config'
  - 'react-native-pager-view'
  # this dependency can probably be removed, needs investigation
  - '@types/react-test-renderer'
