sonar.projectKey=metamask-mobile
sonar.organization=consensys

# This is the name and version displayed in the SonarCloud UI.
sonar.projectName=MetaMask Mobile Project
#sonar.projectVersion=1.0

# Root for sonar analysis.
sonar.sources=app/

# Excluded project files from analysis.
sonar.exclusions=**.stories.**, e2e/**, wdio/**

# Inclusions for test files.
sonar.test.inclusions=**.test.**

# Excluded project files from coverage.
sonar.coverage.exclusions=**util/test**, **/__mocks__/**, **NetworkConnectMultiSelector.tsx, **TransactionNotification/index.js, **BrowserTab.tsx, **RPCMethodMiddleware.ts, **PermissionsSummary.tsx, **useSwitchNetworks.ts, **ethereum-chain-utils.js, **app/selectors/selectedNetworkController.ts, **useAddressBalance.ts, **AccountPermissions.tsx, **AddressFrom.tsx, **TransactionDetails**, **TransactionElement**, **networkController.ts, **util/networks/index.js, **AccountFromToInfoCard.tsx, **Engine/Engine.ts, **TransactionReview/index.js, **smart-publish-hook.ts, **AccountFromToInfoCard.types.tsx, **BrowserTab.tsx, **NetworkSelector.tsx

# Test coverage path in GitHub action
sonar.javascript.lcov.reportPaths=/coverage/lcov.info

# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
