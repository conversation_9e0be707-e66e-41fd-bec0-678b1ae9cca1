diff --git a/node_modules/@metamask/transaction-controller/dist/TransactionController.cjs b/node_modules/@metamask/transaction-controller/dist/TransactionController.cjs
index 986bdf1..2ae9d76 100644
--- a/node_modules/@metamask/transaction-controller/dist/TransactionController.cjs
+++ b/node_modules/@metamask/transaction-controller/dist/TransactionController.cjs
@@ -97,6 +97,12 @@ const metadata = {
         anonymous: false,
         usedInUi: false,
     },
+    swapsTransactions: {
+        includeInStateLogs: true,
+        persist: true,
+        anonymous: false,
+        usedInUi: true,
+    },
 };
 const SUBMIT_HISTORY_LIMIT = 100;
 /**
diff --git a/node_modules/@metamask/transaction-controller/dist/TransactionController.mjs b/node_modules/@metamask/transaction-controller/dist/TransactionController.mjs
index c331c07..31a620c 100644
--- a/node_modules/@metamask/transaction-controller/dist/TransactionController.mjs
+++ b/node_modules/@metamask/transaction-controller/dist/TransactionController.mjs
@@ -99,6 +99,12 @@ const metadata = {
         anonymous: false,
         usedInUi: false,
     },
+    swapsTransactions: {
+        includeInStateLogs: true,
+        persist: true,
+        anonymous: false,
+        usedInUi: true,
+    },
 };
 const SUBMIT_HISTORY_LIMIT = 100;
 /**
