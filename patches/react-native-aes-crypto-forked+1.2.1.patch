diff --git a/node_modules/react-native-aes-crypto-forked/android/build.gradle b/node_modules/react-native-aes-crypto-forked/android/build.gradle
index e717caf..8eb29c8 100644
--- a/node_modules/react-native-aes-crypto-forked/android/build.gradle
+++ b/node_modules/react-native-aes-crypto-forked/android/build.gradle
@@ -1,12 +1,12 @@
 apply plugin: 'com.android.library'
 
 android {
-    compileSdkVersion 26
-    buildToolsVersion "26.0.1"
+    compileSdkVersion 33
+    buildToolsVersion "33.0.0"
 
     defaultConfig {
-        minSdkVersion 19
-        targetSdkVersion 22
+        minSdkVersion 21
+        targetSdkVersion 33
         versionCode 1
         versionName "1.0"
     }
@@ -19,10 +19,10 @@ android {
 }
 
 dependencies {
-    compile 'com.android.support:appcompat-v7:23.0.1'
-    compile 'com.facebook.react:react-native:+'
-    compile 'com.madgag.spongycastle:core:1.58.0.0'
-    compile 'com.madgag.spongycastle:prov:1.54.0.0'
-    compile 'com.madgag.spongycastle:pkix:1.54.0.0'
-    compile 'com.madgag.spongycastle:pg:1.54.0.0'
+    implementation 'com.android.support:appcompat-v7:23.0.1'
+    implementation 'com.facebook.react:react-native:+'
+    implementation 'com.madgag.spongycastle:core:1.58.0.0'
+    implementation 'com.madgag.spongycastle:prov:1.54.0.0'
+    implementation 'com.madgag.spongycastle:pkix:1.54.0.0'
+    implementation 'com.madgag.spongycastle:pg:1.54.0.0'
 }
diff --git a/node_modules/react-native-aes-crypto-forked/android/build.gradle.orig b/node_modules/react-native-aes-crypto-forked/android/build.gradle.orig
new file mode 100644
index 0000000..8784966
--- /dev/null
+++ b/node_modules/react-native-aes-crypto-forked/android/build.gradle.orig
@@ -0,0 +1,28 @@
+apply plugin: 'com.android.library'
+
+android {
+    compileSdkVersion 33
+    buildToolsVersion "33.0.0"
+
+    defaultConfig {
+        minSdkVersion 21
+        targetSdkVersion 33
+        versionCode 1
+        versionName "1.0"
+    }
+    buildTypes {
+        release {
+            minifyEnabled false
+            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
+        }
+    }
+}
+
+dependencies {
+    compile 'com.android.support:appcompat-v7:23.0.1'
+    compile 'com.facebook.react:react-native:+'
+    compile 'com.madgag.spongycastle:core:1.58.0.0'
+    compile 'com.madgag.spongycastle:prov:1.54.0.0'
+    compile 'com.madgag.spongycastle:pkix:1.54.0.0'
+    compile 'com.madgag.spongycastle:pg:1.54.0.0'
+}
diff --git a/node_modules/react-native-aes-crypto-forked/android/build.gradle.rej b/node_modules/react-native-aes-crypto-forked/android/build.gradle.rej
new file mode 100644
index 0000000..2ce2e4e
--- /dev/null
+++ b/node_modules/react-native-aes-crypto-forked/android/build.gradle.rej
@@ -0,0 +1,8 @@
+@@ -1,5 +1,5 @@
+ apply plugin: 'com.android.library'
+ 
+ android {
+-    compileSdkVersion 26
+-    buildToolsVersion "26.0.1"
++    compileSdkVersion 33
++    buildToolsVersion "33.0.0"
diff --git a/node_modules/react-native-aes-crypto-forked/ios/.DS_Store b/node_modules/react-native-aes-crypto-forked/ios/.DS_Store
new file mode 100644
index 0000000..427e223
Binary files /dev/null and b/node_modules/react-native-aes-crypto-forked/ios/.DS_Store differ
diff --git a/node_modules/react-native-aes-crypto-forked/ios/RCTAesForked.xcodeproj/project.pbxproj b/node_modules/react-native-aes-crypto-forked/ios/RCTAesForked.xcodeproj/project.pbxproj
index 1c332b1..bccbf9b 100644
--- a/node_modules/react-native-aes-crypto-forked/ios/RCTAesForked.xcodeproj/project.pbxproj
+++ b/node_modules/react-native-aes-crypto-forked/ios/RCTAesForked.xcodeproj/project.pbxproj
@@ -118,6 +118,7 @@
 			developmentRegion = English;
 			hasScannedForEncodings = 0;
 			knownRegions = (
+				English,
 				en,
 			);
 			mainGroup = 32D980D41BE9F11C00FA27E5;
@@ -231,9 +232,11 @@
 				HEADER_SEARCH_PATHS = (
 					"$(inherited)",
 					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../../../react-native/React/**",
-					"$(SRCROOT)/../../../../../node_modules/react-native/React/**",
-					"$(SRCROOT)/../../../ios/Pods/Headers/Public/React-Core"
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public/RCTDeprecation",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public/React-Core",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Private/React-Core",
+					"$(SRCROOT)/../../react-native/React/**",
 				);
 				OTHER_LDFLAGS = "-ObjC";
 				PRODUCT_NAME = "$(TARGET_NAME)";
@@ -247,9 +250,11 @@
 				HEADER_SEARCH_PATHS = (
 					"$(inherited)",
 					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../../../react-native/React/**",
-					"$(SRCROOT)/../../../../../node_modules/react-native/React/**",
-					"$(SRCROOT)/../../../ios/Pods/Headers/Public/React-Core"
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public/RCTDeprecation",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public/React-Core",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Public",
+					"$(SRCROOT)/../../../ios/Pods/Headers/Private/React-Core",
+					"$(SRCROOT)/../../react-native/React/**",
 				);
 				OTHER_LDFLAGS = "-ObjC";
 				PRODUCT_NAME = "$(TARGET_NAME)";