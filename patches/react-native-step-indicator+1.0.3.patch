diff --git a/node_modules/react-native-step-indicator/src/index.tsx b/node_modules/react-native-step-indicator/src/index.tsx
index 222aeb6..5d08f41 100644
--- a/node_modules/react-native-step-indicator/src/index.tsx
+++ b/node_modules/react-native-step-indicator/src/index.tsx
@@ -49,6 +49,8 @@ interface DefaultStepIndicatorStyles {
     | undefined;
   currentStepLabelColor: string;
   labelFontFamily?: string;
+  finishedStepLabelColor: string;
+  stepIndicatorLabelFontFamily: string;
 }
 
 const defaultStyles: DefaultStepIndicatorStyles = {
@@ -251,7 +253,9 @@ const StepIndicator = ({
     }
     var labelViews = labels.map((label, index) => {
       const selectedStepLabelStyle =
-        index === currentPosition
+        index < currentPosition
+          ? { color: customStyles.finishedStepLabelColor }
+          : index === currentPosition
           ? { color: customStyles.currentStepLabelColor }
           : { color: customStyles.labelColor };
       return (
@@ -356,6 +360,7 @@ const StepIndicator = ({
           overflow: 'hidden',
           fontSize: customStyles.stepIndicatorLabelFontSize,
           color: customStyles.stepIndicatorLabelUnFinishedColor,
+          fontFamily: customStyles.stepIndicatorLabelFontFamily,
         };
         break;
       }
diff --git a/node_modules/react-native-step-indicator/src/types.ts b/node_modules/react-native-step-indicator/src/types.ts
index e7f7c06..507daed 100644
--- a/node_modules/react-native-step-indicator/src/types.ts
+++ b/node_modules/react-native-step-indicator/src/types.ts
@@ -204,6 +204,15 @@ export interface StepIndicatorStyles {
    */
   currentStepLabelColor?: string;
 
+  /**
+   * Color of the finished step label
+   *
+   * @default '#4aae4f'
+   * @type {string}
+   * @memberof StepIndicatorStyles
+   */
+  finishedStepLabelColor?: string;
+
   /**
    * Font size for the labels
    *
@@ -237,6 +246,15 @@ export interface StepIndicatorStyles {
    *
    */
   labelFontFamily?: string;
+
+  /**
+   * stepIndicator fontFamily for custom fonts
+   *
+   * @type {string}
+   * @memberof StepIndicatorStyles
+   *
+   */
+  stepIndicatorLabelFontFamily?: string;
 }
 
 export interface StepIndicatorProps {
diff --git a/node_modules/react-native-step-indicator/src/types.tsx b/node_modules/react-native-step-indicator/src/types.tsx
new file mode 100644
index 0000000..e69de29
