diff --git a/node_modules/@metamask/react-native-button/Button.js b/node_modules/@metamask/react-native-button/Button.js
index 46dc1e0..53b205c 100644
--- a/node_modules/@metamask/react-native-button/Button.js
+++ b/node_modules/@metamask/react-native-button/Button.js
@@ -5,9 +5,10 @@ import {
   Text,
   TouchableOpacity,
   View,
-  ViewPropTypes,
 } from 'react-native';
 
+import { ViewPropTypes, TextPropTypes } from 'deprecated-react-native-prop-types';
+
 import coalesceNonElementChildren from './coalesceNonElementChildren';
 
 const systemButtonOpacity = 0.2;
@@ -16,12 +17,12 @@ export default class Button extends Component {
   static propTypes = {
     ...TouchableOpacity.propTypes,
     accessibilityLabel: PropTypes.string,
-    allowFontScaling: Text.propTypes.allowFontScaling,
+    allowFontScaling: TextPropTypes.allowFontScaling,
     containerStyle: ViewPropTypes.style,
     disabledContainerStyle: ViewPropTypes.style,
     disabled: PropTypes.bool,
-    style: Text.propTypes.style,
-    styleDisabled: Text.propTypes.style,
+    style: TextPropTypes.style,
+    styleDisabled: TextPropTypes.style,
     childGroupStyle: ViewPropTypes.style,
   };
 
