diff --git a/node_modules/@react-native-clipboard/clipboard/android/src/main/java/com/reactnativecommunity/clipboard/ClipboardModule.java b/node_modules/@react-native-clipboard/clipboard/android/src/main/java/com/reactnativecommunity/clipboard/ClipboardModule.java
index b68b858..532f3c9 100644
--- a/node_modules/@react-native-clipboard/clipboard/android/src/main/java/com/reactnativecommunity/clipboard/ClipboardModule.java
+++ b/node_modules/@react-native-clipboard/clipboard/android/src/main/java/com/reactnativecommunity/clipboard/ClipboardModule.java
@@ -64,6 +64,18 @@ public class ClipboardModule extends NativeClipboardModuleSpec {
     return (ClipboardManager) reactContext.getSystemService(Context.CLIPBOARD_SERVICE);
   }
 
+  @ReactMethod
+  public void clearString(Promise promise) {
+    try {
+      ClipboardManager clipboard = getClipboardService();
+      clipboard.clearPrimaryClip();
+      promise.resolve(true);
+    } catch (Exception e) {
+      e.printStackTrace();
+      promise.reject(e);
+    }
+  }
+
   @ReactMethod
   public void getString(Promise promise) {
     try {
diff --git a/node_modules/@react-native-clipboard/clipboard/android/src/paper/java/com/reactnativecommunity/clipboard/NativeClipboardModuleSpec.java b/node_modules/@react-native-clipboard/clipboard/android/src/paper/java/com/reactnativecommunity/clipboard/NativeClipboardModuleSpec.java
index e367325..32335ce 100644
--- a/node_modules/@react-native-clipboard/clipboard/android/src/paper/java/com/reactnativecommunity/clipboard/NativeClipboardModuleSpec.java
+++ b/node_modules/@react-native-clipboard/clipboard/android/src/paper/java/com/reactnativecommunity/clipboard/NativeClipboardModuleSpec.java
@@ -25,6 +25,10 @@ public abstract class NativeClipboardModuleSpec extends ReactContextBaseJavaModu
     super(reactContext);
   }
 
+  @ReactMethod
+  @DoNotStrip
+  public abstract void clearString(Promise promise);
+
   @ReactMethod
   @DoNotStrip
   public abstract void getString(Promise promise);
diff --git a/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.d.ts b/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.d.ts
index 6c85195..3a9302e 100644
--- a/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.d.ts
+++ b/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.d.ts
@@ -90,6 +90,25 @@ export declare const Clipboard: {
      * }
      * ```
      */
+    /**
+     * [IOS ONLY] Set content of string type with an expiry date of 60 seconds. You can use following code to set clipboard content
+     * ```javascript
+     * _setContent() {
+     *   Clipboard.setStringExpire('hello world');
+     * }
+     * ```
+     * @param the content to be stored in the clipboard.
+     */
+	setStringExpire(content: string): void;
+	/**
+     * [ANDROID ONLY] Clears the primary clip on Android
+     * ```javascript
+     * _clearContent() {
+     *   Clipboard.clearString();
+     * }
+     * ```
+     */
+	clearString(): void;
     hasString(): Promise<boolean>;
     /**
      * Returns whether the clipboard has an image or is empty.
diff --git a/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.js b/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.js
index ccdf309..619600c 100644
--- a/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.js
+++ b/node_modules/@react-native-clipboard/clipboard/dist/Clipboard.js
@@ -127,6 +127,29 @@ exports.Clipboard = {
     setStrings(content) {
         NativeClipboardModule_1.default.setStrings(content);
     },
+    	/**
+     * [IOS ONLY] Set content of string type. You can use following code to set clipboard content
+     * ```javascript
+     * _setContent() {
+     *   Clipboard.setString('hello world');
+     * }
+     * ```
+     * @param the content to be stored in the clipboard.
+     */
+	setStringExpire: function (content) {
+        NativeClipboardModule_1.default.setStringExpire(content);
+	},
+	/**
+     * [ANDROID ONLY] Clears the primary clip on Android
+     * ```javascript
+     * _clearContent() {
+     *   Clipboard.clearString();
+     * }
+     * ```
+     */
+	clearString: function () {
+		NativeClipboardModule_1.default.clearString();
+	},
     /**
      * Returns whether the clipboard has content or is empty.
      * This method returns a `Promise`, so you can use following code to get clipboard content
diff --git a/node_modules/@react-native-clipboard/clipboard/ios/RNCClipboard.mm b/node_modules/@react-native-clipboard/clipboard/ios/RNCClipboard.mm
index 3ee5caa..f6bd6f3 100644
--- a/node_modules/@react-native-clipboard/clipboard/ios/RNCClipboard.mm
+++ b/node_modules/@react-native-clipboard/clipboard/ios/RNCClipboard.mm
@@ -6,6 +6,7 @@
 #import <React/RCTBridge.h>
 #import <React/RCTEventDispatcher.h>
 
+#import <MobileCoreServices/UTCoreTypes.h>
 
 @implementation RNCClipboard {
     BOOL isObserving;
@@ -51,6 +52,16 @@ NSString *const CLIPBOARD_TEXT_CHANGED = @"RNCClipboard_TEXT_CHANGED";
     }
 }
 
+RCT_EXPORT_METHOD(setStringExpire:(NSString *)content)
+{
+  NSMutableDictionary *text = [NSMutableDictionary dictionaryWithCapacity:1];
+  [text setValue:content forKey:(NSString *)kUTTypeUTF8PlainText];
+
+  NSArray *pasteboardItems = @[text];
+  NSDictionary *pasteboardOptions = @{UIPasteboardOptionExpirationDate : [[NSDate date] dateByAddingTimeInterval:60]};
+  [[UIPasteboard generalPasteboard] setItems:pasteboardItems options:pasteboardOptions];
+}
+
 RCT_EXPORT_METHOD(setListener)
 {
     [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(listener:) name:UIPasteboardChangedNotification object:nil];
