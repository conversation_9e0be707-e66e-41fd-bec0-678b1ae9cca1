diff --git a/node_modules/@metamask/preferences-controller/dist/PreferencesController.cjs b/node_modules/@metamask/preferences-controller/dist/PreferencesController.cjs
index 83eb804..6033da4 100644
--- a/node_modules/@metamask/preferences-controller/dist/PreferencesController.cjs
+++ b/node_modules/@metamask/preferences-controller/dist/PreferencesController.cjs
@@ -17,7 +17,7 @@ const metadata = {
     isIpfsGatewayEnabled: { persist: true, anonymous: true },
     isMultiAccountBalancesEnabled: { persist: true, anonymous: true },
     lostIdentities: { persist: true, anonymous: false },
-    openSeaEnabled: { persist: true, anonymous: true },
+    displayNftMedia: { persist: true, anonymous: true },
     securityAlertsEnabled: { persist: true, anonymous: true },
     selectedAddress: { persist: true, anonymous: false },
     showTestNetworks: { persist: true, anonymous: true },
@@ -26,13 +26,14 @@ const metadata = {
     useTokenDetection: { persist: true, anonymous: true },
     smartTransactionsOptInStatus: { persist: true, anonymous: false },
     useTransactionSimulations: { persist: true, anonymous: true },
-    useMultiRpcMigration: { persist: true, anonymous: true },
+    showMultiRpcModal: { persist: true, anonymous: true },
     useSafeChainsListValidation: { persist: true, anonymous: true },
     tokenSortConfig: { persist: true, anonymous: true },
     privacyMode: { persist: true, anonymous: true },
     dismissSmartAccountSuggestionEnabled: { persist: true, anonymous: true },
     smartAccountOptIn: { persist: true, anonymous: true },
     smartAccountOptInForAccounts: { persist: true, anonymous: true },
+    tokenNetworkFilter: { persist: true, anonymous: false },
 };
 const name = 'PreferencesController';
 /**
@@ -48,7 +49,7 @@ function getDefaultPreferencesState() {
         isIpfsGatewayEnabled: true,
         isMultiAccountBalancesEnabled: true,
         lostIdentities: {},
-        openSeaEnabled: false,
+        displayNftMedia: false,
         securityAlertsEnabled: false,
         selectedAddress: '',
         showIncomingTransactions: {
@@ -77,12 +78,12 @@ function getDefaultPreferencesState() {
         showTestNetworks: false,
         useNftDetection: false,
         useTokenDetection: true,
-        useMultiRpcMigration: true,
+        showMultiRpcModal: false,
         smartTransactionsOptInStatus: true,
         useTransactionSimulations: true,
         useSafeChainsListValidation: true,
         tokenSortConfig: {
-            key: 'tokenFiatAmount',
+            key: 'tokenFiatBalance',
             order: 'dsc',
             sortCallback: 'stringNumeric',
         },
@@ -90,6 +91,7 @@ function getDefaultPreferencesState() {
         dismissSmartAccountSuggestionEnabled: false,
         smartAccountOptIn: true,
         smartAccountOptInForAccounts: [],
+        tokenNetworkFilter: {},
     };
 }
 exports.getDefaultPreferencesState = getDefaultPreferencesState;
@@ -228,22 +230,22 @@ class PreferencesController extends base_controller_1.BaseController {
      * @param useNftDetection - Boolean indicating user preference on NFT detection.
      */
     setUseNftDetection(useNftDetection) {
-        if (useNftDetection && !this.state.openSeaEnabled) {
-            throw new Error('useNftDetection cannot be enabled if openSeaEnabled is false');
+        if (useNftDetection && !this.state.displayNftMedia) {
+            throw new Error('useNftDetection cannot be enabled if displayNftMedia is false');
         }
         this.update((state) => {
             state.useNftDetection = useNftDetection;
         });
     }
     /**
-     * Toggle the opensea enabled setting.
+     * Toggle the display nft media enabled setting.
      *
-     * @param openSeaEnabled - Boolean indicating user preference on using OpenSea's API.
+     * @param displayNftMedia - Boolean indicating user preference on using web2 third parties.
      */
-    setOpenSeaEnabled(openSeaEnabled) {
+    setDisplayNftMedia(displayNftMedia) {
         this.update((state) => {
-            state.openSeaEnabled = openSeaEnabled;
-            if (!openSeaEnabled) {
+           state.displayNftMedia = displayNftMedia;
+           if (!displayNftMedia) {
                 state.useNftDetection = false;
             }
         });
@@ -307,13 +309,13 @@ class PreferencesController extends base_controller_1.BaseController {
     /**
      * Toggle multi rpc migration modal.
      *
-     * @param useMultiRpcMigration - Boolean indicating if the multi rpc modal will be displayed or not.
+     * @param showMultiRpcModal - Boolean indicating if the multi rpc modal will be displayed or not.
      */
-    setUseMultiRpcMigration(useMultiRpcMigration) {
+    setShowMultiRpcModal(showMultiRpcModal) {
         this.update((state) => {
-            state.useMultiRpcMigration = useMultiRpcMigration;
-            if (!useMultiRpcMigration) {
-                state.useMultiRpcMigration = false;
+            state.showMultiRpcModal = showMultiRpcModal;
+            if (!showMultiRpcModal) {
+               state.showMultiRpcModal = false;
             }
         });
     }
@@ -399,6 +401,16 @@ class PreferencesController extends base_controller_1.BaseController {
             state.smartAccountOptInForAccounts = accounts;
         });
     }
+    /**
+     * Set the token network filter configuration setting.
+     *
+     * @param tokenNetworkFilter - Object describing token sort configuration.
+     */
+    setTokenNetworkFilter(tokenNetworkFilter) {
+      this.update((state) => {
+        state.tokenNetworkFilter = tokenNetworkFilter;
+      });
+    }
 }
 exports.PreferencesController = PreferencesController;
 _PreferencesController_instances = new WeakSet(), _PreferencesController_syncIdentities = function _PreferencesController_syncIdentities(addresses) {
diff --git a/node_modules/@metamask/preferences-controller/dist/PreferencesController.d.cts b/node_modules/@metamask/preferences-controller/dist/PreferencesController.d.cts
index 7c24a04..892e601 100644
--- a/node_modules/@metamask/preferences-controller/dist/PreferencesController.d.cts
+++ b/node_modules/@metamask/preferences-controller/dist/PreferencesController.d.cts
@@ -71,7 +71,7 @@ export type PreferencesState = {
     /**
      * Controls whether the OpenSea API is used
      */
-    openSeaEnabled: boolean;
+    displayNftMedia: boolean;
     /**
      * Controls whether "security alerts" are enabled
      */
@@ -109,7 +109,7 @@ export type PreferencesState = {
     /**
      * Controls whether Multi rpc modal is displayed or not
      */
-    useMultiRpcMigration: boolean;
+    showMultiRpcModal: boolean;
     /**
      * Controls whether to use the safe chains list validation
      */
@@ -134,6 +134,10 @@ export type PreferencesState = {
      * User to opt in for smart account upgrade for specific accounts.
      */
     smartAccountOptInForAccounts: Hex[];
+    /**
+     * Controls token filtering controls
+     */
+    tokenNetworkFilter: Record<string, string>;
 };
 declare const name = "PreferencesController";
 export type PreferencesControllerGetStateAction = ControllerGetStateAction<typeof name, PreferencesState>;
@@ -215,11 +219,11 @@ export declare class PreferencesController extends BaseController<typeof name, P
      */
     setUseNftDetection(useNftDetection: boolean): void;
     /**
-     * Toggle the opensea enabled setting.
+     * Toggle the display nft media enabled setting.
      *
-     * @param openSeaEnabled - Boolean indicating user preference on using OpenSea's API.
+     * @param displayNftMedia - Boolean indicating user preference on using web2 third parties.
      */
-    setOpenSeaEnabled(openSeaEnabled: boolean): void;
+    setDisplayNftMedia(displayNftMedia: boolean): void;
     /**
      * Toggle the security alert enabled setting.
      *
@@ -254,9 +258,9 @@ export declare class PreferencesController extends BaseController<typeof name, P
     /**
      * Toggle multi rpc migration modal.
      *
-     * @param useMultiRpcMigration - Boolean indicating if the multi rpc modal will be displayed or not.
+     * @param showMultiRpcModal - Boolean indicating if the multi rpc modal will be displayed or not.
      */
-    setUseMultiRpcMigration(useMultiRpcMigration: boolean): void;
+    setShowMultiRpcModal(showMultiRpcModal: boolean): void;
     /**
      * A setter for the user to opt into smart transactions
      *
@@ -306,6 +310,12 @@ export declare class PreferencesController extends BaseController<typeof name, P
      * @param accounts - accounts for which user wants to optin for smart account upgrade
      */
     setSmartAccountOptInForAccounts(accounts?: Hex[]): void;
+    /**
+     * Set the token sort configuration setting.
+     *
+     * @param tokenNetworkFilter - Object describing token sort configuration.
+     */
+    setTokenNetworkFilter(tokenNetworkFilter: Record<string, boolean>): void;
 }
 export default PreferencesController;
 //# sourceMappingURL=PreferencesController.d.cts.map
\ No newline at end of file
