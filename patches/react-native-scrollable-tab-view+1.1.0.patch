diff --git a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
index 81a8a70..812c3e6 100644
--- a/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
+++ b/node_modules/react-native-scrollable-tab-view/DefaultTabBar.js
@@ -23,6 +23,7 @@ const DefaultTabBar = createReactClass({
     tabStyle: DeprecatedPropTypes.ViewPropTypes.style,
     renderTab: PropTypes.func,
     underlineStyle: DeprecatedPropTypes.ViewPropTypes.style,
+    tabPadding: PropTypes.number,
   },
 
   getDefaultProps() {
@@ -60,9 +61,11 @@ const DefaultTabBar = createReactClass({
   render() {
     const containerWidth = this.props.containerWidth;
     const numberOfTabs = this.props.tabs.length;
+    const tabPadding = this.props.tabPadding || 0;
+    const effectiveWidth = containerWidth - tabPadding;
     const tabUnderlineStyle = {
       position: 'absolute',
-      width: containerWidth / numberOfTabs,
+      width: effectiveWidth / numberOfTabs,
       height: 4,
       backgroundColor: 'navy',
       bottom: 0,
@@ -70,10 +73,10 @@ const DefaultTabBar = createReactClass({
 
     const translateX = this.props.scrollValue.interpolate({
       inputRange: [0, 1],
-      outputRange: [0,  containerWidth / numberOfTabs],
+      outputRange: [tabPadding / 2, (effectiveWidth / numberOfTabs) + (tabPadding / 2)],
     });
     return (
-      <View style={[styles.tabs, {backgroundColor: this.props.backgroundColor, }, this.props.style, ]}>
+      <View style={[styles.tabs, {backgroundColor: this.props.backgroundColor, paddingHorizontal: tabPadding / 2}, this.props.style, ]}>
         {this.props.tabs.map((name, page) => {
           const isTabActive = this.props.activeTab === page;
           const renderTab = this.props.renderTab || this.renderTab;
@@ -87,7 +90,7 @@ const DefaultTabBar = createReactClass({
                 { translateX },
               ]
             },
-            this.props.tabBarUnderlineStyle,
+            this.props.underlineStyle,
           ]}
         />
       </View>
diff --git a/node_modules/react-native-scrollable-tab-view/index.js b/node_modules/react-native-scrollable-tab-view/index.js
index dc9d4f4..4965309 100644
--- a/node_modules/react-native-scrollable-tab-view/index.js
+++ b/node_modules/react-native-scrollable-tab-view/index.js
@@ -13,17 +13,14 @@ const {
   InteractionManager,
 } = ReactNative;
 
-const ViewPagerAndroid = require('@react-native-community/viewpager');
 const TimerMixin = require('react-timer-mixin');
-const ViewPager = require('@react-native-community/viewpager');
+import PagerView from 'react-native-pager-view';
 
 const SceneComponent = require('./SceneComponent');
 const DefaultTabBar = require('./DefaultTabBar');
 const ScrollableTabBar = require('./ScrollableTabBar');
 
-const AnimatedViewPagerAndroid = Platform.OS === 'android' ?
-  Animated.createAnimatedComponent(ViewPager) :
-  undefined;
+const AnimatedViewPagerAndroid = Platform.OS === 'android' ? Animated.createAnimatedComponent(PagerView) : undefined;
 
 const ScrollableTabView = createReactClass({
   mixins: [TimerMixin, ],
@@ -235,7 +232,7 @@ const ScrollableTabView = createReactClass({
         ref={(scrollView) => { this.scrollView = scrollView; }}
         onScroll={Animated.event(
           [{ nativeEvent: { contentOffset: { x: this.state.scrollXIOS, }, }, }, ],
-          { useNativeDriver: true, listener: this._onScroll, }
+          { listener: this._onScroll, }
         )}
         onMomentumScrollBegin={this._onMomentumScrollBeginAndEnd}
         onMomentumScrollEnd={this._onMomentumScrollBeginAndEnd}
@@ -266,10 +263,7 @@ const ScrollableTabView = createReactClass({
               offset: this.state.offsetAndroid,
             },
           }, ],
-          {
-            useNativeDriver: true,
-            listener: this._onScroll,
-          },
+          { listener: this._onScroll, }
         )}
         ref={(scrollView) => { this.scrollView = scrollView; }}
         {...this.props.contentProps}
