<resources>

	<!-- Base application theme. -->
	<style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
	 <item name="android:windowDisablePreview">true</item>
		<!-- Customize your theme here. -->
        <item name="android:windowBackground">@drawable/app_background</item>
		<item name="android:statusBarColor">@color/theme</item>
		<item name="android:navigationBarColor">@color/navBar</item>
		<item name="android:windowLightStatusBar">@bool/useLightStatusBar</item>
		<item name="android:textColor">@color/themeInverse</item>
	</style>

	<style name="SplashTheme" parent="Theme.AppCompat.Light">
	 	<item name="android:windowDisablePreview">true</item>
        <item name="android:windowBackground">@drawable/app_background</item>
		<item name="android:statusBarColor">@color/theme</item>
		<item name="android:navigationBarColor">@color/navBar</item>
		<item name="android:windowLightStatusBar">@bool/useLightStatusBar</item>
		<item name="android:textColor">@color/themeInverse</item>
	</style>

	<!-- Custom Alert Dialog Theme -->
	<style name="AlertDialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert">
		<item name="android:background">@color/alertBackground</item>
		<item name="android:textColor">@color/alertTextColor</item>
		<item name="android:textColorPrimary">@color/alertTextColor</item>
		<item name="android:buttonBarButtonStyle">@style/AlertButtonStyle</item>
	</style>

    <style name="AlertButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/alertTextColor</item>
    </style>
	
</resources>
