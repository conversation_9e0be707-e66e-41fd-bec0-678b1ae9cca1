<manifest xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
>

	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.RECORD_AUDIO" />
	<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

  <!-- Android < 12 -->
  <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />

  <!-- Bluetooth permissions: Android API >= 31 (Android 12)-->
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

	<uses-sdk tools:overrideLibrary="com.tectiv3.aes" />

	<application
		android:name=".MainApplication"
		android:label="@string/app_name"
		android:icon="@mipmap/ic_launcher"
		android:roundIcon="@mipmap/ic_launcher_round"
		android:allowBackup="false"
		android:theme="@style/AppTheme"
		android:hardwareAccelerated="true"
		android:usesCleartextTraffic="${isDebug}"
		tools:targetApi="33"
		tools:ignore="GoogleAppIndexingWarning"
		android:networkSecurityConfig="@xml/react_native_config"
		android:largeHeap="true"
     	android:exported="false"
     	>
		<activity
			android:launchMode="singleInstance"
			android:name=".SplashActivity"
			android:theme="@style/SplashTheme"
			android:label="@string/app_name"
			android:exported="false"
		>
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
			</intent-filter>
		</activity>
		<activity
			android:launchMode="singleTask"
			android:name=".MainActivity"
			android:label="@string/app_name"
			android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
			android:windowSoftInputMode="adjustResize"
			android:screenOrientation="portrait"
			android:exported="true"
		>
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
			</intent-filter>
			<!-- Branch App Links -->
			<!-- autoVerify links should be at the top of the intent-filter list -->
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="metamask.app.link" />
			</intent-filter>
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="metamask-alternate.app.link" />
			</intent-filter>
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="metamask.test-app.link" />
			</intent-filter>
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="metamask-alternate.test-app.link" />
			</intent-filter>
			<!-- MM App Links -->
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="link.metamask.io" />
			</intent-filter>
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="https" />
				<data android:host="link-test.metamask.io" />
			</intent-filter>
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data android:scheme="expo-neonix" />
			</intent-filter>
			<!-- Branch URI Scheme -->
			<intent-filter>
				<data android:scheme="neonix" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="metamask" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="ethereum" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="dapp" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="wc" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="http" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
			<intent-filter>
				<data android:scheme="https" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
		<intent-filter android:autoVerify="true">
				<data android:scheme="expo-neonix" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
		<intent-filter android:autoVerify="true">
				<data android:scheme="expo-metamask" />
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
			</intent-filter>
		</activity>
		<!-- Explicitly opt-in to safe browsing -->
		<meta-data android:name="android.webkit.WebView.EnableSafeBrowsing" android:value="true" />
		<!-- Branch keys -->
		<meta-data android:name="io.branch.sdk.BranchKey" android:value="${MM_BRANCH_KEY_LIVE}" />
		<meta-data android:name="io.branch.sdk.BranchKey.test" android:value="${MM_BRANCH_KEY_TEST}" />
		<!-- PUSH NOTIFICATIONS -->
		<meta-data	android:name="com.dieam.reactnativepushnotification.notification_channel_name"
		android:value="io.neonix"/>
		<meta-data	android:name="com.dieam.reactnativepushnotification.notification_channel_description"
		android:value="io.neonix"/>
		<meta-data	android:name="com.dieam.reactnativepushnotification.notification_color"
		android:resource="@color/notificationColor"/>
		<meta-data  android:name="com.google.firebase.messaging.default_notification_color"
		android:resource="@color/notificationColor"
		tools:replace="android:resource"/>
		<meta-data  android:name="com.google.firebase.messaging.default_notification_icon"
		android:resource="@mipmap/ic_notification_small"/>

		<provider
			android:name="androidx.core.content.FileProvider"
			android:authorities="${applicationId}.provider"
			android:grantUriPermissions="true"
			android:exported="false"
		>
			<meta-data
				android:name="android.support.FILE_PROVIDER_PATHS"
				android:resource="@xml/filepaths"
			/>
		</provider>
    <service
      android:name="io.metamask.nativesdk.MessageService"
      android:enabled="true"
      android:exported="true">

    </service>
	</application>
</manifest>
