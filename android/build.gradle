// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

	ext {
		buildToolsVersion = "35.0.0"
		minSdkVersion = project.hasProperty('minSdkVersion') ? project.getProperty('minSdkVersion') : 24
		compileSdkVersion = 35
		targetSdkVersion = 34
		ndkVersion = "26.1.10909125"
		bitriseNdkPath = "/usr/local/share/android-sdk/ndk-bundle"
		kotlin_version = "1.9.25"
		kotlinVersion = "$kotlin_version"
		supportLibVersion = "28.0.0"
	}
	repositories {
		google()
		mavenCentral()
	}

	dependencies {
		classpath("com.android.tools.build:gradle")
    	classpath("com.facebook.react:react-native-gradle-plugin")
		classpath("io.sentry:sentry-android-gradle-plugin:4.2.0")
    	classpath("com.google.gms:google-services:4.4.2")
	    classpath('org.jetbrains.kotlin:kotlin-gradle-plugin')
	}
	allprojects {
    repositories {
        maven {
          url("$rootDir/../node_modules/detox/Detox-android")
        }
		        // Notifee repository
        maven {
            url(new File(['node', '--print', "require.resolve('@notifee/react-native/package.json')"].execute(null, rootDir).text.trim(), '../android/libs'))
        }
        maven { url "https://jitpack.io" }
    }
	}
}
