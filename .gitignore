# osx
.DS_Store
# don't save asdf tools-version config as nvm is prioritized.
.tool-versions

# xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
ios/debug.xcconfig
ios/release.xcconfig
.xcode.env.local

# android / intellij
build/
app/bin
.idea
.gradle
local.properties
*.iml
*.hprof
android/.project
android/app/.project
android/app/bin/
android/app/gradle*
android/app/_build*
.cxx/

# if we ever want to add google services
android/app/google-services.json
ios/GoogleService-Info.plist

# node.js
node_modules/
npm-debug.log
yarn-error.log
.yalc
yalc.lock

# buck
buck-out/
\.buckd/
*.keystore
!debug.keystore

# fastlane
*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# bundle artifact
*.jsbundle


# app-specific
/scripts/inpage-bridge/dist
/android/app/src/main/assets/InpageBridgeWeb3.js
/app/core/InpageBridgeWeb3.js

# environment variables instances
# only version the example files
.*.env

# Sentry
/sentry.debug.properties
/sentry.release.properties

# CocoaPods
/ios/Pods/

# Language files to add
/locales/languagesToUpdate/*.json

# attributions
licenseInfos.json

# wdio
wdio/reports/
# Allows access to preview versions of @metamask/* packages for testing
.npmrc

# browserstack autogenerated files
local.log
browserstack.err
.priv.wallet.js

# Detox
# Artifacts generated by failing e2e tests
/artifacts

# E2E Reports generated by tests
e2e/reports
# Unit Coverage Reports generated by tests
tests/coverage

# Performance test results
e2e/specs/performance/reports/*-performance-results.json

# appwright
appwright/report
playwright-report/*
appwright/test-reports/
test-results/
test-reports/
appwright/reporters/reports/*
*.apk

# anvil binaries
.metamask/*
# ppom
ppom/node_modules
ppom/dist
ppom/package-lock.json
app/lib/ppom/ppom.html.js

# ccache
ccache

# open-rpc/test-coverage
html-report/

# terms of use
app/util/termsOfUse/termsOfUseContent.ts
docs/assets/termsOfUse.html

# build metadata
android/app/src/main/assets/modules.json

# Google firebase base64 derived configs
**/GoogleService-Info.plist
# Expo
.expo
dist/
web-build/
expo-env.d.ts

# CICD
github-tools/


# Claude AI development files
CLAUDE.md
/CLAUDE.md
temp/
/temp/

# Reassure performance testing
.reassure/
