"project_id_env": CROWDIN_PROJECT_ID
"api_token_env": CROWDIN_PERSONAL_TOKEN
"base_path" : "."

"preserve_hierarchy": true

# This array identifies the files that should be the source for translations. If a translation key is required to be used
# the uploading of these translation paths is disabled in the .github/workflows/crowdin_action.yml file on line 27.
files: [
 {
  "source" : "locales/languages/en.json",
  "translation" : "/locales/languages/%two_letters_code%.json",
 }
]
