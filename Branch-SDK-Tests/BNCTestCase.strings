/**
 @file          BNCTestCase.strings
 @package       Branch-SDK-Tests
 @brief         String resources for Branch-SDK-Tests.

 <AUTHOR>
 @date          October 2016
 @copyright     Copyright © 2016 Branch. All rights reserved.
*/

"BNCTestCaseString" = "Test success!";

"DumpClassTest" =
"
Class 0x11a380018 is class 'DumpClass' of class 'NSObject':
	Ivar 'stringVar' type 'NSString'.
	Ivar 'intVar' type 'int'.
	Ivar 'charPtrVar' type 'char*'.
	Ivar 'classVar' type 'class'.
	Ivar 'floatVar' type 'float'.
	Ivar 'doubleVar' type 'double'.
	Ivar 'shortVar' type 'short'.
	Ivar 'boolVar' type 'BOOL'.
	Ivar 'ucharVar' type 'unsigned char'.
	Ivar 'uintVar' type 'unsigned int'.
	Ivar 'ushortVar' type 'unsigned short'.
	Ivar 'ulongVar' type 'unsigned long'.
	Ivar 'doubleTroubleVar' type 'long double'.
	Ivar 'UnhandledType' type '{UnhandledStruct=\"int1\"i\"int2\"i}' (un-handled type).
	Ivar '_intProp' type 'int'.
	Ivar '_stringProp' type 'NSString'.
	Property name: 'intProp'.
	Property name: 'stringProp'.
	Class method name: 'classMethod'.
	Method name: '.cxx_destruct'.
	Method name: 'dealloc'.
	Method name: 'init'.
	Method name: 'setIntProp:'.
	Method name: 'setStringProp:'.
	Method name: 'methodThatTakesAnNSString:'.
	Method name: 'intProp'.
	Method name: 'stringProp'.
";

"DumpInstanceTest" =
"
Instance 0x132585f70 is of class 'DumpClass' of class 'NSObject':
	Ivar 'stringVar' type '__NSCFConstantString' value 'Yope!'.
	Ivar 'intVar' type 'int' value '1'.
	Ivar 'charPtrVar' type 'char*' value 'YopeCharString'.
	Ivar 'classVar' type 'class' value 'NSNumber'.
	Ivar 'floatVar' type 'float' value '2.000000'.
	Ivar 'doubleVar' type 'double' value '3.000000'.
	Ivar 'shortVar' type 'short' value '4'.
	Ivar 'boolVar' type 'BOOL' value 'NO'.
	Ivar 'ucharVar' type 'unsigned char' value ''.
	Ivar 'uintVar' type 'unsigned int' value '0'.
	Ivar 'ushortVar' type 'unsigned short' value '0'.
	Ivar 'ulongVar' type 'unsigned long' value '0'.
	Ivar 'doubleTroubleVar' type 'long double' value '0.000000'.
	Ivar 'UnhandledType' type '{UnhandledStruct=\"int1\"i\"int2\"i}' (un-handled type).
	Ivar '_intProp' type 'int' value '5'.
	Ivar '_stringProp' type '__NSCFConstantString' value 'Props!'.
	Property name: 'intProp'.
	Property name: 'stringProp'.
	Class method name: 'classMethod'.
	Method name: '.cxx_destruct'.
	Method name: 'dealloc'.
	Method name: 'init'.
	Method name: 'setIntProp:'.
	Method name: 'setStringProp:'.
	Method name: 'methodThatTakesAnNSString:'.
	Method name: 'intProp'.
	Method name: 'stringProp'.
";

"BranchUniversalObjectJSON" =
"
{
    \"$content_schema\": \"COMMERCE_PRODUCT\",
    \"$quantity\": 2,
    \"$price\": 23.2,
    \"$currency\": \"USD\",
    \"$sku\": \"1994320302\",
    \"$product_name\": \"my_product_name1\",
    \"$product_brand\": \"my_prod_Brand1\",
    \"$product_category\": \"Baby & Toddler\",
    \"$product_variant\": \"3T\",
    \"$rating_average\": 5,
    \"$rating_count\": 5,
    \"$rating_max\": 7,
    \"$rating\": 6,
    \"$condition\": \"FAIR\",
    \"$address_street\": \"Street_name1\",
    \"$address_city\": \"city1\",
    \"$address_region\": \"Region1\",
    \"$address_country\": \"Country1\",
    \"$address_postal_code\": \"postal_code\",
    \"$latitude\": 12.07,
    \"$longitude\": -97.5,
    \"$image_captions\": [\"my_img_caption1\", \"my_img_caption_2\"],
    \"$og_title\": \"My Content Title\",
    \"$canonical_identifier\": \"item\/12345\",
    \"$canonical_url\": \"https:\/\/branch.io\/deepviews\",
    \"$keywords\": [\"My_Keyword1\", \"My_Keyword2\"],
    \"$og_description\": \"my_product_description1\",
    \"$og_image_url\": \"https:\/\/test_img_url\",
    \"$exp_date\": 212123232544,
    \"$publicly_indexable\": false,
    \"$locally_indexable\": true,
    \"$creation_timestamp\": 1501869445321,
    \"Custom_Content_metadata_key1\": \"Custom_Content_metadata_val1\",
    \"Custom_Content_metadata_key2\": \"Custom_Content_metadata_val2\"
}
";
"V2EventProperties" =
"
{
    \"affiliation\": \"test_affiliation\",
    \"coupon\": \"test_coupon\",
    \"currency\": \"USD\",
    \"description\": \"Event _description\",
    \"shipping\": 10.2,
    \"tax\": 12.3,
    \"revenue\": 1.5,
    \"search_query\": \"Query\",
    \"transaction_id\": \"12344555\",
    \"custom_data\": {
        \"Custom_Event_Property_Key1\": \"Custom_Event_Property_val1\",
        \"Custom_Event_Property_Key2\": \"Custom_Event_Property_val2\"
    }
}
";
"V2EventJSON" =
"
{
    \"name\": \"PURCHASE\",
    \"custom_data\": {
        \"Custom_Event_Property_Key1\": \"Custom_Event_Property_val1\",
        \"Custom_Event_Property_Key2\": \"Custom_Event_Property_val2\"
    },
    \"customer_event_alias\": \"event alias\",
    \"event_data\": {
        \"affiliation\": \"test_affiliation\",
        \"coupon\": \"test_coupon\",
        \"currency\": \"USD\",
        \"description\": \"Event _description\",
        \"shipping\": 10.2,
        \"tax\": 12.3,
        \"revenue\": 1.5,
        \"transaction_id\": \"12344555\",
        \"search_query\": \"Query\"
    },
    \"content_items\": [{
        \"$content_schema\": \"COMMERCE_PRODUCT\",
        \"$quantity\": 2,
        \"$price\": 23.2,
        \"$currency\": \"USD\",
        \"$condition\": \"FAIR\",
        \"$sku\": \"1994320302\",
        \"$product_name\": \"my_product_name1\",
        \"$product_brand\": \"my_prod_Brand1\",
        \"$product_category\": \"Baby & Toddler\",
        \"$product_variant\": \"3T\",
        \"$rating_average\": 5,
        \"$rating_count\": 5,
        \"$rating_max\": 7,
        \"$rating\": 6,
        \"$address_street\": \"Street_name1\",
        \"$address_city\": \"city1\",
        \"$address_region\": \"Region1\",
        \"$address_country\": \"Country1\",
        \"$address_postal_code\": \"postal_code\",
        \"$latitude\": 12.07,
        \"$longitude\": -97.5,
        \"$image_captions\": [\"my_img_caption1\", \"my_img_caption_2\"],
        \"Custom_Content_metadata_key1\": \"Custom_Content_metadata_val1\",
        \"Custom_Content_metadata_key2\": \"Custom_Content_metadata_val2\",
        \"$og_title\": \"My Content Title\",
        \"$canonical_identifier\": \"item\/12345\",
        \"$canonical_url\": \"https:\/\/branch.io\/deepviews\",
        \"$keywords\": [\"My_Keyword1\", \"My_Keyword2\"],
        \"$og_description\": \"my_product_description1\",
        \"$og_image_url\": \"https:\/\/test_img_url\",
        \"$exp_date\": 212123232544,
        \"$locally_indexable\": true,
        \"$creation_timestamp\": 1501869445321
    }],
    \"user_data\": {
        \"os\": \"iOS\",
        \"os_version\": 25,
        \"environment\": \"FULL_APP\",
        \"idfa\": \"\",
        \"idfv\": \"\",
        \"user_agent\": \"\",
        \"developer_identity\": \"edsojan\",
        \"country\": \"US\",
        \"language\": \"en\",
        \"brand\": \"Apple\",
        \"device_fingerprint_id\": \"\",
        \"sdk\": \"ios0.17.10\",
        \"app_version\": \"whatever\",
        \"model\": \"x86_64\",
        \"screen_dpi\": 3,
        \"screen_height\": 2208,
        \"screen_width\": 1242
    },
    \"branch_key\": \"key_live_foo\",
    \"retryNumber\": 0
}
";
"BUODescription" =
"<BranchUniversalObject 0x****************
 canonicalIdentifier: item/12345
 title: My Content Title
 contentDescription: my_product_description1
 imageUrl: https://test_img_url
 metadata: {
    \"Custom_Content_metadata_key1\" = \"Custom_Content_metadata_val1\";
    \"Custom_Content_metadata_key2\" = \"Custom_Content_metadata_val2\";
}
 type: COMMERCE_PRODUCT
 locallyIndex: 1
 publiclyIndex: 0
 keywords: (
    \"My_Keyword1\",
    \"My_Keyword2\"
)
 expirationDate: 1976-09-21 03:07:12 +0000
>";
"BNCDeviceDictionaryV2" =
"{
    \"os\": \"iOS\",
    \"os_version\": 25,
    \"environment\": \"FULL_APP\",
    \"idfa\": \"\",
    \"idfv\": \"\",
    \"user_agent\": \"\",
    \"developer_identity\": \"edsojan\",
    \"country\": \"US\",
    \"language\": \"en\",
    \"brand\": \"Apple\",
    \"device_fingerprint_id\": \"\",
    \"sdk\": \"ios\",
    \"sdk_version\": \"0.17.10\",
    \"app_version\": \"whatever\",
    \"model\": \"x86_64\",
    \"screen_dpi\": 3,
    \"screen_height\": 2208,
    \"screen_width\": 1242
}";

