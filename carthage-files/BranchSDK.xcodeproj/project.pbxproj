// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		2BD7C3DD1F27CEDB003696AF /* BNCDebug.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BD7C3D71F27CEDB003696AF /* BNCDebug.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2BD7C3DE1F27CEDB003696AF /* BNCDebug.m in Sources */ = {isa = PBXBuildFile; fileRef = 2BD7C3D81F27CEDB003696AF /* BNCDebug.m */; };
		2BD7C3E01F27CEDB003696AF /* BNCLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 2BD7C3DA1F27CEDB003696AF /* BNCLog.m */; };
		2BD7C3E51F27CF68003696AF /* NSString+Branch.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BD7C3E31F27CF68003696AF /* NSString+Branch.h */; };
		2BD7C3E61F27CF68003696AF /* NSString+Branch.m in Sources */ = {isa = PBXBuildFile; fileRef = 2BD7C3E41F27CF68003696AF /* NSString+Branch.m */; };
		4D1851C72019127700E48994 /* BNCApplication.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1851C32019127600E48994 /* BNCApplication.h */; };
		4D1851C82019127700E48994 /* BNCApplication.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1851C42019127700E48994 /* BNCApplication.m */; };
		4D1851C92019127700E48994 /* BNCKeyChain.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1851C52019127700E48994 /* BNCKeyChain.h */; };
		4D1851CA2019127700E48994 /* BNCKeyChain.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1851C62019127700E48994 /* BNCKeyChain.m */; };
		4D1ED27D1FB3A43A007390A8 /* BNCAvailability.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1ED27B1FB3A439007390A8 /* BNCAvailability.m */; };
		4D1ED27E1FB3A43A007390A8 /* BNCAvailability.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1ED27C1FB3A439007390A8 /* BNCAvailability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D1ED2811FB3A472007390A8 /* BNCSpotlightService.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1ED27F1FB3A471007390A8 /* BNCSpotlightService.h */; };
		4D1ED2821FB3A472007390A8 /* BNCSpotlightService.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1ED2801FB3A471007390A8 /* BNCSpotlightService.m */; };
		4D291AAB1E322ECC007ED118 /* NSMutableDictionary+Branch.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D291AA91E322ECC007ED118 /* NSMutableDictionary+Branch.h */; };
		4D291AAC1E322ECC007ED118 /* NSMutableDictionary+Branch.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D291AAA1E322ECC007ED118 /* NSMutableDictionary+Branch.m */; };
		4D301CE31FA1452100642F4A /* BNCFieldDefines.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DB328021FA10C6200ACF9B0 /* BNCFieldDefines.h */; };
		4D3636042040A504006A1FDE /* BNCURLBlackList.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D3636022040A504006A1FDE /* BNCURLBlackList.h */; };
		4D3636052040A504006A1FDE /* BNCURLBlackList.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D3636032040A504006A1FDE /* BNCURLBlackList.m */; };
		4D3922BD1E1F0C85004FB7C8 /* BNCCommerceEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D3922BC1E1F0C85004FB7C8 /* BNCCommerceEvent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D3FA94D1DFF3F6C00E2B6A9 /* BNCConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D3FA94C1DFF3F6C00E2B6A9 /* BNCConfig.m */; };
		4D510D721F0D6014003D720B /* BNCDeepLinkViewControllerInstance.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D510D701F0D6014003D720B /* BNCDeepLinkViewControllerInstance.h */; };
		4D510D731F0D6014003D720B /* BNCDeepLinkViewControllerInstance.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D510D711F0D6014003D720B /* BNCDeepLinkViewControllerInstance.m */; };
		4D6A27571F2954DE00027264 /* BNCLocalization.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D6A27551F2954DE00027264 /* BNCLocalization.h */; };
		4D6A27581F2954DE00027264 /* BNCLocalization.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D6A27561F2954DE00027264 /* BNCLocalization.m */; };
		4D778E1E218253F200308B51 /* BranchCSSearchableItemAttributeSet.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1351D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D78C9651F2679F000EEDD5F /* BNCCrashlyticsWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D78C9631F2679F000EEDD5F /* BNCCrashlyticsWrapper.h */; };
		4D78C9661F2679F000EEDD5F /* BNCCrashlyticsWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D78C9641F2679F000EEDD5F /* BNCCrashlyticsWrapper.m */; };
		4D9607F91FBF9634008AB3C2 /* UIViewController+Branch.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D9607F71FBF9633008AB3C2 /* UIViewController+Branch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D9607FA1FBF9634008AB3C2 /* UIViewController+Branch.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D9607F81FBF9633008AB3C2 /* UIViewController+Branch.m */; };
		4DA40A8F1E1F0011004D2534 /* BNCCommerceEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DA40A8D1E1F0011004D2534 /* BNCCommerceEvent.m */; };
		4DAB179D1EE8A31B0079EEB4 /* BNCNetworkService.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17711EE8A31B0079EEB4 /* BNCNetworkService.h */; };
		4DAB179E1EE8A31B0079EEB4 /* BNCNetworkService.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17721EE8A31B0079EEB4 /* BNCNetworkService.m */; };
		4DAB179F1EE8A31B0079EEB4 /* BNCNetworkServiceProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17731EE8A31B0079EEB4 /* BNCNetworkServiceProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAB17A01EE8A31B0079EEB4 /* BNCServerInterface.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17741EE8A31B0079EEB4 /* BNCServerInterface.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAB17A11EE8A31B0079EEB4 /* BNCServerInterface.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17751EE8A31B0079EEB4 /* BNCServerInterface.m */; };
		4DAB17A21EE8A31B0079EEB4 /* BNCServerRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17761EE8A31B0079EEB4 /* BNCServerRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAB17A31EE8A31B0079EEB4 /* BNCServerRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17771EE8A31B0079EEB4 /* BNCServerRequest.m */; };
		4DAB17A41EE8A31B0079EEB4 /* BNCServerRequestQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17781EE8A31B0079EEB4 /* BNCServerRequestQueue.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAB17A51EE8A31B0079EEB4 /* BNCServerRequestQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17791EE8A31B0079EEB4 /* BNCServerRequestQueue.m */; };
		4DAB17A61EE8A31B0079EEB4 /* BNCServerResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB177A1EE8A31B0079EEB4 /* BNCServerResponse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAB17A71EE8A31B0079EEB4 /* BNCServerResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB177B1EE8A31B0079EEB4 /* BNCServerResponse.m */; };
		4DAB17A81EE8A31B0079EEB4 /* BranchCloseRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB177D1EE8A31B0079EEB4 /* BranchCloseRequest.h */; };
		4DAB17A91EE8A31B0079EEB4 /* BranchCloseRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB177E1EE8A31B0079EEB4 /* BranchCloseRequest.m */; };
		4DAB17AA1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB177F1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.h */; };
		4DAB17AB1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17801EE8A31B0079EEB4 /* BranchCreditHistoryRequest.m */; };
		4DAB17AC1EE8A31B0079EEB4 /* BranchInstallRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17811EE8A31B0079EEB4 /* BranchInstallRequest.h */; };
		4DAB17AD1EE8A31B0079EEB4 /* BranchInstallRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17821EE8A31B0079EEB4 /* BranchInstallRequest.m */; };
		4DAB17AE1EE8A31B0079EEB4 /* BranchLoadRewardsRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17831EE8A31B0079EEB4 /* BranchLoadRewardsRequest.h */; };
		4DAB17AF1EE8A31B0079EEB4 /* BranchLoadRewardsRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17841EE8A31B0079EEB4 /* BranchLoadRewardsRequest.m */; };
		4DAB17B01EE8A31B0079EEB4 /* BranchLogoutRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17851EE8A31B0079EEB4 /* BranchLogoutRequest.h */; };
		4DAB17B11EE8A31B0079EEB4 /* BranchLogoutRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17861EE8A31B0079EEB4 /* BranchLogoutRequest.m */; };
		4DAB17B21EE8A31B0079EEB4 /* BranchOpenRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17871EE8A31B0079EEB4 /* BranchOpenRequest.h */; };
		4DAB17B31EE8A31B0079EEB4 /* BranchOpenRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17881EE8A31B0079EEB4 /* BranchOpenRequest.m */; };
		4DAB17B41EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17891EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.h */; };
		4DAB17B51EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB178A1EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.m */; };
		4DAB17B61EE8A31B0079EEB4 /* BranchRegisterViewRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB178B1EE8A31B0079EEB4 /* BranchRegisterViewRequest.h */; };
		4DAB17B71EE8A31B0079EEB4 /* BranchRegisterViewRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB178C1EE8A31B0079EEB4 /* BranchRegisterViewRequest.m */; };
		4DAB17B81EE8A31B0079EEB4 /* BranchSetIdentityRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB178D1EE8A31B0079EEB4 /* BranchSetIdentityRequest.h */; };
		4DAB17B91EE8A31B0079EEB4 /* BranchSetIdentityRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB178E1EE8A31B0079EEB4 /* BranchSetIdentityRequest.m */; };
		4DAB17BA1EE8A31B0079EEB4 /* BranchShortUrlRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB178F1EE8A31B0079EEB4 /* BranchShortUrlRequest.h */; };
		4DAB17BB1EE8A31B0079EEB4 /* BranchShortUrlRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17901EE8A31B0079EEB4 /* BranchShortUrlRequest.m */; };
		4DAB17BC1EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17911EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.h */; };
		4DAB17BD1EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17921EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.m */; };
		4DAB17BE1EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAB17931EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.h */; };
		4DAB17BF1EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17941EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.m */; };
		4DAB17C11EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAB17961EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.m */; };
		4DAF63BA1F86C26A006316E9 /* BranchDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DAF63B81F86C269006316E9 /* BranchDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DAF63BB1F86C26A006316E9 /* BranchDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF63B91F86C26A006316E9 /* BranchDelegate.m */; };
		4DB328061FA10C6300ACF9B0 /* BranchEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DB328031FA10C6200ACF9B0 /* BranchEvent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DB328071FA10C6300ACF9B0 /* BranchEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DB328041FA10C6300ACF9B0 /* BranchEvent.m */; };
		4DB5673A1E79FDAF00A8A324 /* BranchShareLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DB567381E79FDAF00A8A324 /* BranchShareLink.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DB5673B1E79FDAF00A8A324 /* BranchShareLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DB567391E79FDAF00A8A324 /* BranchShareLink.m */; };
		4DCF4B001F438A2B00AF9AAB /* BNCError.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A11C1D03DB9E006181D8 /* BNCError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DCF4B011F438A2B00AF9AAB /* BNCLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BD7C3D91F27CEDB003696AF /* BNCLog.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5FB6D3D623219B48006C5094 /* BNCUserAgentCollector.m in Sources */ = {isa = PBXBuildFile; fileRef = 5FB6D3D223219B48006C5094 /* BNCUserAgentCollector.m */; };
		5FB6D3D723219B48006C5094 /* BNCUserAgentCollector.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FB6D3D323219B48006C5094 /* BNCUserAgentCollector.h */; };
		5FB6D3D823219B48006C5094 /* BNCAppleReceipt.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FB6D3D423219B48006C5094 /* BNCAppleReceipt.h */; };
		5FB6D3D923219B48006C5094 /* BNCAppleReceipt.m in Sources */ = {isa = PBXBuildFile; fileRef = 5FB6D3D523219B48006C5094 /* BNCAppleReceipt.m */; };
		5FB6D3DD23219C46006C5094 /* BNCThreads.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FB6D3DB23219C46006C5094 /* BNCThreads.h */; };
		5FB6D3DE23219C46006C5094 /* BNCThreads.m in Sources */ = {isa = PBXBuildFile; fileRef = 5FB6D3DC23219C46006C5094 /* BNCThreads.m */; };
		7DA3BF1D1D889CE500CA8AE0 /* BranchContentDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA3BF171D889CE500CA8AE0 /* BranchContentDiscoverer.m */; };
		7DA3BF1E1D889CE500CA8AE0 /* BranchContentDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA3BF181D889CE500CA8AE0 /* BranchContentDiscoverer.h */; };
		7DA3BF1F1D889CE500CA8AE0 /* BranchContentDiscoveryManifest.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA3BF191D889CE500CA8AE0 /* BranchContentDiscoveryManifest.h */; };
		7DA3BF201D889CE500CA8AE0 /* BranchContentDiscoveryManifest.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA3BF1A1D889CE500CA8AE0 /* BranchContentDiscoveryManifest.m */; };
		7DA3BF211D889CE500CA8AE0 /* BranchContentPathProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA3BF1B1D889CE500CA8AE0 /* BranchContentPathProperties.h */; };
		7DA3BF221D889CE500CA8AE0 /* BranchContentPathProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA3BF1C1D889CE500CA8AE0 /* BranchContentPathProperties.m */; };
		9A149C6720336601002135DC /* Branch+Validator.h in Headers */ = {isa = PBXBuildFile; fileRef = 9A149C6520336600002135DC /* Branch+Validator.h */; };
		9A149C6820336601002135DC /* Branch+Validator.m in Sources */ = {isa = PBXBuildFile; fileRef = 9A149C6620336600002135DC /* Branch+Validator.m */; };
		E230A16C1D03DB9E006181D8 /* BNCConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1151D03DB9E006181D8 /* BNCConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A16D1D03DB9E006181D8 /* BNCContentDiscoveryManager.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1161D03DB9E006181D8 /* BNCContentDiscoveryManager.h */; };
		E230A16E1D03DB9E006181D8 /* BNCContentDiscoveryManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1171D03DB9E006181D8 /* BNCContentDiscoveryManager.m */; };
		E230A16F1D03DB9E006181D8 /* BNCDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1181D03DB9E006181D8 /* BNCDeviceInfo.h */; };
		E230A1701D03DB9E006181D8 /* BNCDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1191D03DB9E006181D8 /* BNCDeviceInfo.m */; };
		E230A1711D03DB9E006181D8 /* BNCEncodingUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A11A1D03DB9E006181D8 /* BNCEncodingUtils.h */; };
		E230A1721D03DB9E006181D8 /* BNCEncodingUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A11B1D03DB9E006181D8 /* BNCEncodingUtils.m */; };
		E230A1741D03DB9E006181D8 /* BNCError.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A11D1D03DB9E006181D8 /* BNCError.m */; };
		E230A1751D03DB9E006181D8 /* BNCLinkCache.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A11E1D03DB9E006181D8 /* BNCLinkCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1761D03DB9E006181D8 /* BNCLinkCache.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A11F1D03DB9E006181D8 /* BNCLinkCache.m */; };
		E230A1771D03DB9E006181D8 /* BNCLinkData.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1201D03DB9E006181D8 /* BNCLinkData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1781D03DB9E006181D8 /* BNCLinkData.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1211D03DB9E006181D8 /* BNCLinkData.m */; };
		E230A1791D03DB9E006181D8 /* BNCPreferenceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1221D03DB9E006181D8 /* BNCPreferenceHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A17A1D03DB9E006181D8 /* BNCPreferenceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1231D03DB9E006181D8 /* BNCPreferenceHelper.m */; };
		E230A1811D03DB9E006181D8 /* BNCStrongMatchHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A12A1D03DB9E006181D8 /* BNCStrongMatchHelper.h */; };
		E230A1821D03DB9E006181D8 /* BNCStrongMatchHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A12B1D03DB9E006181D8 /* BNCStrongMatchHelper.m */; };
		E230A1831D03DB9E006181D8 /* BNCSystemObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A12C1D03DB9E006181D8 /* BNCSystemObserver.h */; };
		E230A1841D03DB9E006181D8 /* BNCSystemObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A12D1D03DB9E006181D8 /* BNCSystemObserver.m */; };
		E230A1861D03DB9E006181D8 /* Branch.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A12F1D03DB9E006181D8 /* Branch.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1871D03DB9E006181D8 /* Branch.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1301D03DB9E006181D8 /* Branch.m */; };
		E230A1881D03DB9E006181D8 /* BranchActivityItemProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1311D03DB9E006181D8 /* BranchActivityItemProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1891D03DB9E006181D8 /* BranchActivityItemProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1321D03DB9E006181D8 /* BranchActivityItemProvider.m */; };
		E230A18A1D03DB9E006181D8 /* BranchConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1331D03DB9E006181D8 /* BranchConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A18B1D03DB9E006181D8 /* BranchConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1341D03DB9E006181D8 /* BranchConstants.m */; };
		E230A18D1D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1361D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.m */; };
		E230A18E1D03DB9E006181D8 /* BranchDeepLinkingController.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1371D03DB9E006181D8 /* BranchDeepLinkingController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A18F1D03DB9E006181D8 /* BranchLinkProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A1381D03DB9E006181D8 /* BranchLinkProperties.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1901D03DB9E006181D8 /* BranchLinkProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A1391D03DB9E006181D8 /* BranchLinkProperties.m */; };
		E230A1921D03DB9E006181D8 /* BranchUniversalObject.h in Headers */ = {isa = PBXBuildFile; fileRef = E230A13B1D03DB9E006181D8 /* BranchUniversalObject.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E230A1931D03DB9E006181D8 /* BranchUniversalObject.m in Sources */ = {isa = PBXBuildFile; fileRef = E230A13C1D03DB9E006181D8 /* BranchUniversalObject.m */; };
		E2B9477B1D15E31700F2270D /* BNCCallbacks.h in Headers */ = {isa = PBXBuildFile; fileRef = E2B9477A1D15E31700F2270D /* BNCCallbacks.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2BD7C3D71F27CEDB003696AF /* BNCDebug.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCDebug.h; sourceTree = "<group>"; };
		2BD7C3D81F27CEDB003696AF /* BNCDebug.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCDebug.m; sourceTree = "<group>"; };
		2BD7C3D91F27CEDB003696AF /* BNCLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCLog.h; sourceTree = "<group>"; };
		2BD7C3DA1F27CEDB003696AF /* BNCLog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCLog.m; sourceTree = "<group>"; };
		2BD7C3E31F27CF68003696AF /* NSString+Branch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+Branch.h"; sourceTree = "<group>"; };
		2BD7C3E41F27CF68003696AF /* NSString+Branch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+Branch.m"; sourceTree = "<group>"; };
		4D1851C32019127600E48994 /* BNCApplication.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCApplication.h; sourceTree = "<group>"; };
		4D1851C42019127700E48994 /* BNCApplication.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCApplication.m; sourceTree = "<group>"; };
		4D1851C52019127700E48994 /* BNCKeyChain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCKeyChain.h; sourceTree = "<group>"; };
		4D1851C62019127700E48994 /* BNCKeyChain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCKeyChain.m; sourceTree = "<group>"; };
		4D1ED27B1FB3A439007390A8 /* BNCAvailability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCAvailability.m; sourceTree = "<group>"; };
		4D1ED27C1FB3A439007390A8 /* BNCAvailability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCAvailability.h; sourceTree = "<group>"; };
		4D1ED27F1FB3A471007390A8 /* BNCSpotlightService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCSpotlightService.h; sourceTree = "<group>"; };
		4D1ED2801FB3A471007390A8 /* BNCSpotlightService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCSpotlightService.m; sourceTree = "<group>"; };
		4D291AA91E322ECC007ED118 /* NSMutableDictionary+Branch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSMutableDictionary+Branch.h"; sourceTree = "<group>"; };
		4D291AAA1E322ECC007ED118 /* NSMutableDictionary+Branch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSMutableDictionary+Branch.m"; sourceTree = "<group>"; };
		4D3636022040A504006A1FDE /* BNCURLBlackList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCURLBlackList.h; sourceTree = "<group>"; };
		4D3636032040A504006A1FDE /* BNCURLBlackList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCURLBlackList.m; sourceTree = "<group>"; };
		4D3922BC1E1F0C85004FB7C8 /* BNCCommerceEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCCommerceEvent.h; sourceTree = "<group>"; };
		4D3FA94C1DFF3F6C00E2B6A9 /* BNCConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCConfig.m; sourceTree = "<group>"; };
		4D510D701F0D6014003D720B /* BNCDeepLinkViewControllerInstance.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCDeepLinkViewControllerInstance.h; sourceTree = "<group>"; };
		4D510D711F0D6014003D720B /* BNCDeepLinkViewControllerInstance.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCDeepLinkViewControllerInstance.m; sourceTree = "<group>"; };
		4D6A27551F2954DE00027264 /* BNCLocalization.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCLocalization.h; sourceTree = "<group>"; };
		4D6A27561F2954DE00027264 /* BNCLocalization.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCLocalization.m; sourceTree = "<group>"; };
		4D78C9631F2679F000EEDD5F /* BNCCrashlyticsWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCCrashlyticsWrapper.h; sourceTree = "<group>"; };
		4D78C9641F2679F000EEDD5F /* BNCCrashlyticsWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCCrashlyticsWrapper.m; sourceTree = "<group>"; };
		4D9607F71FBF9633008AB3C2 /* UIViewController+Branch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+Branch.h"; sourceTree = "<group>"; };
		4D9607F81FBF9633008AB3C2 /* UIViewController+Branch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+Branch.m"; sourceTree = "<group>"; };
		4DA40A8D1E1F0011004D2534 /* BNCCommerceEvent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCCommerceEvent.m; sourceTree = "<group>"; };
		4DAB17711EE8A31B0079EEB4 /* BNCNetworkService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCNetworkService.h; sourceTree = "<group>"; };
		4DAB17721EE8A31B0079EEB4 /* BNCNetworkService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCNetworkService.m; sourceTree = "<group>"; };
		4DAB17731EE8A31B0079EEB4 /* BNCNetworkServiceProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCNetworkServiceProtocol.h; sourceTree = "<group>"; };
		4DAB17741EE8A31B0079EEB4 /* BNCServerInterface.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCServerInterface.h; sourceTree = "<group>"; };
		4DAB17751EE8A31B0079EEB4 /* BNCServerInterface.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCServerInterface.m; sourceTree = "<group>"; };
		4DAB17761EE8A31B0079EEB4 /* BNCServerRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCServerRequest.h; sourceTree = "<group>"; };
		4DAB17771EE8A31B0079EEB4 /* BNCServerRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCServerRequest.m; sourceTree = "<group>"; };
		4DAB17781EE8A31B0079EEB4 /* BNCServerRequestQueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCServerRequestQueue.h; sourceTree = "<group>"; };
		4DAB17791EE8A31B0079EEB4 /* BNCServerRequestQueue.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCServerRequestQueue.m; sourceTree = "<group>"; };
		4DAB177A1EE8A31B0079EEB4 /* BNCServerResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCServerResponse.h; sourceTree = "<group>"; };
		4DAB177B1EE8A31B0079EEB4 /* BNCServerResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCServerResponse.m; sourceTree = "<group>"; };
		4DAB177D1EE8A31B0079EEB4 /* BranchCloseRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchCloseRequest.h; sourceTree = "<group>"; };
		4DAB177E1EE8A31B0079EEB4 /* BranchCloseRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchCloseRequest.m; sourceTree = "<group>"; };
		4DAB177F1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchCreditHistoryRequest.h; sourceTree = "<group>"; };
		4DAB17801EE8A31B0079EEB4 /* BranchCreditHistoryRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchCreditHistoryRequest.m; sourceTree = "<group>"; };
		4DAB17811EE8A31B0079EEB4 /* BranchInstallRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchInstallRequest.h; sourceTree = "<group>"; };
		4DAB17821EE8A31B0079EEB4 /* BranchInstallRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchInstallRequest.m; sourceTree = "<group>"; };
		4DAB17831EE8A31B0079EEB4 /* BranchLoadRewardsRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchLoadRewardsRequest.h; sourceTree = "<group>"; };
		4DAB17841EE8A31B0079EEB4 /* BranchLoadRewardsRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchLoadRewardsRequest.m; sourceTree = "<group>"; };
		4DAB17851EE8A31B0079EEB4 /* BranchLogoutRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchLogoutRequest.h; sourceTree = "<group>"; };
		4DAB17861EE8A31B0079EEB4 /* BranchLogoutRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchLogoutRequest.m; sourceTree = "<group>"; };
		4DAB17871EE8A31B0079EEB4 /* BranchOpenRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchOpenRequest.h; sourceTree = "<group>"; };
		4DAB17881EE8A31B0079EEB4 /* BranchOpenRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchOpenRequest.m; sourceTree = "<group>"; };
		4DAB17891EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchRedeemRewardsRequest.h; sourceTree = "<group>"; };
		4DAB178A1EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchRedeemRewardsRequest.m; sourceTree = "<group>"; };
		4DAB178B1EE8A31B0079EEB4 /* BranchRegisterViewRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchRegisterViewRequest.h; sourceTree = "<group>"; };
		4DAB178C1EE8A31B0079EEB4 /* BranchRegisterViewRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchRegisterViewRequest.m; sourceTree = "<group>"; };
		4DAB178D1EE8A31B0079EEB4 /* BranchSetIdentityRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchSetIdentityRequest.h; sourceTree = "<group>"; };
		4DAB178E1EE8A31B0079EEB4 /* BranchSetIdentityRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchSetIdentityRequest.m; sourceTree = "<group>"; };
		4DAB178F1EE8A31B0079EEB4 /* BranchShortUrlRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchShortUrlRequest.h; sourceTree = "<group>"; };
		4DAB17901EE8A31B0079EEB4 /* BranchShortUrlRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchShortUrlRequest.m; sourceTree = "<group>"; };
		4DAB17911EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchShortUrlSyncRequest.h; sourceTree = "<group>"; };
		4DAB17921EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchShortUrlSyncRequest.m; sourceTree = "<group>"; };
		4DAB17931EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchSpotlightUrlRequest.h; sourceTree = "<group>"; };
		4DAB17941EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchSpotlightUrlRequest.m; sourceTree = "<group>"; };
		4DAB17951EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchUserCompletedActionRequest.h; sourceTree = "<group>"; };
		4DAB17961EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchUserCompletedActionRequest.m; sourceTree = "<group>"; };
		4DAF63B81F86C269006316E9 /* BranchDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchDelegate.h; sourceTree = "<group>"; };
		4DAF63B91F86C26A006316E9 /* BranchDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchDelegate.m; sourceTree = "<group>"; };
		4DB328021FA10C6200ACF9B0 /* BNCFieldDefines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCFieldDefines.h; sourceTree = "<group>"; };
		4DB328031FA10C6200ACF9B0 /* BranchEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchEvent.h; sourceTree = "<group>"; };
		4DB328041FA10C6300ACF9B0 /* BranchEvent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchEvent.m; sourceTree = "<group>"; };
		4DB567381E79FDAF00A8A324 /* BranchShareLink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchShareLink.h; sourceTree = "<group>"; };
		4DB567391E79FDAF00A8A324 /* BranchShareLink.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchShareLink.m; sourceTree = "<group>"; };
		5FB6D3D223219B48006C5094 /* BNCUserAgentCollector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCUserAgentCollector.m; sourceTree = "<group>"; };
		5FB6D3D323219B48006C5094 /* BNCUserAgentCollector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCUserAgentCollector.h; sourceTree = "<group>"; };
		5FB6D3D423219B48006C5094 /* BNCAppleReceipt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCAppleReceipt.h; sourceTree = "<group>"; };
		5FB6D3D523219B48006C5094 /* BNCAppleReceipt.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCAppleReceipt.m; sourceTree = "<group>"; };
		5FB6D3DB23219C46006C5094 /* BNCThreads.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCThreads.h; sourceTree = "<group>"; };
		5FB6D3DC23219C46006C5094 /* BNCThreads.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCThreads.m; sourceTree = "<group>"; };
		7DA3BF171D889CE500CA8AE0 /* BranchContentDiscoverer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchContentDiscoverer.m; sourceTree = "<group>"; };
		7DA3BF181D889CE500CA8AE0 /* BranchContentDiscoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchContentDiscoverer.h; sourceTree = "<group>"; };
		7DA3BF191D889CE500CA8AE0 /* BranchContentDiscoveryManifest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchContentDiscoveryManifest.h; sourceTree = "<group>"; };
		7DA3BF1A1D889CE500CA8AE0 /* BranchContentDiscoveryManifest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchContentDiscoveryManifest.m; sourceTree = "<group>"; };
		7DA3BF1B1D889CE500CA8AE0 /* BranchContentPathProperties.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchContentPathProperties.h; sourceTree = "<group>"; };
		7DA3BF1C1D889CE500CA8AE0 /* BranchContentPathProperties.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchContentPathProperties.m; sourceTree = "<group>"; };
		9A149C6520336600002135DC /* Branch+Validator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Branch+Validator.h"; sourceTree = "<group>"; };
		9A149C6620336600002135DC /* Branch+Validator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "Branch+Validator.m"; sourceTree = "<group>"; };
		E230A1151D03DB9E006181D8 /* BNCConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCConfig.h; sourceTree = "<group>"; };
		E230A1161D03DB9E006181D8 /* BNCContentDiscoveryManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCContentDiscoveryManager.h; sourceTree = "<group>"; };
		E230A1171D03DB9E006181D8 /* BNCContentDiscoveryManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCContentDiscoveryManager.m; sourceTree = "<group>"; };
		E230A1181D03DB9E006181D8 /* BNCDeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCDeviceInfo.h; sourceTree = "<group>"; };
		E230A1191D03DB9E006181D8 /* BNCDeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCDeviceInfo.m; sourceTree = "<group>"; };
		E230A11A1D03DB9E006181D8 /* BNCEncodingUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCEncodingUtils.h; sourceTree = "<group>"; };
		E230A11B1D03DB9E006181D8 /* BNCEncodingUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCEncodingUtils.m; sourceTree = "<group>"; };
		E230A11C1D03DB9E006181D8 /* BNCError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCError.h; sourceTree = "<group>"; };
		E230A11D1D03DB9E006181D8 /* BNCError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCError.m; sourceTree = "<group>"; };
		E230A11E1D03DB9E006181D8 /* BNCLinkCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCLinkCache.h; sourceTree = "<group>"; };
		E230A11F1D03DB9E006181D8 /* BNCLinkCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCLinkCache.m; sourceTree = "<group>"; };
		E230A1201D03DB9E006181D8 /* BNCLinkData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCLinkData.h; sourceTree = "<group>"; };
		E230A1211D03DB9E006181D8 /* BNCLinkData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCLinkData.m; sourceTree = "<group>"; };
		E230A1221D03DB9E006181D8 /* BNCPreferenceHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCPreferenceHelper.h; sourceTree = "<group>"; };
		E230A1231D03DB9E006181D8 /* BNCPreferenceHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCPreferenceHelper.m; sourceTree = "<group>"; };
		E230A12A1D03DB9E006181D8 /* BNCStrongMatchHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCStrongMatchHelper.h; sourceTree = "<group>"; };
		E230A12B1D03DB9E006181D8 /* BNCStrongMatchHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCStrongMatchHelper.m; sourceTree = "<group>"; };
		E230A12C1D03DB9E006181D8 /* BNCSystemObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCSystemObserver.h; sourceTree = "<group>"; };
		E230A12D1D03DB9E006181D8 /* BNCSystemObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCSystemObserver.m; sourceTree = "<group>"; };
		E230A12F1D03DB9E006181D8 /* Branch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Branch.h; sourceTree = "<group>"; };
		E230A1301D03DB9E006181D8 /* Branch.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Branch.m; sourceTree = "<group>"; };
		E230A1311D03DB9E006181D8 /* BranchActivityItemProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchActivityItemProvider.h; sourceTree = "<group>"; };
		E230A1321D03DB9E006181D8 /* BranchActivityItemProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchActivityItemProvider.m; sourceTree = "<group>"; };
		E230A1331D03DB9E006181D8 /* BranchConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchConstants.h; sourceTree = "<group>"; };
		E230A1341D03DB9E006181D8 /* BranchConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchConstants.m; sourceTree = "<group>"; };
		E230A1351D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchCSSearchableItemAttributeSet.h; sourceTree = "<group>"; };
		E230A1361D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchCSSearchableItemAttributeSet.m; sourceTree = "<group>"; };
		E230A1371D03DB9E006181D8 /* BranchDeepLinkingController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchDeepLinkingController.h; sourceTree = "<group>"; };
		E230A1381D03DB9E006181D8 /* BranchLinkProperties.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchLinkProperties.h; sourceTree = "<group>"; };
		E230A1391D03DB9E006181D8 /* BranchLinkProperties.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchLinkProperties.m; sourceTree = "<group>"; };
		E230A13B1D03DB9E006181D8 /* BranchUniversalObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BranchUniversalObject.h; sourceTree = "<group>"; };
		E230A13C1D03DB9E006181D8 /* BranchUniversalObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BranchUniversalObject.m; sourceTree = "<group>"; };
		E298D0521C73D1B800589D22 /* Branch.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Branch.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E298D0571C73D1B800589D22 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E2B9477A1D15E31700F2270D /* BNCCallbacks.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCCallbacks.h; sourceTree = "<group>"; };
		E2B9477C1D15E3C100F2270D /* BNCFabricAnswers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BNCFabricAnswers.h; sourceTree = "<group>"; };
		E2B9477D1D15E3C100F2270D /* BNCFabricAnswers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BNCFabricAnswers.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E298D04E1C73D1B800589D22 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4DAB17701EE8A31B0079EEB4 /* Networking */ = {
			isa = PBXGroup;
			children = (
				4DAB17711EE8A31B0079EEB4 /* BNCNetworkService.h */,
				4DAB17721EE8A31B0079EEB4 /* BNCNetworkService.m */,
				4DAB17731EE8A31B0079EEB4 /* BNCNetworkServiceProtocol.h */,
				4DAB17741EE8A31B0079EEB4 /* BNCServerInterface.h */,
				4DAB17751EE8A31B0079EEB4 /* BNCServerInterface.m */,
				4DAB17761EE8A31B0079EEB4 /* BNCServerRequest.h */,
				4DAB17771EE8A31B0079EEB4 /* BNCServerRequest.m */,
				4DAB17781EE8A31B0079EEB4 /* BNCServerRequestQueue.h */,
				4DAB17791EE8A31B0079EEB4 /* BNCServerRequestQueue.m */,
				4DAB177A1EE8A31B0079EEB4 /* BNCServerResponse.h */,
				4DAB177B1EE8A31B0079EEB4 /* BNCServerResponse.m */,
				4DAB177C1EE8A31B0079EEB4 /* Requests */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		4DAB177C1EE8A31B0079EEB4 /* Requests */ = {
			isa = PBXGroup;
			children = (
				4DAB177D1EE8A31B0079EEB4 /* BranchCloseRequest.h */,
				4DAB177E1EE8A31B0079EEB4 /* BranchCloseRequest.m */,
				4DAB177F1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.h */,
				4DAB17801EE8A31B0079EEB4 /* BranchCreditHistoryRequest.m */,
				4DAB17811EE8A31B0079EEB4 /* BranchInstallRequest.h */,
				4DAB17821EE8A31B0079EEB4 /* BranchInstallRequest.m */,
				4DAB17831EE8A31B0079EEB4 /* BranchLoadRewardsRequest.h */,
				4DAB17841EE8A31B0079EEB4 /* BranchLoadRewardsRequest.m */,
				4DAB17851EE8A31B0079EEB4 /* BranchLogoutRequest.h */,
				4DAB17861EE8A31B0079EEB4 /* BranchLogoutRequest.m */,
				4DAB17871EE8A31B0079EEB4 /* BranchOpenRequest.h */,
				4DAB17881EE8A31B0079EEB4 /* BranchOpenRequest.m */,
				4DAB17891EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.h */,
				4DAB178A1EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.m */,
				4DAB178B1EE8A31B0079EEB4 /* BranchRegisterViewRequest.h */,
				4DAB178C1EE8A31B0079EEB4 /* BranchRegisterViewRequest.m */,
				4DAB178D1EE8A31B0079EEB4 /* BranchSetIdentityRequest.h */,
				4DAB178E1EE8A31B0079EEB4 /* BranchSetIdentityRequest.m */,
				4DAB178F1EE8A31B0079EEB4 /* BranchShortUrlRequest.h */,
				4DAB17901EE8A31B0079EEB4 /* BranchShortUrlRequest.m */,
				4DAB17911EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.h */,
				4DAB17921EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.m */,
				4DAB17931EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.h */,
				4DAB17941EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.m */,
				4DAB17951EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.h */,
				4DAB17961EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.m */,
			);
			path = Requests;
			sourceTree = "<group>";
		};
		E230A1141D03DB9E006181D8 /* Branch-SDK */ = {
			isa = PBXGroup;
			children = (
				5FB6D3DB23219C46006C5094 /* BNCThreads.h */,
				5FB6D3DC23219C46006C5094 /* BNCThreads.m */,
				5FB6D3D423219B48006C5094 /* BNCAppleReceipt.h */,
				5FB6D3D523219B48006C5094 /* BNCAppleReceipt.m */,
				5FB6D3D323219B48006C5094 /* BNCUserAgentCollector.h */,
				5FB6D3D223219B48006C5094 /* BNCUserAgentCollector.m */,
				4D1851C32019127600E48994 /* BNCApplication.h */,
				4D1851C42019127700E48994 /* BNCApplication.m */,
				4D1ED27C1FB3A439007390A8 /* BNCAvailability.h */,
				4D1ED27B1FB3A439007390A8 /* BNCAvailability.m */,
				E2B9477A1D15E31700F2270D /* BNCCallbacks.h */,
				4D3922BC1E1F0C85004FB7C8 /* BNCCommerceEvent.h */,
				4DA40A8D1E1F0011004D2534 /* BNCCommerceEvent.m */,
				E230A1151D03DB9E006181D8 /* BNCConfig.h */,
				4D3FA94C1DFF3F6C00E2B6A9 /* BNCConfig.m */,
				E230A1161D03DB9E006181D8 /* BNCContentDiscoveryManager.h */,
				E230A1171D03DB9E006181D8 /* BNCContentDiscoveryManager.m */,
				4D78C9631F2679F000EEDD5F /* BNCCrashlyticsWrapper.h */,
				4D78C9641F2679F000EEDD5F /* BNCCrashlyticsWrapper.m */,
				2BD7C3D71F27CEDB003696AF /* BNCDebug.h */,
				2BD7C3D81F27CEDB003696AF /* BNCDebug.m */,
				4D510D701F0D6014003D720B /* BNCDeepLinkViewControllerInstance.h */,
				4D510D711F0D6014003D720B /* BNCDeepLinkViewControllerInstance.m */,
				E230A1181D03DB9E006181D8 /* BNCDeviceInfo.h */,
				E230A1191D03DB9E006181D8 /* BNCDeviceInfo.m */,
				E230A11A1D03DB9E006181D8 /* BNCEncodingUtils.h */,
				E230A11B1D03DB9E006181D8 /* BNCEncodingUtils.m */,
				E230A11C1D03DB9E006181D8 /* BNCError.h */,
				E230A11D1D03DB9E006181D8 /* BNCError.m */,
				E2B9477C1D15E3C100F2270D /* BNCFabricAnswers.h */,
				E2B9477D1D15E3C100F2270D /* BNCFabricAnswers.m */,
				4DB328021FA10C6200ACF9B0 /* BNCFieldDefines.h */,
				4D1851C52019127700E48994 /* BNCKeyChain.h */,
				4D1851C62019127700E48994 /* BNCKeyChain.m */,
				E230A11E1D03DB9E006181D8 /* BNCLinkCache.h */,
				E230A11F1D03DB9E006181D8 /* BNCLinkCache.m */,
				E230A1201D03DB9E006181D8 /* BNCLinkData.h */,
				E230A1211D03DB9E006181D8 /* BNCLinkData.m */,
				4D6A27551F2954DE00027264 /* BNCLocalization.h */,
				4D6A27561F2954DE00027264 /* BNCLocalization.m */,
				2BD7C3D91F27CEDB003696AF /* BNCLog.h */,
				2BD7C3DA1F27CEDB003696AF /* BNCLog.m */,
				E230A1221D03DB9E006181D8 /* BNCPreferenceHelper.h */,
				E230A1231D03DB9E006181D8 /* BNCPreferenceHelper.m */,
				4D1ED27F1FB3A471007390A8 /* BNCSpotlightService.h */,
				4D1ED2801FB3A471007390A8 /* BNCSpotlightService.m */,
				E230A12A1D03DB9E006181D8 /* BNCStrongMatchHelper.h */,
				E230A12B1D03DB9E006181D8 /* BNCStrongMatchHelper.m */,
				E230A12C1D03DB9E006181D8 /* BNCSystemObserver.h */,
				E230A12D1D03DB9E006181D8 /* BNCSystemObserver.m */,
				4D3636022040A504006A1FDE /* BNCURLBlackList.h */,
				4D3636032040A504006A1FDE /* BNCURLBlackList.m */,
				E230A12F1D03DB9E006181D8 /* Branch.h */,
				E230A1301D03DB9E006181D8 /* Branch.m */,
				9A149C6520336600002135DC /* Branch+Validator.h */,
				9A149C6620336600002135DC /* Branch+Validator.m */,
				E230A1311D03DB9E006181D8 /* BranchActivityItemProvider.h */,
				E230A1321D03DB9E006181D8 /* BranchActivityItemProvider.m */,
				E230A1331D03DB9E006181D8 /* BranchConstants.h */,
				E230A1341D03DB9E006181D8 /* BranchConstants.m */,
				7DA3BF181D889CE500CA8AE0 /* BranchContentDiscoverer.h */,
				7DA3BF171D889CE500CA8AE0 /* BranchContentDiscoverer.m */,
				7DA3BF191D889CE500CA8AE0 /* BranchContentDiscoveryManifest.h */,
				7DA3BF1A1D889CE500CA8AE0 /* BranchContentDiscoveryManifest.m */,
				7DA3BF1B1D889CE500CA8AE0 /* BranchContentPathProperties.h */,
				7DA3BF1C1D889CE500CA8AE0 /* BranchContentPathProperties.m */,
				E230A1351D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.h */,
				E230A1361D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.m */,
				E230A1371D03DB9E006181D8 /* BranchDeepLinkingController.h */,
				4DAF63B81F86C269006316E9 /* BranchDelegate.h */,
				4DAF63B91F86C26A006316E9 /* BranchDelegate.m */,
				4DB328031FA10C6200ACF9B0 /* BranchEvent.h */,
				4DB328041FA10C6300ACF9B0 /* BranchEvent.m */,
				E230A1381D03DB9E006181D8 /* BranchLinkProperties.h */,
				E230A1391D03DB9E006181D8 /* BranchLinkProperties.m */,
				4DB567381E79FDAF00A8A324 /* BranchShareLink.h */,
				4DB567391E79FDAF00A8A324 /* BranchShareLink.m */,
				E230A13B1D03DB9E006181D8 /* BranchUniversalObject.h */,
				E230A13C1D03DB9E006181D8 /* BranchUniversalObject.m */,
				4DAB17701EE8A31B0079EEB4 /* Networking */,
				4D291AA91E322ECC007ED118 /* NSMutableDictionary+Branch.h */,
				4D291AAA1E322ECC007ED118 /* NSMutableDictionary+Branch.m */,
				2BD7C3E31F27CF68003696AF /* NSString+Branch.h */,
				2BD7C3E41F27CF68003696AF /* NSString+Branch.m */,
				4D9607F71FBF9633008AB3C2 /* UIViewController+Branch.h */,
				4D9607F81FBF9633008AB3C2 /* UIViewController+Branch.m */,
			);
			name = "Branch-SDK";
			path = "../Branch-SDK/Branch-SDK";
			sourceTree = "<group>";
		};
		E298D0481C73D1B800589D22 = {
			isa = PBXGroup;
			children = (
				E298D0571C73D1B800589D22 /* Info.plist */,
				E230A1141D03DB9E006181D8 /* Branch-SDK */,
				E298D0531C73D1B800589D22 /* Products */,
			);
			sourceTree = "<group>";
		};
		E298D0531C73D1B800589D22 /* Products */ = {
			isa = PBXGroup;
			children = (
				E298D0521C73D1B800589D22 /* Branch.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		E298D04F1C73D1B800589D22 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5FB6D3DD23219C46006C5094 /* BNCThreads.h in Headers */,
				4D1ED27E1FB3A43A007390A8 /* BNCAvailability.h in Headers */,
				E2B9477B1D15E31700F2270D /* BNCCallbacks.h in Headers */,
				4D3922BD1E1F0C85004FB7C8 /* BNCCommerceEvent.h in Headers */,
				E230A16C1D03DB9E006181D8 /* BNCConfig.h in Headers */,
				2BD7C3DD1F27CEDB003696AF /* BNCDebug.h in Headers */,
				4DCF4B001F438A2B00AF9AAB /* BNCError.h in Headers */,
				E230A1751D03DB9E006181D8 /* BNCLinkCache.h in Headers */,
				5FB6D3D723219B48006C5094 /* BNCUserAgentCollector.h in Headers */,
				4DCF4B011F438A2B00AF9AAB /* BNCLog.h in Headers */,
				E230A1771D03DB9E006181D8 /* BNCLinkData.h in Headers */,
				E230A1791D03DB9E006181D8 /* BNCPreferenceHelper.h in Headers */,
				4D3636042040A504006A1FDE /* BNCURLBlackList.h in Headers */,
				4DAB179F1EE8A31B0079EEB4 /* BNCNetworkServiceProtocol.h in Headers */,
				4DAB17A01EE8A31B0079EEB4 /* BNCServerInterface.h in Headers */,
				4DAB17A61EE8A31B0079EEB4 /* BNCServerResponse.h in Headers */,
				4D1ED2811FB3A472007390A8 /* BNCSpotlightService.h in Headers */,
				4DAB17A21EE8A31B0079EEB4 /* BNCServerRequest.h in Headers */,
				4DAB17A41EE8A31B0079EEB4 /* BNCServerRequestQueue.h in Headers */,
				E230A1861D03DB9E006181D8 /* Branch.h in Headers */,
				E230A1881D03DB9E006181D8 /* BranchActivityItemProvider.h in Headers */,
				E230A18A1D03DB9E006181D8 /* BranchConstants.h in Headers */,
				E230A18E1D03DB9E006181D8 /* BranchDeepLinkingController.h in Headers */,
				4D778E1E218253F200308B51 /* BranchCSSearchableItemAttributeSet.h in Headers */,
				4DAF63BA1F86C26A006316E9 /* BranchDelegate.h in Headers */,
				4DAB179D1EE8A31B0079EEB4 /* BNCNetworkService.h in Headers */,
				4DB328061FA10C6300ACF9B0 /* BranchEvent.h in Headers */,
				E230A1921D03DB9E006181D8 /* BranchUniversalObject.h in Headers */,
				E230A18F1D03DB9E006181D8 /* BranchLinkProperties.h in Headers */,
				4DB5673A1E79FDAF00A8A324 /* BranchShareLink.h in Headers */,
				4D9607F91FBF9634008AB3C2 /* UIViewController+Branch.h in Headers */,
				4DAB17B81EE8A31B0079EEB4 /* BranchSetIdentityRequest.h in Headers */,
				4D510D721F0D6014003D720B /* BNCDeepLinkViewControllerInstance.h in Headers */,
				4DAB17BA1EE8A31B0079EEB4 /* BranchShortUrlRequest.h in Headers */,
				7DA3BF211D889CE500CA8AE0 /* BranchContentPathProperties.h in Headers */,
				4DAB17B41EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.h in Headers */,
				4DAB17AC1EE8A31B0079EEB4 /* BranchInstallRequest.h in Headers */,
				7DA3BF1E1D889CE500CA8AE0 /* BranchContentDiscoverer.h in Headers */,
				4DAB17AE1EE8A31B0079EEB4 /* BranchLoadRewardsRequest.h in Headers */,
				4D78C9651F2679F000EEDD5F /* BNCCrashlyticsWrapper.h in Headers */,
				4DAB17B21EE8A31B0079EEB4 /* BranchOpenRequest.h in Headers */,
				9A149C6720336601002135DC /* Branch+Validator.h in Headers */,
				E230A16F1D03DB9E006181D8 /* BNCDeviceInfo.h in Headers */,
				4D6A27571F2954DE00027264 /* BNCLocalization.h in Headers */,
				4D1851C72019127700E48994 /* BNCApplication.h in Headers */,
				2BD7C3E51F27CF68003696AF /* NSString+Branch.h in Headers */,
				E230A1811D03DB9E006181D8 /* BNCStrongMatchHelper.h in Headers */,
				4DAB17B01EE8A31B0079EEB4 /* BranchLogoutRequest.h in Headers */,
				4DAB17A81EE8A31B0079EEB4 /* BranchCloseRequest.h in Headers */,
				E230A16D1D03DB9E006181D8 /* BNCContentDiscoveryManager.h in Headers */,
				4D1851C92019127700E48994 /* BNCKeyChain.h in Headers */,
				E230A1711D03DB9E006181D8 /* BNCEncodingUtils.h in Headers */,
				4D301CE31FA1452100642F4A /* BNCFieldDefines.h in Headers */,
				5FB6D3D823219B48006C5094 /* BNCAppleReceipt.h in Headers */,
				4DAB17B61EE8A31B0079EEB4 /* BranchRegisterViewRequest.h in Headers */,
				4D291AAB1E322ECC007ED118 /* NSMutableDictionary+Branch.h in Headers */,
				4DAB17AA1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.h in Headers */,
				E230A1831D03DB9E006181D8 /* BNCSystemObserver.h in Headers */,
				4DAB17BE1EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.h in Headers */,
				4DAB17BC1EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.h in Headers */,
				7DA3BF1F1D889CE500CA8AE0 /* BranchContentDiscoveryManifest.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		E298D0511C73D1B800589D22 /* Branch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E298D05A1C73D1B800589D22 /* Build configuration list for PBXNativeTarget "Branch" */;
			buildPhases = (
				E298D04D1C73D1B800589D22 /* Sources */,
				E298D04E1C73D1B800589D22 /* Frameworks */,
				E298D04F1C73D1B800589D22 /* Headers */,
				E298D0501C73D1B800589D22 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Branch;
			productName = "Branch iOS SDK Carthage";
			productReference = E298D0521C73D1B800589D22 /* Branch.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E298D0491C73D1B800589D22 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = BNC;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = "Branch, Inc.";
				TargetAttributes = {
					E298D0511C73D1B800589D22 = {
						CreatedOnToolsVersion = 7.2.1;
					};
				};
			};
			buildConfigurationList = E298D04C1C73D1B800589D22 /* Build configuration list for PBXProject "BranchSDK" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E298D0481C73D1B800589D22;
			productRefGroup = E298D0531C73D1B800589D22 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E298D0511C73D1B800589D22 /* Branch */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E298D0501C73D1B800589D22 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E298D04D1C73D1B800589D22 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E230A16E1D03DB9E006181D8 /* BNCContentDiscoveryManager.m in Sources */,
				2BD7C3E01F27CEDB003696AF /* BNCLog.m in Sources */,
				4DAB17A31EE8A31B0079EEB4 /* BNCServerRequest.m in Sources */,
				E230A1701D03DB9E006181D8 /* BNCDeviceInfo.m in Sources */,
				7DA3BF201D889CE500CA8AE0 /* BranchContentDiscoveryManifest.m in Sources */,
				4DB328071FA10C6300ACF9B0 /* BranchEvent.m in Sources */,
				4DAB17AF1EE8A31B0079EEB4 /* BranchLoadRewardsRequest.m in Sources */,
				E230A1931D03DB9E006181D8 /* BranchUniversalObject.m in Sources */,
				4DAB17C11EE8A31B0079EEB4 /* BranchUserCompletedActionRequest.m in Sources */,
				E230A1741D03DB9E006181D8 /* BNCError.m in Sources */,
				4D1ED27D1FB3A43A007390A8 /* BNCAvailability.m in Sources */,
				4DAB17AD1EE8A31B0079EEB4 /* BranchInstallRequest.m in Sources */,
				4DAB17A11EE8A31B0079EEB4 /* BNCServerInterface.m in Sources */,
				5FB6D3D923219B48006C5094 /* BNCAppleReceipt.m in Sources */,
				4DAB179E1EE8A31B0079EEB4 /* BNCNetworkService.m in Sources */,
				4D1851CA2019127700E48994 /* BNCKeyChain.m in Sources */,
				4DAB17A51EE8A31B0079EEB4 /* BNCServerRequestQueue.m in Sources */,
				E230A1901D03DB9E006181D8 /* BranchLinkProperties.m in Sources */,
				4DAB17A91EE8A31B0079EEB4 /* BranchCloseRequest.m in Sources */,
				4DAB17B71EE8A31B0079EEB4 /* BranchRegisterViewRequest.m in Sources */,
				4DAB17B91EE8A31B0079EEB4 /* BranchSetIdentityRequest.m in Sources */,
				4DA40A8F1E1F0011004D2534 /* BNCCommerceEvent.m in Sources */,
				E230A1781D03DB9E006181D8 /* BNCLinkData.m in Sources */,
				4DAB17BF1EE8A31B0079EEB4 /* BranchSpotlightUrlRequest.m in Sources */,
				E230A1721D03DB9E006181D8 /* BNCEncodingUtils.m in Sources */,
				4DAB17B11EE8A31B0079EEB4 /* BranchLogoutRequest.m in Sources */,
				4DAB17AB1EE8A31B0079EEB4 /* BranchCreditHistoryRequest.m in Sources */,
				E230A1891D03DB9E006181D8 /* BranchActivityItemProvider.m in Sources */,
				4DAB17BB1EE8A31B0079EEB4 /* BranchShortUrlRequest.m in Sources */,
				4D9607FA1FBF9634008AB3C2 /* UIViewController+Branch.m in Sources */,
				4D510D731F0D6014003D720B /* BNCDeepLinkViewControllerInstance.m in Sources */,
				5FB6D3D623219B48006C5094 /* BNCUserAgentCollector.m in Sources */,
				4DB5673B1E79FDAF00A8A324 /* BranchShareLink.m in Sources */,
				4DAB17B51EE8A31B0079EEB4 /* BranchRedeemRewardsRequest.m in Sources */,
				4DAB17B31EE8A31B0079EEB4 /* BranchOpenRequest.m in Sources */,
				2BD7C3DE1F27CEDB003696AF /* BNCDebug.m in Sources */,
				4D3636052040A504006A1FDE /* BNCURLBlackList.m in Sources */,
				4DAB17BD1EE8A31B0079EEB4 /* BranchShortUrlSyncRequest.m in Sources */,
				4D1ED2821FB3A472007390A8 /* BNCSpotlightService.m in Sources */,
				E230A18B1D03DB9E006181D8 /* BranchConstants.m in Sources */,
				E230A1871D03DB9E006181D8 /* Branch.m in Sources */,
				E230A1761D03DB9E006181D8 /* BNCLinkCache.m in Sources */,
				7DA3BF1D1D889CE500CA8AE0 /* BranchContentDiscoverer.m in Sources */,
				4D291AAC1E322ECC007ED118 /* NSMutableDictionary+Branch.m in Sources */,
				9A149C6820336601002135DC /* Branch+Validator.m in Sources */,
				5FB6D3DE23219C46006C5094 /* BNCThreads.m in Sources */,
				4D78C9661F2679F000EEDD5F /* BNCCrashlyticsWrapper.m in Sources */,
				2BD7C3E61F27CF68003696AF /* NSString+Branch.m in Sources */,
				E230A17A1D03DB9E006181D8 /* BNCPreferenceHelper.m in Sources */,
				4D3FA94D1DFF3F6C00E2B6A9 /* BNCConfig.m in Sources */,
				E230A18D1D03DB9E006181D8 /* BranchCSSearchableItemAttributeSet.m in Sources */,
				4D6A27581F2954DE00027264 /* BNCLocalization.m in Sources */,
				E230A1821D03DB9E006181D8 /* BNCStrongMatchHelper.m in Sources */,
				E230A1841D03DB9E006181D8 /* BNCSystemObserver.m in Sources */,
				4DAB17A71EE8A31B0079EEB4 /* BNCServerResponse.m in Sources */,
				4D1851C82019127700E48994 /* BNCApplication.m in Sources */,
				4DAF63BB1F86C26A006316E9 /* BranchDelegate.m in Sources */,
				7DA3BF221D889CE500CA8AE0 /* BranchContentPathProperties.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		E298D0581C73D1B800589D22 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		E298D0591C73D1B800589D22 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E298D05B1C73D1B800589D22 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.branch.ios-carthage";
				PRODUCT_MODULE_NAME = "$(TARGET_NAME)";
				PRODUCT_NAME = Branch;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		E298D05C1C73D1B800589D22 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.branch.ios-carthage";
				PRODUCT_MODULE_NAME = "$(TARGET_NAME)";
				PRODUCT_NAME = Branch;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E298D04C1C73D1B800589D22 /* Build configuration list for PBXProject "BranchSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E298D0581C73D1B800589D22 /* Debug */,
				E298D0591C73D1B800589D22 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E298D05A1C73D1B800589D22 /* Build configuration list for PBXNativeTarget "Branch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E298D05B1C73D1B800589D22 /* Debug */,
				E298D05C1C73D1B800589D22 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E298D0491C73D1B800589D22 /* Project object */;
}
