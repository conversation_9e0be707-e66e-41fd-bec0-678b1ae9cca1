<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1010"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "6700165F1940F51400A9E103"
               BuildableName = "Branch-TestBed.app"
               BlueprintName = "Branch-TestBed"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F1D4F9AB1F323F01002D13FF"
               BuildableName = "Branch-TestBed-UITests.xctest"
               BlueprintName = "Branch-TestBed-UITests"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      enableThreadSanitizer = "YES"
      enableUBSanitizer = "YES"
      codeCoverageEnabled = "YES"
      shouldUseLaunchSchemeArgsEnv = "NO">
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
               BuildableName = "Branch-SDK-Tests.xctest"
               BlueprintName = "Branch-SDK-Tests"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
            <DeviceAppData
               resolvedPath = "../Simulate-FirstRun.xcappdata">
            </DeviceAppData>
         </TestableReference>
         <TestableReference
            skipped = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F1D4F9AB1F323F01002D13FF"
               BuildableName = "Branch-TestBed-UITests.xctest"
               BlueprintName = "Branch-TestBed-UITests"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "6700165F1940F51400A9E103"
            BuildableName = "Branch-TestBed.app"
            BlueprintName = "Branch-TestBed"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "BNCTestBreakpoints"
            value = "0"
            isEnabled = "YES">
         </EnvironmentVariable>
      </EnvironmentVariables>
      <AdditionalOptions>
         <AdditionalOption
            key = "NSZombieEnabled"
            value = "YES"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      enableThreadSanitizer = "YES"
      language = "ru"
      region = "RU"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      stopOnEveryThreadSanitizerIssue = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "6700165F1940F51400A9E103"
            BuildableName = "Branch-TestBed.app"
            BlueprintName = "Branch-TestBed"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <EnvironmentVariables>
         <EnvironmentVariable
            key = "OS_ACTIVITY_MODE"
            value = "disable"
            isEnabled = "NO">
         </EnvironmentVariable>
      </EnvironmentVariables>
      <AdditionalOptions>
      </AdditionalOptions>
      <LocationScenarioReference
         identifier = "com.apple.dt.IDEFoundation.CurrentLocationScenarioIdentifier"
         referenceType = "1">
      </LocationScenarioReference>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "6700165F1940F51400A9E103"
            BuildableName = "Branch-TestBed.app"
            BlueprintName = "Branch-TestBed"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
