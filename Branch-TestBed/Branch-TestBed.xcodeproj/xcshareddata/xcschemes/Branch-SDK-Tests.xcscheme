<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1010"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
               BuildableName = "Branch-SDK-Tests.xctest"
               BlueprintName = "Branch-SDK-Tests"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      enableThreadSanitizer = "YES"
      codeCoverageEnabled = "YES"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
               BuildableName = "Branch-SDK-Tests.xctest"
               BlueprintName = "Branch-SDK-Tests"
               ReferencedContainer = "container:Branch-TestBed.xcodeproj">
            </BuildableReference>
            <SkippedTests>
               <Test
                  Identifier = "BNCServerInterfaceTests/testGetRequestAsyncRetriesWhenAppropriate">
               </Test>
               <Test
                  Identifier = "BNCServerInterfaceTests/testGetRequestAsyncRetriesWhenInappropriateResponse">
               </Test>
               <Test
                  Identifier = "BNCServerInterfaceTests/testGetRequestAsyncRetriesWhenInappropriateRetryCount">
               </Test>
               <Test
                  Identifier = "BNCServerInterfaceTests/testPostRequestAsyncRetriesWhenAppropriate">
               </Test>
               <Test
                  Identifier = "BNCServerInterfaceTests/testPostRequestAsyncRetriesWhenInappropriateResponse">
               </Test>
               <Test
                  Identifier = "BNCServerInterfaceTests/testPostRequestAsyncRetriesWhenInappropriateRetryCount">
               </Test>
            </SkippedTests>
         </TestableReference>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
            BuildableName = "Branch-SDK-Tests.xctest"
            BlueprintName = "Branch-SDK-Tests"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
         <AdditionalOption
            key = "NSZombieEnabled"
            value = "YES"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      enableThreadSanitizer = "YES"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      stopOnEveryThreadSanitizerIssue = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
            BuildableName = "Branch-SDK-Tests.xctest"
            BlueprintName = "Branch-SDK-Tests"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
         <AdditionalOption
            key = "NSZombieEnabled"
            value = "YES"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "7E6B3B501AA42D0E005F45BF"
            BuildableName = "Branch-SDK-Tests.xctest"
            BlueprintName = "Branch-SDK-Tests"
            ReferencedContainer = "container:Branch-TestBed.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
