<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="5XF-Tz-7zj">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13527"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--TestBed-->
        <scene sceneID="l46-6g-ekY">
            <objects>
                <tableViewController id="5XF-Tz-7zj" userLabel="TestBed" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="top" alwaysBounceVertical="YES" dataMode="static" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="44" sectionHeaderHeight="28" sectionFooterHeight="28" id="Wew-Xa-SfF">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <sections>
                            <tableViewSection id="vjS-B3-fm2">
                                <cells>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="7JC-0E-Wi2">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="7JC-0E-Wi2" id="3kO-e8-RYc">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Branch Link" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="hI6-Pg-mcQ" userLabel="branchLinkTextField">
                                                    <rect key="frame" x="47.5" y="7" width="280" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="280" id="nBo-Z9-Qab"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="done"/>
                                                </textField>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="hI6-Pg-mcQ" firstAttribute="centerX" secondItem="3kO-e8-RYc" secondAttribute="centerX" id="1xo-9W-FJG"/>
                                                <constraint firstItem="hI6-Pg-mcQ" firstAttribute="centerY" secondItem="3kO-e8-RYc" secondAttribute="centerY" id="fEM-6u-bKS"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="6q1-HK-w21">
                                        <rect key="frame" x="0.0" y="44" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="6q1-HK-w21" id="ysS-3p-Syq">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y6b-UK-qcE" userLabel="createBranchLinkButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.27450980390000002" green="0.27058823529999998" blue="0.2784313725" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="F2f-uU-KYk"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Create Branch Link">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="createBranchLinkButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="lFF-O1-A8P"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="y6b-UK-qcE" firstAttribute="centerX" secondItem="ysS-3p-Syq" secondAttribute="centerX" id="G8f-rO-BZJ"/>
                                                <constraint firstItem="y6b-UK-qcE" firstAttribute="centerY" secondItem="ysS-3p-Syq" secondAttribute="centerY" id="XLg-iZ-Tkh"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="vMX-nu-TV1">
                                        <rect key="frame" x="0.0" y="88" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="vMX-nu-TV1" id="QrH-TO-pcR">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zfN-8g-iSY" userLabel="pointsLabel">
                                                    <rect key="frame" x="183.5" y="15" width="8" height="15"/>
                                                    <accessibility key="accessibilityConfiguration" label="0"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reward Points" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Igq-yS-1WM" userLabel="rewardPointsLabel">
                                                    <rect key="frame" x="16" y="15" width="81" height="15"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <activityIndicatorView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" hidesWhenStopped="YES" animating="YES" style="white" translatesAutoresizingMaskIntoConstraints="NO" id="Iut-QD-0zF" userLabel="activityIndicator">
                                                    <rect key="frame" x="177.5" y="12" width="20" height="20"/>
                                                </activityIndicatorView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="Igq-yS-1WM" firstAttribute="leading" secondItem="QrH-TO-pcR" secondAttribute="leadingMargin" id="5Mf-JJ-q24"/>
                                                <constraint firstItem="Iut-QD-0zF" firstAttribute="centerX" secondItem="QrH-TO-pcR" secondAttribute="centerX" id="7gn-sG-Wku"/>
                                                <constraint firstItem="zfN-8g-iSY" firstAttribute="centerX" secondItem="QrH-TO-pcR" secondAttribute="centerX" id="aQt-bu-Pfy"/>
                                                <constraint firstItem="Igq-yS-1WM" firstAttribute="centerY" secondItem="QrH-TO-pcR" secondAttribute="centerY" id="eiJ-mm-mBm"/>
                                                <constraint firstItem="Iut-QD-0zF" firstAttribute="centerY" secondItem="QrH-TO-pcR" secondAttribute="centerY" id="mEv-OO-y7n"/>
                                                <constraint firstItem="zfN-8g-iSY" firstAttribute="centerY" secondItem="QrH-TO-pcR" secondAttribute="centerY" id="ugN-86-CnI"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="F9T-rB-wr8">
                                        <rect key="frame" x="0.0" y="132" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="F9T-rB-wr8" id="x4h-HL-dol">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iHe-K8-5b7" userLabel="redeem5PointsButton">
                                                    <rect key="frame" x="16" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="uVp-r7-odl"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Redeem 5 Points">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="redeemFivePointsButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="aVj-pk-zou"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Gvo-ig-DXh" userLabel="setUserIDButton">
                                                    <rect key="frame" x="219" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="opi-Xg-bCf"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Set User ID">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="setUserIDButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="Tlm-h5-cTF"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="iHe-K8-5b7" firstAttribute="centerY" secondItem="x4h-HL-dol" secondAttribute="centerY" id="4zG-Bx-k82"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="Gvo-ig-DXh" secondAttribute="trailing" id="ICP-PP-TXO"/>
                                                <constraint firstItem="iHe-K8-5b7" firstAttribute="leading" secondItem="x4h-HL-dol" secondAttribute="leadingMargin" id="wef-SU-rPc"/>
                                                <constraint firstItem="Gvo-ig-DXh" firstAttribute="centerY" secondItem="x4h-HL-dol" secondAttribute="centerY" id="yDY-4u-DXk"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="WJ8-YP-kGW">
                                        <rect key="frame" x="0.0" y="176" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="WJ8-YP-kGW" id="mbf-qp-UtI">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="b1a-Qu-ft1" userLabel="refreshRewardPointsButton">
                                                    <rect key="frame" x="16" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="mBi-cL-1i2"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Refresh Reward Points">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="refreshRewardsButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="4sm-mk-ajD"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9Zw-5o-CK3" userLabel="simulateLogoutButton">
                                                    <rect key="frame" x="219" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="Vcv-Mu-UxJ"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Simulate Logout">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="logoutWithCallback" destination="5XF-Tz-7zj" eventType="touchUpInside" id="Zm0-Cd-Txl"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="b1a-Qu-ft1" firstAttribute="leading" secondItem="mbf-qp-UtI" secondAttribute="leadingMargin" id="QlI-Qf-NKZ"/>
                                                <constraint firstItem="b1a-Qu-ft1" firstAttribute="centerY" secondItem="mbf-qp-UtI" secondAttribute="centerY" id="pSH-jC-nNT"/>
                                                <constraint firstItem="9Zw-5o-CK3" firstAttribute="centerY" secondItem="mbf-qp-UtI" secondAttribute="centerY" id="qDF-RT-rIg"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="9Zw-5o-CK3" secondAttribute="trailing" id="sUb-JB-00o"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="4Ed-vk-UV4">
                                        <rect key="frame" x="0.0" y="220" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="4Ed-vk-UV4" id="yeh-vZ-Pvw">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WGf-Zp-tZq" userLabel="sendBuyEventButton">
                                                    <rect key="frame" x="16" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="kiE-ia-uca"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Send &quot;Buy&quot; Event">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="sendBuyEventButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="9vL-tI-G0q"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kPP-OI-PHS" userLabel="sendComplexEventButton">
                                                    <rect key="frame" x="219" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="8N2-Cd-qhv"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Send Complex Event">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="sendComplexEventButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="N2G-GU-DKO"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="WGf-Zp-tZq" firstAttribute="leading" secondItem="yeh-vZ-Pvw" secondAttribute="leadingMargin" id="0aW-y7-3JH"/>
                                                <constraint firstItem="kPP-OI-PHS" firstAttribute="centerY" secondItem="yeh-vZ-Pvw" secondAttribute="centerY" id="Be8-vZ-680"/>
                                                <constraint firstItem="WGf-Zp-tZq" firstAttribute="centerY" secondItem="yeh-vZ-Pvw" secondAttribute="centerY" id="OtA-WR-QQj"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="kPP-OI-PHS" secondAttribute="trailing" id="xml-2u-Y5n"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="Usk-UV-tfb">
                                        <rect key="frame" x="0.0" y="264" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Usk-UV-tfb" id="nWx-Ls-AD5">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Zp-nX-mHX" userLabel="showRewardsHistoryButton">
                                                    <rect key="frame" x="16" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="msy-In-VvR"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Show Rewards History">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="getCreditHistoryButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="dBb-no-GAe"/>
                                                    </connections>
                                                </button>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PNx-8Z-tOL" userLabel="simulateReferralsButton">
                                                    <rect key="frame" x="219" y="9" width="140" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="140" id="teE-Xa-47Y"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Simulate Referrals">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="simulateReferralsButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="S20-1A-ez8"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="5Zp-nX-mHX" firstAttribute="leading" secondItem="nWx-Ls-AD5" secondAttribute="leadingMargin" id="0Xo-J3-6FT"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="PNx-8Z-tOL" secondAttribute="trailing" id="Db7-KQ-MXx"/>
                                                <constraint firstItem="5Zp-nX-mHX" firstAttribute="centerY" secondItem="nWx-Ls-AD5" secondAttribute="centerY" id="LUX-8D-YUl"/>
                                                <constraint firstItem="PNx-8Z-tOL" firstAttribute="centerY" secondItem="nWx-Ls-AD5" secondAttribute="centerY" id="VSD-ng-teB"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="NrB-u2-mrS">
                                        <rect key="frame" x="0.0" y="308" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="NrB-u2-mrS" id="rrI-5q-W8n">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GhX-bC-4ml" userLabel="viewFirstReferringParamsButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="Ypc-sf-qtc"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="View FirstReferringParams">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="viewFirstReferringParamsButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="blc-y5-HBr"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="GhX-bC-4ml" firstAttribute="centerX" secondItem="rrI-5q-W8n" secondAttribute="centerX" id="3Jh-Zk-eK1"/>
                                                <constraint firstItem="GhX-bC-4ml" firstAttribute="centerY" secondItem="rrI-5q-W8n" secondAttribute="centerY" id="qLf-Xu-mGK"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="na8-MS-JZJ">
                                        <rect key="frame" x="0.0" y="352" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="na8-MS-JZJ" id="sFd-ow-hIu">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oFa-g9-cjV" userLabel="viewLatestReferringParamsButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="KmS-an-voP"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="View LatestReferringParams">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="viewLatestReferringParamsButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="jt6-aW-Ud3"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="oFa-g9-cjV" firstAttribute="centerX" secondItem="sFd-ow-hIu" secondAttribute="centerX" id="Eq5-vu-8Lb"/>
                                                <constraint firstItem="oFa-g9-cjV" firstAttribute="centerY" secondItem="sFd-ow-hIu" secondAttribute="centerY" id="KtO-oK-IE8"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="4an-O2-JZW">
                                        <rect key="frame" x="0.0" y="396" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="4an-O2-JZW" id="x21-xY-jDH">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bw0-bO-aBs" userLabel="simulateContentAccessButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="W5A-Qj-G2q"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Simulate Content Access">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="simulateContentAccessButtonTouchUpInsideButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="FZ4-Je-pJM"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="bw0-bO-aBs" firstAttribute="centerY" secondItem="x21-xY-jDH" secondAttribute="centerY" id="1Cg-Ub-NYw"/>
                                                <constraint firstItem="bw0-bO-aBs" firstAttribute="centerX" secondItem="x21-xY-jDH" secondAttribute="centerX" id="bI0-6r-lxB"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="VWl-ve-nx2">
                                        <rect key="frame" x="0.0" y="440" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="VWl-ve-nx2" id="guM-eT-6Uh">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="w4V-cP-QS9" userLabel="shareLinkButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="qSk-j6-JkS"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Share Link">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="shareLinkButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="fpP-Tj-JHt"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="w4V-cP-QS9" firstAttribute="centerY" secondItem="guM-eT-6Uh" secondAttribute="centerY" id="4DK-lj-YcW"/>
                                                <constraint firstItem="w4V-cP-QS9" firstAttribute="centerX" secondItem="guM-eT-6Uh" secondAttribute="centerX" id="iCf-VR-o6N"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" id="MXJ-4S-PwT">
                                        <rect key="frame" x="0.0" y="484" width="375" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="MXJ-4S-PwT" id="B7Y-kQ-RHA">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1Rj-F4-ULE" userLabel="registerWithSpotlightButton">
                                                    <rect key="frame" x="92.5" y="9" width="190" height="27"/>
                                                    <color key="backgroundColor" red="0.28235294119999998" green="0.2784313725" blue="0.28627450980000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="190" id="N9j-DD-YQO"/>
                                                    </constraints>
                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                    <state key="normal" title="Register with Spotlight">
                                                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </state>
                                                    <connections>
                                                        <action selector="registerWithSpotlightButtonTouchUpInside:" destination="5XF-Tz-7zj" eventType="touchUpInside" id="kes-aT-4Tt"/>
                                                    </connections>
                                                </button>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="1Rj-F4-ULE" firstAttribute="centerY" secondItem="B7Y-kQ-RHA" secondAttribute="centerY" id="ZkA-42-MN7"/>
                                                <constraint firstItem="1Rj-F4-ULE" firstAttribute="centerX" secondItem="B7Y-kQ-RHA" secondAttribute="centerX" id="vuc-Oe-4hK"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.14117647059999999" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </cells>
                            </tableViewSection>
                        </sections>
                        <connections>
                            <outlet property="dataSource" destination="5XF-Tz-7zj" id="gC9-fy-gaI"/>
                            <outlet property="delegate" destination="5XF-Tz-7zj" id="1xT-4n-mdj"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" title="Branch-TestBed" id="gkg-Vr-sq6" userLabel="Branch-TestBed"/>
                    <connections>
                        <outlet property="activityIndicator" destination="Iut-QD-0zF" id="zeB-UE-Fcw"/>
                        <outlet property="createBranchLinkButton" destination="y6b-UK-qcE" id="1pk-Sw-8dI"/>
                        <outlet property="editRefShortUrl" destination="hI6-Pg-mcQ" id="8cA-Mu-x8J"/>
                        <outlet property="pointsLabel" destination="zfN-8g-iSY" id="ofI-Oc-SuB"/>
                    </connections>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kc7-vr-YCL" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1956" y="157"/>
        </scene>
    </scenes>
</document>
