# branch private repo for fastlane match
git_url("**************:BranchMetrics/ios-signing-certificates.git")

storage_mode("git")

type("development") # The default type, can be: appstore, adhoc, enterprise or development

app_identifier(["io.branch.sdk.Branch-TestBed"])
username("<EMAIL>")

# For all available options run `fastlane match --help`
# Remove the # in the beginning of the line to enable the other options

# The docs are available on https://docs.fastlane.tools/actions/match
