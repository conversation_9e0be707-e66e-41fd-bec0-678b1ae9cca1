{"alert_system": {"alert_modal": {"title": "Peringatan", "checkbox_label": "<PERSON>a telah memahami risikonya dan masih ingin melanjutkan", "got_it_btn": "<PERSON><PERSON><PERSON>", "alert_details": "Detail <PERSON>"}, "confirm_modal": {"title": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> tin<PERSON>i", "checkbox_label": "<PERSON>a telah memahami peringatan tersebut dan masih ingin melanjutkan", "review_alerts": "<PERSON><PERSON><PERSON> semua per<PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> tolak permintaan ini. <PERSON><PERSON>, aset <PERSON>a mungkin akan be<PERSON>.", "title_blockaid": "<PERSON><PERSON> <PERSON>a berpotensi hilang", "blockaid": {"message": "<PERSON><PERSON> permintaan ini di<PERSON>, <PERSON><PERSON> dapat keh<PERSON>n aset milik <PERSON>. Sebaiknya batalkan permintaan ini.", "message1": "<PERSON><PERSON> permin<PERSON> ini <PERSON>, <PERSON><PERSON> mengi<PERSON>kan penipu untuk menarik dan menggunakan aset Anda. Anda tidak akan mendapatkannya kembali.", "message2": "<PERSON><PERSON> aset milik Anda ke penipu. <PERSON><PERSON>, <PERSON><PERSON> akan kehilangan aset tersebut.", "message3": "<PERSON><PERSON>, semua aset yang <PERSON>a daft<PERSON>an di OpenSea bisa berpotensi hilang.", "message4": "<PERSON><PERSON>, semua aset yang <PERSON>a daft<PERSON> di Blur bisa berpotensi hilang.", "message5": "<PERSON>a be<PERSON>i dengan situs berbahaya. <PERSON><PERSON>, <PERSON><PERSON> akan kehilangan aset milik <PERSON>."}}, "inline_alert_label": "Peringatan", "review_alerts": "<PERSON><PERSON><PERSON>", "review_alert": "<PERSON><PERSON><PERSON>", "upgrade_account": {"title": "<PERSON><PERSON><PERSON><PERSON> aku<PERSON>", "message": "Anda memperbarui akun ke akun cerdas. Anda akan tetap menggunakan alamat akun yang sama sambil menikmati transaksi yang lebih cepat dan biaya jaringan yang lebih rendah.", "learn_more": "Selengkapnya"}, "domain_mismatch": {"title": "Permintaan masuk yang men<PERSON>akan", "message": "Situs yang mengajukan permintaan tersebut bukanlah situs yang Anda masuki. Ini mungkin merupakan upaya untuk mencuri kredensial masuk Anda."}, "insufficient_balance": {"title": "<PERSON> tidak cukup", "message": "%{nativeCurrency} di akun Anda tidak cukup untuk membayar biaya jaringan.", "buy_action": "Beli %{nativeCurrency}"}, "signed_or_submitted": {"title": "Transaction in progress", "message": "Transaksi sebelumnya masih dalam proses penanda<PERSON><PERSON>an atau pengiriman."}, "signed_or_submitted_perps_deposit": {"title": "Deposit in progress", "message": "You already have a deposit in progress. You'll need to wait for it to go through before depositing again."}, "pending_transaction": {"title": "Transaksi tertunda", "message": "Transaksi ini tidak akan berjalan sampai transaksi sebelumnya selesai.", "learn_more": "<PERSON><PERSON><PERSON><PERSON> cara membatalkan atau mempercepat transaksi."}, "batched_unused_approvals": {"title": "<PERSON><PERSON> yang tidak diperlukan", "message": "Anda memberikan izin kepada orang lain untuk menarik token, meskipun itu tidak diperlukan untuk transaksi ini."}, "perps_deposit_minimum": {"message": "Minimal $10"}, "insufficient_pay_token_balance": {"message": "<PERSON> tidak cukup"}, "insufficient_pay_token_balance_fees": {"message": "Insufficient {{symbol}} to cover fees. Add less funds or select a different token to continue.", "title": "Insufficient funds"}, "insufficient_pay_token_native": {"message": "Insufficient {{ticker}} to cover fees. Select a token on a different network to continue."}, "no_pay_token_quotes": {"message": "Rute pembayaran ini tidak tersedia untuk saat ini. <PERSON><PERSON> ubah jumlah, jar<PERSON><PERSON>, atau <PERSON>, dan kami akan mencari opsi terbaik."}, "perps_hardware_account": {"title": "<PERSON><PERSON> not supported", "message": "Perps doesn't support hardware wallets.\nSwitch accounts to continue funding."}}, "blockaid_banner": {"approval_farming_description": "Jika Anda menyetujui permintaan ini, pihak ketiga yang terdeteksi melakukan penipuan dapat mengambil semua aset <PERSON>a.", "blur_farming_description": "Jika Anda menyetujui permintaan ini, seseorang dapat mencuri aset Anda yang terdaftar di Blur.", "deceptive_request_title": "Ini adalah permintaan tipuan", "failed_title": "<PERSON><PERSON><PERSON><PERSON> mungkin tidak aman", "failed_description": "<PERSON><PERSON>, permintaan ini tidak diverifikasi oleh penyedia kea<PERSON>n. Lanjutkan dengan hati-hati.", "malicious_domain_description": "Anda berinteraksi dengan domain berbahaya. Jika Anda menyetujui permintaan ini, aset <PERSON>a kemung<PERSON>an akan hilang.", "other_description": "<PERSON><PERSON> Anda menyetujui permintaan ini, aset <PERSON>a kem<PERSON>an akan hilang.", "raw_signature_farming_description": "<PERSON><PERSON> Anda menyetujui permintaan ini, aset <PERSON>a kem<PERSON>an akan hilang.", "seaport_farming_description": "Jika Anda menyetujui permintaan ini, sese<PERSON>g dapat mencuri aset Anda yang terdaftar di OpenSea.", "see_details": "Lihat <PERSON>", "does_not_look_right": "Ada yang tidak beres?", "report_an_issue": "Laporkan masalah", "suspicious_request_title": "Ini adalah permintaan yang mencurigakan", "trade_order_farming_description": "<PERSON><PERSON> Anda menyetujui permintaan ini, aset <PERSON>a kem<PERSON>an akan hilang.", "transfer_farming_description": "Jika Anda menyetujui permintaan ini, pihak ketiga yang terdeteksi melakukan penipuan akan mengambil semua aset <PERSON>.", "before_you_proceed": "Sebelum melanjutkan", "enable_blockaid_alerts": "Untuk mengaktifkan fitur ini, kami perlu mengatur peringatan keamanan. Halaman ini harus tetap terbuka selagi diatur. Diperlukan waktu kurang dari satu menit untuk menyelesaikannya.", "enable_blockaid_alerts_description": "Halaman ini harus tetap terbuka selagi mengatur peringatan keamanan. Butuh waktu kurang dari satu menit untuk menyelesaikan proses ini.", "setting_up_alerts": "<PERSON><PERSON><PERSON> per<PERSON> keamanan", "setting_up_alerts_description": "<PERSON><PERSON> akan mengatur peringatan keamanan secepat mungkin. <PERSON><PERSON><PERSON><PERSON> se<PERSON>, kami hampir se<PERSON>.", "setup_complete": "Pengat<PERSON><PERSON> se<PERSON>ai", "setup_failed": "Kami tidak dapat menyelesaikan pengaturan peringatan keamanan. Periksa koneksi internet dan coba lagi.", "setup_multiple_failures": "Koneksi internet saat ini tidak stabil, jadi kami tidak dapat mengatur peringatan keamanan. Coba lagi saat koneksi sudah pulih.", "setup_complete_description": "<PERSON>inga<PERSON> keamanan siap digunakan. Anda dapat menutup halaman ini dan mulai menjelajahi dompet", "continue": "Lanjutkan", "cancel": "<PERSON><PERSON>", "got_it": "<PERSON><PERSON><PERSON>", "try_again": "<PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>"}, "date": {"months": {"0": "Jan", "1": "Feb", "2": "Mar", "3": "Apr", "4": "<PERSON>", "5": "Jun", "6": "Jul", "7": "<PERSON><PERSON>", "8": "Sep", "9": "Okt", "10": "Nov", "11": "Des"}, "connector": "pada"}, "autocomplete": {"placeholder": "<PERSON>i be<PERSON> token, situs, atau alamat", "recents": "Terbaru", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "sites": "Situs", "tokens": "Token"}, "navigation": {"back": "Kembali", "close": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "info": "Informasi", "ok": "<PERSON>e"}, "onboarding": {"title": "Ayo mulai!", "sync_desc": "Ji<PERSON> Anda sudah memiliki ekstensi MetaMask atau dompet lain, sinkronkan atau impor untuk mengelola aset yang ada.", "create_desc": "Atur dompet pertama Anda dan mulailah men<PERSON>jahi aplikasi terdesentralisasi.", "import": "Impor dompet yang ada atau buat yang baru", "import_wallet_button": "Sinkronkan atau impor dompet", "new_to_crypto": "Baru mengenal kripto?", "start_exploring_now": "Buat dompet baru", "unlock": "<PERSON><PERSON>", "new_to_metamask": "Baru mengenal MetaMask?", "already_have_wallet": "Sudah memiliki dompet?", "optin_back_title": "Perhatian!", "optin_back_desc": "Harap setujui atau tidak setuju dengan penggunaan analitis data. Anda juga dapat memperbarui opsi ini di pengaturan.", "warning_title": "Peringatan", "warning_text_1": "Dompet dan akun Anda saat ini akan", "warning_text_2": "di<PERSON><PERSON>", "warning_text_3": "jika tetap melanju<PERSON>.", "warning_text_4": "Anda HANYA dapat memulihkannya dengan Frasa Pemulihan Rahasia dompet. MetaMask tidak dapat membantu Anda memulihkannya.", "warning_proceed": "<PERSON><PERSON> dompet dan lan<PERSON>kan", "warning_cancel": "<PERSON><PERSON>", "step1": "Pengaturan dompet", "step2": "<PERSON>uat kata sandi", "step3": "Amankan do<PERSON>et", "already_have": "Sudah memiliki dompet?", "sync_existing": "Sinkronkan dompet MetaMask yang ada dari ekstensi browser atau impor secara manual.", "scan_title": "Langkah-langkah untuk menyinkronkan dengan ekstensi MetaMask", "scan": "Pindai", "scan_step_1": "Buka ekstensi di desktop", "scan_step_2": "B<PERSON> Pengaturan > Lanjutan", "scan_step_3": "Klik “Sinkronkan dengan Ponsel”", "scan_step_4": "Pindai kode QR untuk mulai menyinkronkan", "success": "<PERSON><PERSON><PERSON><PERSON>", "continue_with_google": "Lanjutkan dengan Google", "continue_with_apple": "Lanjutkan den<PERSON>", "or": "atau", "import_existing_wallet": "I<PERSON>r dompet yang ada", "bottom_sheet_title": "<PERSON><PERSON>h opsi untuk melanjutkan", "continue_with_srp": "<PERSON><PERSON><PERSON>", "import_srp": "<PERSON><PERSON><PERSON> Frasa Pemulihan <PERSON>", "sign_in_with_google": "Ma<PERSON>k <PERSON>", "sign_in_with_apple": "<PERSON><PERSON><PERSON>", "your_wallet": "Anda berhasil mereset dompet!", "delete_current": "Reset dompet saat ini", "have_existing_wallet": "<PERSON>a sudah memiliki dompet", "import_using_srp": "<PERSON><PERSON><PERSON> Frasa Pemulihan <PERSON>", "import_using_srp_social_login": "<PERSON>a sudah memiliki dompet", "by_continuing": "By continuing, you agree to MetaMask's", "terms_of_use": "<PERSON><PERSON><PERSON><PERSON> pengg<PERSON>an", "privacy_notice": "Privacy notice", "and": "dan"}, "onboarding_success": {"title": "<PERSON><PERSON> agar <PERSON>asa P<PERSON>ulihan <PERSON> tetap aman!", "description": "Frasa Pemulihan <PERSON> ini dapat membantu Anda mendapatkan kembali akses jika Anda lupa kata sandi atau kehilangan akses login.", "description_bold": "<PERSON><PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON> dan <PERSON>.", "description_continued": "<PERSON>a dapat menyimpan frasa ini dengan aman se<PERSON>ga Anda tidak akan kehilangan akses terhadap uang.", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "learn_how": "<PERSON><PERSON><PERSON><PERSON>a", "leave_hint": "Tinggalkan petunjuk?", "default_settings": "Pengaturan default", "manage_default_settings": "Ke<PERSON>la pengaturan default", "default_settings_footer": "Pengaturan dioptimalkan untuk kemudahan penggunaan dan keamanan.\nUbah pengaturan ini setiap saat.", "no_srp_title": "Pengingat diatur!", "no_srp_description": "Dana akan hilang jika Anda terkunci dari aplikasi atau menggunakan perangkat baru. Pastikan untuk mencadangkan Frasa Pemulihan Rahasia di", "import_title": "Dompet Anda sudah siap!", "import_description": "<PERSON><PERSON> kehilangan <PERSON>, <PERSON><PERSON> tidak dapat menggunakan dompet.", "import_description2": "<PERSON>a dapat menyimpan frasa ini dengan aman se<PERSON>ga Anda tidak akan kehilangan akses terhadap uang.", "import_description_social_login": "Anda dapat masuk ke dompet setiap saat dengan akun dan kata sandi {{authConnection}} Anda.", "import_description_social_login_2": "<PERSON><PERSON> lupa kata sandi, <PERSON><PERSON> tidak dapat mengakses dompet.", "done": "Se<PERSON><PERSON>", "create_hint": "<PERSON>uat petunjuk kata sandi", "hint_title": "Petunjuk kata sandi", "hint_description": "Berikan petunjuk untuk membantu Anda mengingat kata sandi. Petunjuk ini disimpan di perangkat dan tidak akan dibagikan.", "hint_description2": "Ingat: <PERSON><PERSON> kehilangan kata sandi, <PERSON><PERSON> tidak dapat menggunakan dompet.", "hint_button": "<PERSON><PERSON><PERSON>", "hint_placeholder": "mis. rumah ibu", "hint_saved": "Simpan", "hint_saved_toast": "Petunjuk kata sandi disimpan", "remind_later": "<PERSON><PERSON> akan men<PERSON>kan <PERSON>a nanti", "remind_later_description": "Jika tidak mencadangkan Frasa P<PERSON>, <PERSON><PERSON> akan kehilangan akses ke dana apabila terkunci dari aplikasi atau mendapatkan perangkat baru.", "remind_later_description2": "<PERSON>a dapat mencadangkan dompet atau melihat Frasa Pemulihan <PERSON> di", "setting_security_privacy": "Pengaturan > <PERSON><PERSON><PERSON> dan <PERSON>"}, "onboarding_carousel": {"title1": "Selamat datang di MetaMask", "title2": "Kelola aset digital Anda", "title3": "Gerbang menuju web3", "subtitle1": "MetaMask merupakan dompet aman yang dipercaya oleh jutaan orang yang membuat dunia web3 dapat diakses oleh semua orang.", "subtitle2": "<PERSON><PERSON><PERSON>, gun<PERSON><PERSON>, dan kirim aset digital seperti token, Ethereum, koleksi unik.", "subtitle3": "<PERSON><PERSON><PERSON> melalui MetaMask dan lakukan transaksi untuk berinvestasi, men<PERSON><PERSON><PERSON><PERSON>, bermain game, menjual, dan banyak lagi!", "get_started": "<PERSON><PERSON>"}, "onboarding_wizard": {"skip_tutorial": "<PERSON><PERSON>", "coachmark": {"action_back": "Tidak, terima kasih", "action_next": "<PERSON><PERSON><PERSON> tur", "progress_back": "Kembali", "progress_next": "<PERSON><PERSON><PERSON>!"}, "step1": {"title": "Selamat datang di dompet baru Anda!", "content1": "Untuk mengg<PERSON>kan blockchain, <PERSON><PERSON> memerlukan dompet! Beberapa tindakan memerlukan Ether (ETH).", "content2": "<PERSON><PERSON> akan menu<PERSON>n cara membeli ETH, atau Anda dapat memintanya dari teman."}, "step2": {"title": "<PERSON><PERSON><PERSON>", "content1": "Ini merupakan akun per<PERSON>a <PERSON>, nilai total, dan alamat publiknya yang unik (0x...).", "content2": "Anda dapat membuat beberapa akun dalam dompet ini dengan mengetuk ikon profil."}, "step3": {"title": "<PERSON> <PERSON>", "content1": "Cobalah untuk membuat nama akun yang unik dan mudah diingat.", "content2": "Ketuk lama", "content3": "sekarang saatnya mengedit nama akun."}, "step4": {"title": "<PERSON><PERSON>", "content1": "<PERSON>a dapat mengakses riwayat Transaksi, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON> dari menu ini.", "content2": "<PERSON>a dapat melakukan berbagai tindakan dengan akun <PERSON> & mengakses pengaturan MetaMask."}, "step5": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content1": "Anda dapat menjelajahi web3 menggunakan browser"}, "step6": {"title": "<PERSON><PERSON>", "content": "Telusuri situs, atau ketikkan URL jika Anda mengetahui tujuan <PERSON>."}}, "onboarding_wizard_new": {"coachmark": {"action_back": "Tidak, terima kasih", "action_next": "<PERSON><PERSON><PERSON> tur", "progress_next": "<PERSON><PERSON><PERSON>"}, "step1": {"title": "Selamat datang di dompet Anda!", "content1": "<PERSON>chain, <PERSON><PERSON> dompet (dan mungkin beberapa ETH). <PERSON><PERSON>, mari kita lihat cara menggunakan dompet MetaMask."}, "step2": {"title": "<PERSON><PERSON><PERSON>", "content1": "Ini adalah akun dan alamat publik unik (0x...) Anda. <PERSON>uat lebih banyak akun dengan mengetuk ikon panah."}, "step3": {"title": "<PERSON><PERSON><PERSON> akun <PERSON>", "content1": "Edit na<PERSON>, lihat transaksi di Etherscan, bagikan kunci publik, dan lihat kunci pribadi dengan mengetuk ikon ini"}, "step4": {"title": "Notif<PERSON><PERSON>", "content1": "<PERSON><PERSON>, kiri<PERSON>, tuka<PERSON>, dan terima aset dengan mengetuk ikon ini"}, "step5": {"title": "Menjelajahi web3", "content1": "Buka browser dengan mengetuk ikon ini"}, "step6": {"title": "Menggunakan browser", "content1": "Telusuri situs dengan kata kunci atau masukkan URL. Selamat bersenang-senang! "}, "step7": {"title": "Menggunakan browser", "content1": "Telusuri situs dengan kata kunci atau masukkan URL. Selamat bersenang-senang! "}}, "create_wallet": {"title": "Membuat dompet Anda...", "subtitle": "Tidak perlu menu<PERSON>gu lama"}, "import_wallet": {"title": "Sudah menjadi pengguna MetaMask?", "sub_title": "Sinkronkan dengan ekstensi", "sync_help": "Sinkronkan dompet Anda dengan e<PERSON>i", "sync_help_step_one": "1. <PERSON><PERSON>", "sync_help_step_two": "2. <PERSON><PERSON> > Lanjutan", "sync_help_step_three": "3. <PERSON><PERSON> \"Sinkronkan dengan Ponsel\"", "sync_help_step_four": "4. <PERSON>ndai Kode QR untuk mulai menyinkronkan", "sync_from_browser_extension_button": "Sinkronkan dengan ekstensi MetaMask", "or": "ATAU", "import_from_seed_button": "<PERSON><PERSON><PERSON> Frasa Pemulihan <PERSON>"}, "login": {"title": "Selamat Datang kembali!", "password": "<PERSON>a sandi", "password_placeholder": "<PERSON><PERSON><PERSON><PERSON> kata sandi <PERSON>", "unlock_button": "<PERSON><PERSON>", "go_back": "Dompet tidak bisa dibuka? Anda dapat MENGHAPUS dompet saat ini dan mengatur yang baru", "forgot_password": "Lupa kata sandi?", "cant_proceed": "<PERSON>dakan ini tidak dapat dilanjutkan sampai Anda mengetik kata 'Hapus'. <PERSON><PERSON> ini, <PERSON><PERSON> memilih untuk menghapus dompet saat ini.", "invalid_password": "<PERSON>a sandi salah, coba lagi.", "type_delete": "Ketik ‘%{erase}’ untuk mengonfirmasi", "erase_my": "<PERSON>, reset dompet", "cancel": "Batalkan", "are_you_sure": "Anda yakin?", "your_current_wallet": "<PERSON><PERSON>, akun, dan aset <PERSON> akan", "removed_from": " di<PERSON><PERSON> secara permanen dari aplikasi ini", "this_action": "Tindakan ini tidak dapat dibatalkan.", "you_can_only": "Anda hanya bisa mendapatkan kembali dompet ini dengan informasi yang digunakan saat membuatnya, se<PERSON>i Frasa P<PERSON>, akun Google, atau akun Apple. MetaMask tidak memiliki informasi ini.", "recovery_phrase": "<PERSON><PERSON>", "metamask_does_not": "MetaMask tidak menyimpan <PERSON>.", "i_understand": "<PERSON><PERSON>, la<PERSON><PERSON><PERSON><PERSON>", "passcode_not_set_error": "Kesalahan: <PERSON><PERSON> sandi belum diatur.", "wrong_password_error": "Kesalahan: <PERSON><PERSON><PERSON><PERSON> gagal", "wrong_password_error_android": "Kesalahan: error:1e000065:Cipher functions:OPENSSL_internal:BAD_DECRYPT", "vault_error": "Kesalahan: Tidak dapat dibuka tanpa vault sebelumnya.", "clean_vault_error": "MetaMask mengalami kesalahan karena batas penyimpanan telah tercapai. Data lokal telah rusak. Instal ulang MetaMask dan pulihkan dengan Frasa Pemulihan Rahasia.", "seedless_password_outdated": "Kata sandi Anda baru saja diubah.", "seedless_password_outdated_modal_title": "Kata sandi Anda telah diubah", "seedless_password_outdated_modal_content": "Kata sandi Anda baru saja diubah, jadi <PERSON>a perlu memasukkan kata sandi baru untuk tetap masuk ke MetaMask.", "seedless_password_outdated_modal_confirm": "Lanjutkan", "no_internet_connection": "Seedless account recovery requires internet connection.", "seedless_controller_error_prompt_title": "<PERSON><PERSON><PERSON><PERSON>", "seedless_controller_error_prompt_description": "An issue occurred while unlocking. Re-login with Google and your MetaMask password.", "seedless_controller_error_prompt_primary_button_label": "<PERSON><PERSON><PERSON>", "security_alert_title": "<PERSON><PERSON><PERSON>", "security_alert_desc": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus mengaktifkan Kode Sandi atau metode autentikasi biometrik yang didukung di per<PERSON>kat Anda (ID Wajah, ID Sentuh, atau Sidik Jari)", "too_many_attempts": "Terlalu banyak percobaan. Coba lagi dalam {{remainingTime}}", "apple_button": "<PERSON><PERSON><PERSON>", "google_button": "Ma<PERSON>k <PERSON>", "other_methods": "<PERSON>akan metode login yang berbeda", "forgot_password_desc": "Lupa kata sandi Anda?", "forgot_password_desc_2": "MetaMask tidak dapat memulihkan kata sandi Anda.", "forgot_password_point_1": "Ji<PERSON> Anda masuk ke MetaMask pada perangkat dengan", "forgot_password_point_1_bold": "biometrik diaktifkan", "forgot_password_point_1_1": "(seperti ID Wajah), <PERSON><PERSON> dapat mereset kata sandi di sana.", "forgot_password_point_2": "<PERSON><PERSON> And<PERSON> memiliki", "forgot_password_point_2_bold": "domp<PERSON>,", "forgot_password_point_2_1": "Anda dapat mereset dompet saat ini dan mengimpor ulang menggunakan Frasa Pemulihan Rahasia.", "reset_wallet": "Reset dompet", "reset_wallet_desc": "Data dompet <PERSON><PERSON> akan", "reset_wallet_desc_bold": "di<PERSON>pus secara permanen", "reset_wallet_desc_2": "dari <PERSON> di perangkat ini. Tindakan ini tidak dapat dibatalkan.", "reset_wallet_desc_login": "Untuk memuli<PERSON> do<PERSON>, <PERSON><PERSON> dapat mengg<PERSON>kan Frasa Pemuli<PERSON>, atau kata sandi akun Google atau Apple. MetaMask tidak memiliki informasi ini.", "reset_wallet_desc_srp": "Untuk memuli<PERSON> domp<PERSON>, pastikan Anda memiliki Frasa Pemulihan Rahasia. MetaMask tidak memiliki informasi ini."}, "connect_hardware": {"title_select_hardware": "Hubungkan dompet perangkat keras", "select_hardware": "<PERSON><PERSON>h dompet perangkat keras yang ingin digunakan"}, "enter_password": {"title": "<PERSON><PERSON><PERSON><PERSON> kata sandi <PERSON>a", "desc": "<PERSON><PERSON><PERSON>n kata sandi Anda untuk melanjutkan", "password": "<PERSON>a sandi", "confirm_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>"}, "choose_password": {"title": "<PERSON>a sandi <PERSON>", "description": "Buka MetaMask hanya pada perangkat ini.", "description_social_login": "Gunakan ini untuk pemulihan dompet di semua perangkat.", "description_social_login_update": "If you lose this password, you’ll lose access to your wallet on all devices. Store it somewhere safe,", "description_social_login_update_bold": "MetaMask can't reset it.", "subtitle": "Kata sandi ini akan membuka dompet MetaMask Anda hanya pada perangkat ini.", "password": "Create password", "confirm_password": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> kata sandi", "create_button": "<PERSON>uat kata sandi", "import_with_seed_phrase": "<PERSON><PERSON><PERSON>", "password_length_error": "Kata sandi harus memuat minimal 8 karakter", "password_dont_match": "Kata sandi tidak cocok", "password_strength": "Kekuatan kata sandi:", "strength_weak": "Lemah", "strength_good": "Bagus", "strength_strong": "Ku<PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "seed_phrase": "<PERSON><PERSON>", "must_be_at_least": "Minimal berisi {{number}} karakter", "remember_me": "<PERSON>gat saya", "security_alert_title": "<PERSON><PERSON><PERSON>", "security_alert_message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus mengaktifkan Kode Sandi atau metode autentikasi biometrik yang didukung di per<PERSON>kat Anda (ID Wajah, ID Sentuh, atau Sidik Jari)", "i_understand": "<PERSON>a memahami bahwa MetaMask tidak dapat memulihkan kata sandi ini untuk saya.", "learn_more": "Pelajari se<PERSON>g<PERSON>.", "secure": "Amankan do<PERSON>et", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disable_biometric_error": "Anda telah menonaktifkan biometrik untuk aplikasi ini. <PERSON><PERSON><PERSON> pengaturan Ingatkan Saya dan coba lagi.", "create_password_cta": "<PERSON>uat kata sandi", "steps": "Lang<PERSON>h {{currentStep}} dari {{totalSteps}}", "password_error": "Passwords don’t match.", "marketing_opt_in_description": "Get product updates, tips, and news including by email. We may use your interactions to improve what we share.", "loose_password_description": "If I lose this password, MetaMask can’t reset it."}, "reset_password": {"title": "Ubah kata sandi", "subtitle": "Kata sandi ini akan membuka dompet MetaMask Anda hanya pada perangkat ini.", "password": "Kata sandi baru", "confirm_password": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> kata sandi", "reset_button": "Reset kata sandi", "import_with_seed_phrase": "<PERSON><PERSON><PERSON>", "password_length_error": "Kata sandi harus memuat minimal 8 karakter", "password_dont_match": "Kata sandi tidak cocok", "password_strength": "Kekuatan kata sandi:", "strength_weak": "Lemah", "strength_good": "Bagus", "strength_strong": "Ku<PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "seed_phrase": "<PERSON><PERSON>", "must_be_at_least": "Minimal berisi {{number}} karakter", "remember_me": "Ingatkan saya", "security_alert_title": "<PERSON><PERSON><PERSON>", "security_alert_message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> harus mengaktifkan Kode Sandi atau metode autentikasi biometrik yang didukung di per<PERSON>kat Anda (ID Wajah, ID Sentuh, atau Sidik Jari)", "i_understand": "<PERSON>a memahami bahwa MetaMask tidak dapat memulihkan kata sandi ini untuk saya.", "learn_more": "Pelajari se<PERSON>g<PERSON>.", "secure": "Amankan do<PERSON>et", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "password_updated": "Kata sandi baru tersimpan", "successfully_changed": "<PERSON>a sandi <PERSON>a be<PERSON><PERSON><PERSON>h", "new_password_placeholder": "Gunakan minimal 8 karakter", "confirm_password_placeholder": "<PERSON><PERSON><PERSON><PERSON> kembali kata sandi", "confirm_btn": "Simpan", "warning_password_change_title": "Anda yakin?", "warning_password_change_description": "Mengubah kata sandi di sini akan mengunci MetaMask di perangkat lain yang Anda gunakan. Anda perlu masuk lagi dengan kata sandi baru", "warning_password_change_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning_password_cancel_button": "Batalkan", "changing_password": "Mengubah kata sandi...", "changing_password_subtitle": "Tidak perlu menu<PERSON>gu lama", "checkbox_forgot_password": "Jika lupa kata sandi ini, saya akan kehilangan akses ke dompet secara permanen. MetaMask tidak dapat mereset kata sandi ini untuk saya.", "seedless_change_password_error_modal_title": "Change password failed", "seedless_change_password_error_modal_content": "We were unable to change your password. Please try again.", "seedless_change_password_error_modal_confirm": "Coba lagi"}, "import_from_seed": {"title": "Impor do<PERSON>", "seed_phrase_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "create_new_password": "Create password", "confirm_password": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> kata sandi", "import_button": "IMPOR", "cancel_button": "<PERSON><PERSON>", "password_length_error": "Kata sandi harus memuat minimal 8 karakter", "password_dont_match": "Kata sandi tidak cocok", "seed_phrase_requirements": "<PERSON><PERSON> 12, 15, 18, 21, atau 24 kata", "invalid_seed_phrase": "<PERSON><PERSON> tidak di<PERSON>.", "error": "<PERSON><PERSON><PERSON>", "invalid_qr_code_title": "Kode QR Tidak Valid", "invalid_qr_code_message": "Kode QR ini tidak mewakili Frasa Pemulihan <PERSON> yang valid", "enter_your_secret_recovery_phrase": "<PERSON><PERSON><PERSON><PERSON>", "metamask_password": "<PERSON>a sandi <PERSON>", "metamask_password_description": "Buka MetaMask hanya pada perangkat ini.", "seed_phrase_required": "<PERSON><PERSON> waji<PERSON>", "re_enter_password": "<PERSON><PERSON><PERSON><PERSON> kembali kata sandi", "use_at_least_8_characters": "Gunakan minimal 8 karakter", "unlock_with_face_id": "<PERSON><PERSON> dengan ID Wajah?", "learn_more": "<PERSON><PERSON> saya lupa kata sandi ini, MetaMask tidak dapat memulihkannya untuk saya.", "learn_more_social_login": "Jika lupa kata sandi ini, saya akan kehilangan akses ke dompet secara permanen. MetaMask tidak dapat mereset kata sandi ini untuk saya.", "seed_phrase_length_error": "<PERSON><PERSON> harus terdiri dari 12, 15, 18, 21, atau 24 kata", "continue": "Lanjutkan", "clear_all": "<PERSON><PERSON> semua", "show_all": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "paste": "Tempel", "hide_all": "Semb<PERSON><PERSON><PERSON> semua", "srp": "<PERSON><PERSON>", "srp_placeholder": "Tambahkan spasi di antara setiap kata dan pastikan tidak ada yang melihat.", "learn_more_link": "Selengkapnya", "import_create_password_cta": "Create password", "pass_flow": "Lanjutkan dengan <PERSON>", "steps": "Lang<PERSON>h {{currentStep}} dari {{totalSteps}}", "password_error": "Kata sandi tidak sesuai. Coba lagi.", "spellcheck_error": "<PERSON><PERSON><PERSON> hanya huru<PERSON> k<PERSON>, per<PERSON><PERSON><PERSON><PERSON>, dan susun kata-kata dalam urutan as<PERSON>ya.", "enter_strong_password": "<PERSON><PERSON>kkan kata sandi yang kuat"}, "bottom_tab_bar": {"dapps": "ÐApps", "wallet": "Dompet", "transfer": "Transfer"}, "bottom_nav": {"home": "Be<PERSON><PERSON>", "browser": "Browser", "activity": "Aktivitas", "trade": "Trade", "settings": "<PERSON><PERSON><PERSON><PERSON>", "rewards": "<PERSON><PERSON>"}, "drawer": {"send_button": "<PERSON><PERSON>", "receive_button": "<PERSON><PERSON><PERSON> dana", "coming_soon": "<PERSON><PERSON><PERSON> hadir...", "wallet": "Dompet", "transaction_activity": "Aktivitas", "request_feature": "<PERSON><PERSON>", "submit_feedback_message": "<PERSON><PERSON><PERSON> jenis umpan balik yang akan di<PERSON>.", "submit_bug": "<PERSON><PERSON><PERSON>", "submit_general_feedback": "<PERSON><PERSON>", "share_address": "Bagikan Alamat <PERSON> saya", "view_in_etherscan": "Lihat di Etherscan", "view_in": "<PERSON><PERSON>", "browser": "Browser", "settings": "<PERSON><PERSON><PERSON><PERSON>", "settings_warning": "Dompet tidak terlindungi", "settings_warning_short": "Tidak terlindung", "help": "Dukungan", "lock": "<PERSON><PERSON><PERSON>", "lock_title": "Yakin ingin mengunci dompet <PERSON>?", "lock_ok": "YA", "lock_cancel": "TIDAK", "feedback": "<PERSON><PERSON> balik", "metamask_support": "Dukungan MetaMask", "public_address": "<PERSON><PERSON><PERSON>"}, "send": {"available": "tersedia", "invalid_value": "<PERSON><PERSON> tidak valid", "insufficient_funds": "<PERSON> tidak cukup", "continue": "Lanjutkan", "unit": "unit", "units": "unit", "next": "Berikutnya", "title": "<PERSON><PERSON>", "deeplink_failure": "<PERSON><PERSON><PERSON><PERSON> kesalahan! Silakan coba lagi", "warn_network_change": "<PERSON><PERSON><PERSON> di<PERSON>h ke", "send_to": "<PERSON><PERSON> ke", "amount": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paste": "Tempel", "no_assets_available": "Aset untuk dikirim tidak tersedia", "clear": "Hapus", "to": "<PERSON>", "enter_address_to_send_to": "<PERSON><PERSON><PERSON><PERSON> alamat untuk di<PERSON>rim", "accounts": "<PERSON><PERSON><PERSON>", "contacts": "Kontak", "no_contacts_found": "Kontak tidak ditemukan", "review": "Tinjau", "all_networks": "<PERSON><PERSON><PERSON>", "no_tokens_match_filters": "Tidak ada token yang se<PERSON>ai dengan filter Anda", "clear_filters": "Hapus filter", "no_tokens_available": "Token tidak tersedia", "sign": "<PERSON>da tangan", "network_not_found_title": "<PERSON><PERSON><PERSON> tidak <PERSON>", "network_not_found_description": "Jaringan dengan id chain {{chain_id}} tidak ditemukan di dompet Anda. Tambahkan jaringan terlebih dulu.", "network_missing_id": "ID rantai tidak ada.", "search_tokens_and_nfts": "Cari token dan N<PERSON>", "tokens": "Token", "nfts": "NFT", "show_more_nfts": "Tampilkan NFT lainnya", "token_contract_warning": "Alamat ini merupakan alamat kontrak token. <PERSON><PERSON> mengiri<PERSON> token ke alamat ini, <PERSON><PERSON> akan kehilangan token tersebut.", "invisible_character_error": "<PERSON><PERSON> men<PERSON> karakter tak terlihat pada nama ENS. Periksa nama ENS untuk menghindari potensi penipuan.", "could_not_resolve_name": "Couldn't resolve name"}, "deposit": {"title": "<PERSON><PERSON><PERSON><PERSON>", "selectRegion": "<PERSON><PERSON><PERSON>", "chooseYourRegionToContinue": "<PERSON><PERSON><PERSON> wilayah Anda untuk melanjutkan", "buildQuote": {"payWith": "<PERSON><PERSON>", "unexpectedError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak terduga.", "quoteFetchError": "Gagal mengambil kuotasi.", "kycFormsFetchError": "Gagal mengambil formulir KYC.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "token_modal": {"select_a_token": "<PERSON><PERSON><PERSON>", "select_token": "<PERSON><PERSON><PERSON> token", "search_by_name_or_address": "Cari token berdasarkan nama atau alamat", "no_tokens_found": "Tidak ada token yang cocok dengan \"{{searchString}}\""}, "networks_filter_bar": {"all_networks": "<PERSON><PERSON><PERSON>"}, "networks_filter_selector": {"select_network": "<PERSON><PERSON><PERSON>", "select_all": "<PERSON><PERSON><PERSON> se<PERSON>a", "deselect_all": "<PERSON><PERSON> pilih semua", "apply": "Terapkan"}, "configuration_modal": {"title": "Opsi", "view_order_history": "Lihat riwayat order", "contact_support": "Hubungi dukungan", "log_out": "<PERSON><PERSON><PERSON>", "logged_out_success": "<PERSON><PERSON><PERSON><PERSON> kel<PERSON>", "error_sdk_not_initialized": "SDK tidak diinisialisasi", "logged_out_error": "Kesalahan saat keluar"}, "region_modal": {"select_a_region": "<PERSON><PERSON><PERSON> wilayah", "search_by_country": "<PERSON><PERSON> be<PERSON> negara", "no_regions_found": "Tidak ada wilayah yang cocok dengan \"{{searchString}}\""}, "state_modal": {"select_a_state": "Pilih state", "search_by_state": "<PERSON>i berda<PERSON> negara bagian", "no_state_results": "Tidak ada state yang cocok dengan \"{{searchString}}\""}, "payment_modal": {"select_a_payment_method": "<PERSON><PERSON><PERSON>"}, "payment_duration": {"instant": "Instan", "1_to_2_days": "1 sampai 2 hari"}, "unsupported_region_modal": {"title": "Wilayah tidak didukung", "location_prefix": "Sepertinya Anda berada di:", "description": "<PERSON>mi <PERSON>g berupaya keras untuk memperluas jangkauan ke wilayah Anda. Sementara itu, mungkin ada cara lain bagi Anda untuk mendapatkan kripto.", "change_region": "Ubah wilayah", "buy_crypto": "<PERSON><PERSON>"}, "unsupported_state_modal": {"title": "Wilayah tidak didukung", "location_prefix": "Anda telah memilih:", "description": "<PERSON>mi <PERSON>g berupaya keras untuk memperluas jangkauan ke wilayah Anda. Sementara itu, ada cara lain bagi Anda untuk mendapatkan kripto.", "change_state": "Ubah wilayah", "try_another_option": "Coba opsi lain"}, "incompatible_token_acount_modal": {"title": "G<PERSON> akun <PERSON>", "description": "Akun {{networkName}} yang Anda gunakan tidak mendukung token yang dipilih. Ganti akun atau token untuk melanjutkan.", "cta": "<PERSON><PERSON>"}, "ssn_info_modal": {"title": "<PERSON>asan kami bertanya", "description": "Undang-Undang A.S. mewajibkan Transak untuk memeriksa identitas Anda sebelum menukarkan dolar ke kripto. Artinya, pelanggan di A.S. perlu memberikan nomor Jaminan Sosial."}, "get_started": {"navbar_title": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> dengan mudah menggunakan USDC", "bullet_1_title": "<PERSON><PERSON> yang didukung oleh dolar <PERSON>", "bullet_1_description": "USDC merupakan dolar digital yang didukung oleh dolar AS, jadi <PERSON>a selalu tahu ni<PERSON>.", "bullet_2_title": "Volatilitas rendah", "bullet_2_description": "USDC dirancang untuk tetap stabil, menja<PERSON>kan<PERSON> semakin mudah untuk membuat rencana, men<PERSON>ung, dan berb<PERSON>.", "bullet_3_title": "Lebih cepat dari bank", "bullet_3_description": "<PERSON><PERSON> dan terima dalam hitungan menit, tanpa menunggu jam operasional bank atau penundaan internasional.", "button": "<PERSON><PERSON>"}, "enter_email": {"navbar_title": "Verifikasikan identitas Anda", "title": "Masukkan email Anda", "description": "<PERSON><PERSON> akan mengirimkan kode verifikasi enam digit melalui email untuk masuk atau membuat akun dengan aman di Transak.", "input_placeholder": "<EMAIL>", "submit_button": "<PERSON><PERSON> email", "loading": "Mengirim email...", "validation_error": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email yang valid", "error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengirim email"}, "otp_code": {"navbar_title": "Verifikasikan identitas Anda", "title": "<PERSON><PERSON><PERSON>n kode enam digit", "description": "<PERSON><PERSON><PERSON><PERSON> kode yang kami kirim ke {{email}}. <PERSON><PERSON> Anda tidak melihatnya, periksa folder spam.", "submit_button": "<PERSON><PERSON>", "loading": "Memverifikasi kode...", "invalid_code_error": "<PERSON><PERSON> tidak valid", "validation_error": "Masukkan kode yang valid", "need_help": "Butuh bantuan?", "resend_code_description": "Belum menerima kode?", "resend_code_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han kirim ulang kode.", "resend_code_button": "<PERSON><PERSON>", "resend_cooldown": "<PERSON><PERSON> ulang kode dalam {{seconds}} detik", "contact_support": "Hubungi dukungan", "error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat memverifikasi kode"}, "verify_identity": {"title": "Verifikasikan identitas Anda", "navbar_title": "<PERSON><PERSON><PERSON><PERSON>", "description_1": "Untuk mendepositkan uang tunai untuk pertama kalinya, identitas Anda harus diverifikasi.", "description_2_transak": "Transak", "description_2_rest": " akan memfasilitasi deposit dan data Anda akan dikirim langsung ke Transak. Kami tidak memproses data yang Anda bagikan.", "description_3_part1": "Lihat ", "description_3_privacy_policy": "kebijakan privasi", "description_3_part2": " untuk selengkapnya.", "agreement_text_part1": "<PERSON>gan mengeklik tombol di bawah ini, <PERSON><PERSON> ", "agreement_text_transak_terms": "Ketentuan <PERSON>an <PERSON>ak", "agreement_text_and": " dan ", "agreement_text_privacy_policy": "<PERSON><PERSON><PERSON><PERSON>", "agreement_text_part2": ".", "button": "<PERSON><PERSON><PERSON> dan lan<PERSON>kan"}, "additional_verification": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paragraph_1": "Untuk deposit yang lebih besar, Anda memerlukan identitas yang valid (seperti SIM) dan swafoto waktu nyata.", "paragraph_2": "Untuk menyelesaikan veri<PERSON>, <PERSON><PERSON> perlu mengaktifkan akses ke kamera.", "button": "Lanjutkan"}, "basic_info": {"navbar_title": "Verifikasikan identitas Anda", "title": "Masukkan info dasar Anda", "continue": "Lanjutkan", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kami memer<PERSON>an beberapa informasi dasar tentang Anda.", "first_name": "<PERSON><PERSON> de<PERSON>", "last_name": "<PERSON><PERSON>", "phone_number": "Nomor telepon", "date_of_birth": "<PERSON><PERSON> lahir", "social_security_number": "Nomor jaminan sosial (SSN)", "enter_phone_number": "Ma<PERSON>kkan nomor telepon", "select_region": "<PERSON><PERSON><PERSON> wilayah", "first_name_required": "<PERSON><PERSON> de<PERSON>an", "first_name_invalid": "<PERSON><PERSON><PERSON>n nama depan yang valid", "last_name_required": "<PERSON><PERSON> be<PERSON> dip<PERSON>lukan", "last_name_invalid": "Ma<PERSON>kkan nama belakang yang valid", "mobile_number_required": "Nomor telepon diperlukan", "mobile_number_invalid": "Masukkan nomor telepon yang valid", "dob_required": "<PERSON><PERSON> lahir diperlukan", "dob_invalid": "<PERSON><PERSON><PERSON><PERSON> tanggal lahir yang valid", "ssn_required": "Nomor jaminan sosial diperlukan", "unexpected_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak terduga. Coba lagi."}, "enter_address": {"navbar_title": "Verifikasikan identitas Anda", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> alamat permanen terbaru <PERSON>.", "continue": "Lanjutkan", "address_line_1": "Baris alamat 1", "address_line_2": "Baris alamat 2 (opsional)", "state": "Negara Bagian/Wilayah", "city": "Kota", "postal_code": "Kode Pos/Zip", "country": "Negara", "select_state": "Pilih state", "address_line_1_required": "Baris alamat 1 diperlukan", "address_line_1_invalid": "<PERSON><PERSON><PERSON><PERSON> alamat yang valid", "address_line_2_invalid": "<PERSON><PERSON><PERSON><PERSON> alamat yang valid", "city_required": "Kota diperlukan", "city_invalid": "Masukkan kota yang valid", "state_required": "Negara Bagian/Wilayah diperlukan", "state_invalid": "Masukkan negara bagian yang valid", "postal_code_required": "Kode Pos diperlukan", "postal_code_invalid": "Masukkan kode pos yang valid", "unexpected_error": "Kesalahan tak terduga."}, "privacy_section": {"transak": "Transak mengenkripsi dan menyimpan informasi ini.", "metamask": "MetaMask tidak pernah menerima atau menggunakan data Anda."}, "kyc_processing": {"navbar_title": "Verifikasikan identitas Anda", "heading": "<PERSON><PERSON> tunggu...", "description": "<PERSON>ya memakan waktu sekitar 2 menit.", "error_heading": "Kami tidak dapat memverifikasi identitas Anda.", "error_description": "Coba unggah dokumen Anda lagi dan kirim ulang untuk verifikasi.", "error_button": "Coba verifikasi lagi", "success_heading": "Anda sudah terveri<PERSON>i", "success_description": "Selesaikan deposit Anda sekarang.", "success_button": "Selesaikan order"}, "provider_webview": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "kyc_webview": {"title": "Memverifikasi identitas Anda", "webview_received_error": "WebView menerima kode status kesalahan: {{code}}"}, "order_processing": {"title": "Memproses deposit", "button": "<PERSON><PERSON><PERSON>", "description": "Pembelian kartu umumnya memerlukan waktu beberapa menit", "bank_transfer_description": "Transfer bank biasanya membutuhkan waktu beberapa hari kerja", "success_title": "<PERSON><PERSON><PERSON><PERSON>", "success_description": "Deposit sejumlah {{amount}} {{currency}} berhasil!", "error_title": "Deposit gagal", "error_description": "Kami tidak dapat menyelesaikan deposit", "cancel_order_description": "Anda meminta pembatalan deposit.", "error_button": "Coba lagi", "contact_support_button": "Hubungi dukungan", "account": "<PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON>", "order_id": "ID Order", "fees": "Biaya", "total": "Total", "view_order_details_in_transak": "Lihat detail order di Transak", "no_order_found": "Order tidak ditemukan", "back_to_wallet": "Kembali ke Dompet", "cancel_order_button": "Batalkan order"}, "order_details": {"title": "Order Deposit", "error_title": "Te<PERSON><PERSON><PERSON> kesalahan pada order deposit", "error_message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak terduga."}, "bank_details": {"navbar_title": "Transfer bank {{paymentMethod}}", "button": "Konfirmasikan transfer", "button_cancel": "Batalkan order", "main_title": "Untuk menyelesaikan order", "main_content_1": "Lakukan transfer dari bank pilihan menggunakan informasi di bawah ini.", "main_content_2": "<PERSON><PERSON><PERSON>, kembali dan klik “Konfirmasikan transfer” untuk mengonfirmasi.", "show_bank_info": "Tampilkan informasi bank", "hide_bank_info": "Sembunyikan informasi bank", "transfer_amount": "<PERSON><PERSON><PERSON> transfer", "account_holder_name": "<PERSON><PERSON> p<PERSON>g rekening", "beneficiary_name": "<PERSON><PERSON> penerima", "routing_number": "Nomor routing", "account_number": "Nomor rekening", "iban": "IBAN", "bic": "BIC", "account_type": "Tipe rekening", "bank_name": "Nama bank", "beneficiary_address": "<PERSON><PERSON><PERSON>", "bank_address": "Alamat bank", "info_banner_text": "Anda harus melihat '{{accountHolderName}}' sebagai pemegang rekening. Ini membantu bank memproses transfer.", "error_message": "Kami tidak dapat mengonfirmasi transfer bank Anda. Coba lagi.", "cancel_order_error": "Kami tidak dapat membatalkan order Anda. Coba lagi."}, "webview_modal": {"error": "Kesalahan penerimaan Webview: %{code}"}, "notifications": {"deposit_failed_title": "Deposit sejumlah {{currency}} gagal! Coba lagi. <PERSON>hon maaf atas ketidaknyamanan yang timbul!", "deposit_failed_description": "Verifikasikan metode pembayaran dan dukungan kartu", "deposit_cancelled_title": "<PERSON><PERSON><PERSON><PERSON>", "deposit_cancelled_description": "Anda meminta pembatalan deposit.", "deposit_completed_title": "Deposit sejumlah {{amount}} {{currency}} berhasil!", "deposit_completed_description": "{{currency}} kini tersedia di dompet Anda", "deposit_pending_title": "Memproses deposit {{currency}}", "deposit_pending_description": "<PERSON><PERSON> perlu beberapa menit..."}, "error_view": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memproses deposit. Hubungi dukungan jika masalah berl<PERSON>.", "try_again": "Coba lagi"}}, "perps": {"title": "Per<PERSON>", "perps_trading": "Perdagangan Perp", "perp_account_balance": "<PERSON>do akun perp", "manage_balance": "<PERSON><PERSON><PERSON>", "total_balance": "Total Saldo", "available_balance": "<PERSON><PERSON> tersedia", "margin_used": "<PERSON><PERSON> yang <PERSON>", "gtm_content": {"title": "PERPS ARE HERE", "title_description": "Long or short tokens with up to 40x leverage. Fund your account with any EVM token in one click.", "not_now": "Tidak se<PERSON>", "try_now": "Get started"}, "unrealized_pnl": "PnL Belum Terealisasi", "withdraw": "<PERSON><PERSON>", "refresh_balance": "Segarkan <PERSON>", "add_funds": "<PERSON><PERSON><PERSON> dana", "your_positions": "Posisi Anda", "loading_positions": "<PERSON><PERSON><PERSON> posisi...", "refreshing_positions": "Menyegarkan posisi...", "no_open_orders": "Tidak ada order terbuka", "deposit": {"title": "<PERSON><PERSON><PERSON> yang akan dideposit", "get_usdc_hyperliquid": "Dapatkan USDC • Hyperliquid", "insufficient_funds": "<PERSON> tidak cukup", "enter_amount": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "fetching_quote": "Mengambil kuotasi", "submitting": "Mengirimkan transaksi", "get_usdc": "Dapatkan USDC", "network_fee": "<PERSON><PERSON><PERSON>", "estimated_time": "Estimasi waktu", "rate": "Terbaik", "slippage": "<PERSON><PERSON>", "slippage_info": "Jika harga berubah antara waktu penempatan dan konfirmasi order Anda, ini disebut selip. Transaksi akan otomatis dibatalkan jika selip melebihi toleransi yang Anda tetapkan di sini.", "slippage_auto": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Terapkan", "max_button": "<PERSON><PERSON>", "done_button": "Se<PERSON><PERSON>", "metamask_fee": "Biaya MetaMask", "metamask_fee_tooltip": "MetaMask tidak mengenakan biaya apa pun untuk deposit ke Hyperliquid", "minimum_deposit_error": "Ju<PERSON><PERSON> deposit minimum adalah {{amount}} USDC", "processing_title": "Memproses deposit", "preview": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "processing": {"title": "Memproses Deposit"}, "deposit_completed": "Deposit berhasil diselesaikan!", "deposit_failed": "<PERSON><PERSON><PERSON><PERSON>", "retry_deposit": "Coba Deposit Ulang", "go_back": "Kembali", "view_balance": "<PERSON><PERSON>", "calculating_fee": "Menghitung...", "quote_expired_modal": {"title": "<PERSON><PERSON><PERSON>", "description": "Kuotasi deposit Anda telah kedaluwarsa setelah {{refreshRate}} detik. Dapatkan kuotasi baru untuk melanjutkan.", "get_new_quote": "Dapatkan Ku<PERSON> Bar<PERSON>"}, "steps": {"preparing": "Mempersiapkan deposit...", "swapping": "Menukar {{token}} ke USDC", "bridging": "Bridge ke Hyperliquid", "depositing": "Mendeposit ke akun perp", "depositing_direct": "Mentransfer USDC langsung ke akun HyperLiquid Anda..."}, "step_descriptions": {"preparing": "Mempersiapkan transaksi deposit Anda...", "swapping": "Mengonversi token Anda ke USDC untuk deposit...", "bridging": "Memindahkan USDC ke jaringan Arbitrum...", "depositing": "Mentransfer USDC ke akun HyperLiquid Anda...", "success": "Berhasil mendeposit {{amount}} USDC ke akun HyperLiquid Anda", "error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han selama proses deposit. Coba lagi."}, "quote_fetch_error": "Gagal mengambil kuotasi deposit. Coba lagi.", "bridge_quote_timeout": "Tidak dapat mengambil kuotasi bridge. Coba lagi atau pilih token lain.", "no_quotes_available": "Rute untuk swap ini tidak tersedia. Coba token atau jumlah yang berbeda.", "success": {"title": "Deposit Berhasil!", "description": "USDC telah berhasil didepositkan ke akun perdagangan HyperLiquid Anda", "amount": "<PERSON><PERSON><PERSON>", "processing_time": "<PERSON><PERSON><PERSON>", "status": "Status", "completed": "Se<PERSON><PERSON>", "view_balance": "<PERSON><PERSON>", "view_transaction": "Li<PERSON>"}, "success_toast": "Your Perps account was funded", "success_message": "{{amount}} available to trade", "funds_are_ready_to_trade": "Funds are ready to trade", "error_toast": "Transaction failed", "error_generic": "Funds have been returned to you", "in_progress": "Adding funds to Perps", "estimated_processing_time": "Est. {{time}}", "funds_available_momentarily": "Funds will be available momentarily", "your_funds_are_available_to_trade": "Your funds are available to trade", "track": "Track"}, "withdrawal": {"title": "<PERSON><PERSON>", "insufficient_funds": "<PERSON> tidak cukup", "enter_amount": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "review": "<PERSON><PERSON><PERSON> penarikan", "withdraw": "<PERSON><PERSON>", "withdraw_usdc": "Tarik USDC", "minimum_amount_error": "<PERSON><PERSON><PERSON> penarikan minimum adalah ${{amount}}", "amount_too_low": "<PERSON><PERSON><PERSON> harus lebih besar dari ${{minAmount}} untuk menutupi biaya", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processing_title": "<PERSON><PERSON><PERSON> dimulai", "eta_will_be_shared_shortly": "ETA will be shared shortly", "success_title": "<PERSON><PERSON><PERSON> be<PERSON>", "success_description": "USDC Anda yang berjumlah {{amount}} telah ditarik dari Hyperliquid", "network_fee": "<PERSON><PERSON><PERSON>", "metamask_fee": "Biaya MetaMask", "total_fees": "Total biaya", "receiving_amount": "<PERSON><PERSON> akan men<PERSON>", "estimated_time": "Estimasi waktu", "done": "Se<PERSON><PERSON>", "initiated": "<PERSON><PERSON><PERSON> dimulai", "wait_time_message": "<PERSON> akan tiba dalam waktu 5 menit", "submitting": "Mengirimkan...", "error": "<PERSON><PERSON><PERSON> gagal", "success_toast": "<PERSON><PERSON><PERSON> confirmed", "success_toast_description": "You'll receive {{amount}} {{symbol}} on {{networkName}} within 5 minutes", "bridge_info": "Validator HyperLiquid sedang memproses penarikan <PERSON>a", "error_generic": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat penarikan", "invalid_amount": "<PERSON><PERSON><PERSON><PERSON> jumlah yang valid", "max": "<PERSON><PERSON>", "percentage_10": "10%", "percentage_25": "25%", "available_balance": "Saldo perp yang tersedia: {{amount}}", "receive": "Terima", "provider_fee": "Biaya <PERSON>", "you_will_receive": "<PERSON><PERSON> akan men<PERSON>", "continue": "Lanjutkan", "funds_received": "{{amount}} {{symbol}} diterima"}, "quote": {"network_fee": "<PERSON><PERSON><PERSON>", "estimated_time": "Estimasi waktu", "rate": "Terbaik", "metamask_fee": "Biaya MetaMask", "metamask_fee_tooltip": "MetaMask tidak mengenakan biaya apa pun untuk transaksi Hyperliquid", "metamask_fee_tooltip_deposit": "MetaMask tidak mengenakan biaya apa pun untuk deposit ke Hyperliquid", "metamask_fee_tooltip_withdrawal": "MetaMask tidak mengenakan biaya apa pun untuk penarikan dari Hyperliquid"}, "order": {"title": "Order Baru", "leverage": "Leverage", "limit_price": "Harga limit", "enter_price": "<PERSON><PERSON> masuk", "trigger_price": "<PERSON><PERSON> pem<PERSON>u", "liquidation_price": "<PERSON><PERSON> lik<PERSON>", "fees": "Biaya", "market": "Pasar", "limit": "Limit", "open_orders": "Order", "max": "maks", "cancel_order": "Batalkan order", "filled": "terp<PERSON><PERSON>i", "reduce_only": "Kurangi saja", "yes": "Ya", "status": {"open": "<PERSON><PERSON>", "filled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "canceled": "Di<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "validation": {"failed": "Validasi order gagal", "amount_required": "Jumlah order harus lebih besar dari 0", "minimum_amount": "Ukuran order minimum adalah ${{amount}}", "insufficient_funds": "Insufficient funds", "insufficient_balance": "Saldo tidak mencukupi. Diperlukan: ${{required}}, Tersedia: ${{available}}", "invalid_leverage": "Leverage harus berada di antara {{min}}x dan {{max}}x", "high_leverage_warning": "Leverage yang tinggi meningkatkan risiko likuidasi", "invalid_take_profit": "Ambil profit harus {{direction}} harga saat ini untuk posisi {{positionType}}", "invalid_stop_loss": "Stop loss harus {{direction}} harga saat ini untuk posisi {{positionType}}", "liquidation_warning": "Posisi mendekati harga likuidasi", "limit_price_required": "Atur harga limit untuk order limit", "please_set_a_limit_price": "Please set a limit price", "limit_price_must_be_set_before_configuring_tpsl": "Limit price must be set before configuring TP/SL", "only_hyperliquid_usdc": "Saat ini hanya USDC di Hyperliquid yang didukung untuk pembayaran", "limit_price_far_warning": "Limit price is far from current market price"}, "error": {"placement_failed": "Pembuatan order gagal", "network_error": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak dikenal", "dismiss": "Lewatkan", "invalid_asset": "<PERSON><PERSON> tidak valid", "go_back": "Kembali", "asset_not_tradable": "{{asset}} bukan aset yang dapat diperdagangkan"}, "off": "<PERSON><PERSON><PERSON><PERSON>", "estimated_execution_time": "Estimasi waktu eksekusi", "one_to_three_seconds": "1 sampai 3 detik", "margin": "<PERSON><PERSON>", "take_profit": "Take profit", "stop_loss": "Stop loss", "tp_sl": "TP/SL", "button": {"long": "Panjang {{asset}}", "short": "Pendek {{asset}}"}, "tpsl_modal": {"title": "Take profit & Stop loss", "save": "Simpan", "current_price": "Harga saat ini: {{price}}", "on": "AKTIF", "off": "NONAKTIF", "take_profit_helper": "Tutup posisi saat harga mencapai level ini", "stop_loss_helper": "Batasi loss dengan menutup posisi pada harga ini"}, "limit_price_modal": {"title": "Atur harga limit", "set": "Atur", "market_price": "Harga pasar: {{price}}", "market": "Pasar", "current_price": "Harga saat ini", "ask_price": "<PERSON><PERSON> per<PERSON>", "bid_price": "<PERSON><PERSON>", "difference_from_market": "<PERSON><PERSON><PERSON> dari pasar:", "limit_price_above": "Limit price is above current price", "limit_price_below": "Limit price is below current price"}, "leverage_modal": {"title": "Leverage", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entry_price": "<PERSON><PERSON>", "current_price": "Harga saat ini", "liquidation_price": "<PERSON><PERSON>", "liquidation_distance": "<PERSON><PERSON>k <PERSON>"}, "type": {"title": "Jenis Order", "market": {"title": "Pasar", "description": "<PERSON>ks<PERSON><PERSON><PERSON> segera pada harga pasar saat ini"}, "limit": {"title": "Limit", "description": "<PERSON>ks<PERSON><PERSON>i hanya pada harga yang Anda tentukan atau lebih baik"}}, "success": {"title": "Order Berhasil Dibuat", "subtitle": "Po<PERSON>i {{direction}} <PERSON><PERSON> untuk {{asset}} telah dibuat", "asset": "<PERSON><PERSON>", "direction": "Petunjuk", "amount": "<PERSON><PERSON><PERSON>", "orderId": "ID Order", "viewPositions": "<PERSON><PERSON>", "placeAnother": "Buat Order Lain", "backToPerps": "Kembali ke Perp"}, "submitted": "Order Dikirim", "confirmed": "Order Dikonfirmasi", "cancelling_order": "Cancelling order", "cancelling_order_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_cancelled": "<PERSON><PERSON><PERSON>", "failed_to_cancel_order": "Failed to cancel order", "funds_have_been_returned_to_you": "Funds have been returned to you", "funds_are_available_to_trade": "Funds are available to trade", "close_order_still_active": "Close order still active", "order_submitted": "Order submitted", "order_filled": "Order filled", "order_placed": "Order placed", "order_placement_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_failed": "<PERSON><PERSON><PERSON> gagal", "your_funds_have_been_returned_to_you": "Your funds have been returned to you"}, "close_position": {"title": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>", "closing": "<PERSON><PERSON><PERSON> posisi...", "position_close_order_placed": "Placed order to close position", "partially_closing_position": "Partially closing position", "partial_close_submitted": "Partial close submitted", "position_partially_closed": "Position partially closed", "closing_position": "<PERSON><PERSON><PERSON>", "limit_close_order_cancelled": "Limit close order cancelled", "closing_position_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "your_funds_will_be_available_momentarily": "Your funds will be available momentarily", "cancel": "<PERSON><PERSON>", "margin": "<PERSON><PERSON>", "includes_pnl": "includes P&L", "pnl": "PnL", "estimated_pnl": "Estimasi PnL", "fees": "Biaya", "receive": "<PERSON><PERSON> akan men<PERSON>", "you_receive": "<PERSON><PERSON> akan men<PERSON>", "select_amount": "<PERSON><PERSON><PERSON> jumlah yang akan ditutup", "error_unknown": "<PERSON><PERSON> p<PERSON>i", "success_title": "Posisi Berhas<PERSON>", "position_closed": "Position closed", "funds_are_available_to_trade": "Funds are available to trade", "error_title": "<PERSON><PERSON>", "fox_points_earned": "{{points}} Poin <PERSON> diperoleh!", "minimum_remaining_warning": "Posisi yang tersisa minimal harus ${{minimum}}. Saat ini: ${{remaining}}", "minimum_remaining_error": "Tidak dapat ditutup sebagian: posisi tersisa (${{remaining}}) akan berada di bawah jumlah order minimum (${{minimum}}). Tutup 100%.", "must_close_full_below_minimum": "<PERSON><PERSON> posisi di bawah $10. <PERSON><PERSON> harus menutup posisi 100%.", "negative_receive_amount": "<PERSON><PERSON><PERSON> mele<PERSON>hi nilai posisi <PERSON>a", "no_amount_selected": "<PERSON><PERSON><PERSON> jumlah yang ingin ditutup", "order_type_reverted_to_market_order": "Changed to market order", "you_need_set_price_limit_order": "You need to set a price for a limit order."}, "tpsl": {"title": "Take profit dan stop loss", "description": "Pilih persentase profit atau loss, atau masukkan harga pemicu khusus untuk menutup posisi secara otomatis.", "set": "Atur", "updating": "Memperbarui...", "off": "<PERSON><PERSON><PERSON><PERSON>", "current_price": "Harga saat ini", "leverage": "Leverage", "margin": "<PERSON><PERSON>", "liquidation_price": "<PERSON><PERSON> lik<PERSON>", "take_profit_long": "Take profit", "take_profit_short": "Take profit", "stop_loss_long": "Stop loss", "stop_loss_short": "Stop loss", "trigger_price_placeholder": "<PERSON><PERSON> pem<PERSON>u", "profit_percent_placeholder": "Profit", "loss_percent_placeholder": "Loss", "profit_roe_placeholder": "% Profit", "loss_roe_placeholder": "% Loss", "usd_label": "($)", "take_profit_invalid_price": "Take profit must be {{direction}} {{priceType}} price", "stop_loss_invalid_price": "Stop loss must be {{direction}} {{priceType}} price", "stop_loss_beyond_liquidation_error": "Stop loss must be {{direction}} liquidation price", "stop_loss_order_view_warning": "Stop loss is {{direction}} liquidation price", "above": "above", "below": "below"}, "token_selector": {"no_tokens": "Token tidak tersedia"}, "errors": {"minimumDeposit": "Ju<PERSON><PERSON> deposit minimum adalah {{amount}} USDC", "tokenNotSupported": "Token {{token}} tidak didukung untuk deposit", "unknownError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak dikenal", "clientNotInitialized": "Klien SDK HyperLiquid tidak diinisialisasi dengan benar", "exchangeClientNotAvailable": "ExchangeClient tidak tersedia setelah inisialisasi", "infoClientNotAvailable": "InfoClient tidak tersedia setelah inisialisasi", "subscriptionClientNotInitialized": "SubscriptionClient tidak diinisialisasi", "failedToSubscribePosition": "<PERSON><PERSON> berl<PERSON>an pembaruan posisi", "failedToUnsubscribePosition": "<PERSON><PERSON> ber<PERSON><PERSON> berl<PERSON>an pembaruan posisi", "failedToSubscribeOrderFill": "Gagal berlangganan pembaruan pemenuhan order", "failedToUnsubscribeOrderFill": "<PERSON><PERSON> berhenti berlangganan pembaruan pemenuhan order", "failedToEstablishAllMids": "Gagal membuat langganan allMids global", "failedToEstablishMarketData": "Gagal membuat langganan data pasar untuk {{symbol}}", "failed_to_toggle_network": "Gagal mengganti jaringan", "noAccountSelected": "Tidak ada akun yang dipilih", "unsupportedMethod": "Metode tidak didukung: {{method}}", "invalidAddressFormat": "Format alamat tidak valid: {{address}}", "depositValidation": {"assetIdRequired": "AssetId diperlukan untuk validasi deposit", "amountRequired": "<PERSON><PERSON><PERSON> dan harus lebih besar dari 0", "amountPositive": "<PERSON><PERSON><PERSON> harus berupa angka positif"}, "withdrawValidation": {"missingParams": "Parameter yang diperlukan untuk penarikan tidak ada", "assetIdRequired": "assetId diperlukan untuk penarikan", "amountRequired": "jumlah dip<PERSON><PERSON>an untuk penarikan", "amountPositive": "<PERSON><PERSON><PERSON> harus berupa angka positif", "invalidDestination": "Format alamat tujuan tidak valid: {{address}}", "insufficientBalance": "Saldo tidak cukup. Tersedia: {{available}}, Diminta: {{requested}}", "assetNotSupported": "Aset {{assetId}} tidak didukung untuk penarikan. Aset yang didukung: {{supportedAssets}}"}, "orderValidation": {"coinRequired": "<PERSON><PERSON> dip<PERSON>an untuk order", "sizePositive": "Ukuran harus berupa angka positif", "pricePositive": "Harga harus berupa angka positif jika tersedia", "unknownCoin": "<PERSON><PERSON> tidak di<PERSON>: {{coin}}"}, "networkToggleFailed": "Gagal mengganti jaringan: {{error}}", "accountBalanceFailed": "<PERSON>l mendapatkan saldo akun: {{error}}", "positionsFailed": "Gagal mendapatkan posisi", "accountStateFailed": "Gagal mendapatkan status akun", "marketsFailed": "Gagal mendapatkan pasar", "bridgeContractNotFound": "Tidak dapat memperoleh alamat kontrak bridge HyperLiquid", "providerNotAvailable": "Penyedia {{providerId}} tidak tersedia", "withdrawFailed": "<PERSON><PERSON>", "connectionRequired": "usePerpsConnection harus digunakan dalam PerpsConnectionProvider", "assetMappingFailed": "<PERSON><PERSON> membuat pemetaan aset", "depositFailed": "Deposit gagal", "orderLeverageReductionFailed": "You cannot reduce your leverage", "connectionFailed": {"title": "Perp sedang offline untuk sementara", "description": "<PERSON>mi <PERSON>g berupaya agar dapat segera kembali online.", "retry": "Coba lagi", "go_back": "Kembali"}, "networkError": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> masalah saat menghub<PERSON>kan ke jaringan. Coba lagi.", "retry": "<PERSON><PERSON>"}, "unknown": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tak terduga. Coba lagi nanti.", "retry": "Coba lagi"}}, "position": {"title": "<PERSON><PERSON><PERSON>", "card": {"entry_price": "<PERSON><PERSON> masuk", "funding_cost": "Pendanaan", "liquidation_price": "<PERSON><PERSON>", "take_profit": "Take profit", "stop_loss": "Stop loss", "margin": "<PERSON><PERSON>", "not_set": "Belum diatur", "edit_tpsl": "Edit TP/SL", "close_position": "<PERSON><PERSON><PERSON>", "tpsl_count_multiple": "{{count}} orders", "tpsl_count_single": "{{count}} order"}, "list": {"loading": "<PERSON><PERSON><PERSON> posisi...", "error_title": "Kesalahan saat Memuat Posisi", "empty_title": "Tidak Ada Posisi Terbuka", "empty_description": "Anda belum memiliki posisi terbuka.\n<PERSON><PERSON> berdagang untuk melihat posisi Anda di sini.", "first_time_title": "Per<PERSON>", "first_time_description": "<PERSON><PERSON><PERSON><PERSON> pergerakan harga dengan leverage hingga 40x.", "start_trading": "<PERSON><PERSON>", "start_new_trade": "<PERSON><PERSON> per<PERSON> baru", "open_positions": "Po<PERSON>i <PERSON>rbuka", "position_count": "{{count}} posisi", "position_count_plural": "{{count}} posisi"}, "details": {"error_message": "Data posisi tidak ditemukan. Kembali dan coba lagi.", "section_title": "<PERSON><PERSON><PERSON>"}, "account": {"summary_title": "<PERSON><PERSON><PERSON>", "total_balance": "Total Saldo", "available_balance": "<PERSON><PERSON>", "margin_used": "<PERSON><PERSON> yang <PERSON>", "total_unrealized_pnl": "Total Laba Rugi yang Belum Terealisasi", "unrealized_pnl": "P&L Belum Terealisasi"}, "tpsl": {"update_success": "TP/SL berhasil diperbarui", "update_failed": "Failed to update TP/SL"}}, "markets": {"title": "Pasar"}, "market": {"details": {"title": "Detail Pasar", "error_message": "Data pasar tidak ditemukan. Kembali dan coba lagi."}, "statistics": "Overview", "24hr_high": "Tinggi 24 jam", "24hr_low": "Rendah 24 jam", "24h_volume": "Volume 24 jam", "open_interest": "<PERSON><PERSON><PERSON> terb<PERSON>", "funding_rate": "Tingkat pendan<PERSON>", "countdown": "<PERSON>ung mundur", "long": "<PERSON>", "short": "Short", "add_funds": "Add funds", "add_funds_to_start_trading_perps": "Tambahkan dana untuk memulai perdagangan perp", "position": "<PERSON><PERSON><PERSON>", "orders": "Order"}, "buttons": {"get_account_balance": "Dapatkan Saldo Akun", "deposit_funds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switch_to_mainnet": "Beralih ke Mainnet", "switch_to_testnet": "Beralih ke Testnet", "view_markets": "Lihat <PERSON>sar", "positions": "<PERSON><PERSON><PERSON>"}, "tooltips": {"leverage": {"title": "Leverage", "content": "Leverage memungkinkan Anda bertransaksi dengan modal lebih besar dibandingkan yang diinvestasikan. Leverage dapat meningkatkan profit, sekaligus loss Anda. Makin tinggi leverage, makin berisiko perdagangannya."}, "liquidation_price": {"title": "<PERSON><PERSON> lik<PERSON>", "content": "Jika harga mencapai titik ini, <PERSON><PERSON> akan dilikuidasi dan kehilangan margin. Leverage yang lebih tinggi meningkatkan kemungkinan hal ini."}, "margin": {"title": "<PERSON><PERSON>", "content": "Margin merupakan uang yang Anda investasikan untuk membuka perdagangan. Margin berfungsi sebagai jaminan dan merupakan kerugian maksimum yang dapat Anda alami dalam perdagangan tersebut."}, "fees": {"title": "Biaya", "content": "Biaya perdagangan dibebankan saat Anda membuka atau menutup posisi.", "metamask_fee": "Biaya MetaMask", "provider_fee": "Biaya <PERSON>", "total": "Total biaya"}, "closing_fees": {"title": "<PERSON><PERSON><PERSON>", "metamask_fee": "Biaya MetaMask", "provider_fee": "Biaya <PERSON>"}, "estimated_pnl": {"title": "Estimasi P&L", "content": "Estimasi profit atau loss saat menutup posisi ini pada harga pasar saat ini. Ini dihitung berdasarkan harga masuk dan mencakup porsi posisi yang Anda tutup."}, "limit_price": {"title": "<PERSON><PERSON>", "content": "Harga spesifik dari order limit yang akan dieksekusi. Untuk menutup posisi long, order dieksekusi saat harga naik ke level ini. Untuk menutup posisi short, order dieksekusi saat harga turun ke level ini."}, "open_interest": {"title": "<PERSON><PERSON><PERSON> terb<PERSON>", "content": "<PERSON><PERSON> gabungan dari semua posisi terbuka untuk perp ini."}, "funding_rate": {"title": "Tingkat pendan<PERSON>", "content": "Biaya per jam yang di<PERSON>an antar trader untuk mempertahankan harga agar sesuai dengan pasar. <PERSON><PERSON> ni<PERSON>ya positif, pemegang posisi long membayar pemegang posisi short. <PERSON><PERSON> negatif, pemegang posisi short membayar pemegang posisi long."}, "geo_block": {"title": "Perp tidak tersedia di wilayah Anda", "content": "Perdagangan perp tidak tersedia di lokasi Anda karena pembatasan atau sanksi setempat."}, "receive": {"title": "Terima", "content": "The amount of USDC you'll receive in your wallet after withdrawal. You'll receive USDC on the Arbitrum network."}, "withdrawal_fees": {"title": "Biaya <PERSON>", "content": "A flat fee charged by the provider on each withdrawal."}, "tp_sl": {"title": "Take Profit & Stop Loss", "content": "Take Profit (TP) menutup posisi Anda secara otomatis saat mencapai target profit. Stop Loss (SL) membatasi loss dengan menutup posisi jika harga bergerak melawan Anda."}, "close_position_you_receive": {"title": "Receive amount", "content": "Your receive amount is estimated and may change slightly due to slippage."}, "notifications": {"title": "Aktifkan notifikasi", "description": "Dapatkan pemberitahuan seputar peristiwa perdagangan perp penting seperti pemenuhan order, likuidasi, dan pembaruan pasar.", "turn_on_button": "Aktifkan"}, "got_it_button": "<PERSON><PERSON><PERSON>", "tpsl_count_warning": {"title": "Multiple TP/SL orders are active", "content": "To set TP/SL for the whole position, cancel your existing TP/SL orders first.", "view_orders_button": "View orders", "got_it_button": "Got it"}}, "connection": {"failed": "Koneksi <PERSON>l", "error_message": "Tidak dapat terhubung ke layanan perdagangan Perps.", "retry_connection": "Coba Hubungkan Kembali", "retrying_connection": "Menghubungkan...", "connecting_to_perps": "Menghubungkan ke Perps", "timeout_title": "Connection taking longer than expected"}, "chart": {"no_data": "Data grafik tidak tersedia", "candle_intervals": "Interval lilin", "candle_period_selector": {"show_more": "Selengkapnya"}}, "perps_markets": "Pasar perp", "volume": "Volume", "price_24h_change": "Harga/perubahan 24 jam", "failed_to_load_market_data": "Gagal memuat data pasar", "tap_to_retry": "Ketuk untuk mencoba lagi", "search_by_token_symbol": "<PERSON>i berda<PERSON>kan simbol token", "testnet": "Testnet", "mainnet": "Mainnet", "developer_options": {"hyperliquid_network_toggle": "<PERSON><PERSON> Hyperli<PERSON>", "simulate_connection_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transactions": {"title": "Per<PERSON>", "tabs": {"trades": "Perdagangan", "orders": "Order", "funding": "Pendanaan", "funding_description": "Funding history is a summary of the fees you have paid or received, as determined by the funding rates on your open positions."}, "not_found": "Transaksi tidak di<PERSON>n", "position": {"date": "Tanggal", "size": "Ukuran", "entry_price": "<PERSON><PERSON> masuk", "close_price": "Close price", "points": "Poin", "fees": "Total biaya", "pnl": "P&L Bersih"}, "order": {"date": "Tanggal", "size": "Ukuran", "limit_price": "Limit harga", "filled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metamask_fee": "Biaya MetaMask", "hyperliquid_fee": "Biaya Hyperliquid", "total_fee": "Total biaya"}, "funding": {"date": "Tanggal", "fee": "Biaya", "rate": "Ting<PERSON>"}, "view_on_explorer": "Lihat di block explorer", "empty_state": {"no_transactions": "Belum ada transaksi {{type}}", "history_will_appear": "Riwayat perdagangan Anda akan muncul di sini"}}, "risk_disclaimer": "Perps trading is risky, and you could suddenly and without notice lose your entire margin. You trade entirely at your own risk. Market data provided by Hyperliquid. Price chart powered by", "tutorial": {"continue": "Lanjutkan", "skip": "<PERSON><PERSON>", "add_funds": "<PERSON><PERSON><PERSON> dana", "what_are_perps": {"title": "Apa itu perp?", "description": "MetaMask kini mendukung perpetual berjan<PERSON><PERSON>—alias perp—yang memung<PERSON>kan Anda berdagang berdasarkan pergerakan harga token tanpa membelinya.", "subtitle": "<PERSON><PERSON>i cara kerjanya."}, "go_long_or_short": {"title": "<PERSON>bil posisi long atau short pada token", "description": "Pick a token to long or short, then set your order size.", "subtitle": "Ambil posisi long untuk dapat profit jika harga naik. Ambil posisi short untuk dapat profit jika harga turun."}, "choose_leverage": {"title": "<PERSON><PERSON><PERSON> leverage", "description": "Leverage mengamplifikasi keuntungan dan kerugian. Dengan leverage 10x, pergerakan harga 1% = keuntungan atau kerugian 10% pada margin Anda."}, "watch_liquidation": {"title": "Was<PERSON><PERSON> lik<PERSON>", "description": "<PERSON>a akan kehilangan seluruh margin jika token mencapai harga likuidasi. Leverage yang lebih tinggi berarti lebih sedikit ruang untuk likuidasi."}, "close_anytime": {"title": "<PERSON><PERSON><PERSON> set<PERSON>p saat", "description": "<PERSON><PERSON><PERSON> setiap saat Anda mau. Anda akan mendapatkan kembali margin, plus profit atau minus loss."}, "ready_to_trade": {"title": "Siap untuk berdagang?", "fund_text_helper": "MetaMask will swap your funds to USDC on Arbitrum, and deposit them onto HyperEVM for no added fee.", "description": "Danai akun perp <PERSON>a dengan token apa pun dan lakukan perdagangan pertama dalam hitungan detik."}, "got_it": "<PERSON><PERSON><PERSON>"}}, "receive": {"title": "Terima"}, "experience_enhancer_modal": {"title": "Bantu kami meningkatkan pengalaman Anda", "paragraph1a": "Sebagai tambahan ", "paragraph1b": ", kami ingin menggunakan data (seperti informasi dari cookie) untuk mempelajari cara Anda berinteraksi dengan komunikasi pemasaran kami.", "link": "MetaMetrics", "paragraph2": "Ini membantu kami mempersonalisasi hal yang kami bagikan kepada <PERSON>, se<PERSON>i:", "bullet1": "Perkembangan terkini", "bullet2": "<PERSON>tur produk", "bullet3": "<PERSON><PERSON> promosi lain yang relevan", "footer": "Ingat, kami tidak pernah menjual data yang Anda berikan dan Anda dapat memilih untuk keluar setiap saat.", "accept": "<PERSON><PERSON>", "cancel": "Tidak, terima kasih"}, "multi_rpc_migration_modal": {"description": "Saat ini kami mendukung berbagai RPC untuk satu jaringan. RPC terbaru Anda telah dipilih sebagai RPC default untuk mengatasi informasi yang saling bertentangan", "accept": "Terima"}, "qr_tab_switcher": {"scanner_tab": "Pindai kode QR", "receive_tab": "Kode QR Anda"}, "banner": {"bridge": {"title": "Siap untuk menggunakan bridge?", "subtitle": "Bergerak melintasi 9 chain, semuanya di dalam dompet Anda"}, "card": {"title": "Kartu MetaMask", "subtitle": "Tersedia di wilayah tertentu"}, "fund": {"title": "<PERSON><PERSON>", "subtitle": "Tambah atau transfer token untuk memulai"}, "cashout": {"title": "Cairkan uang tunai dengan MetaMask", "subtitle": "Jual kripto Anda untuk mendapatkan uang tunai"}, "aggregated": {"title": "<PERSON>do And<PERSON> dikump<PERSON>", "subtitle": "<PERSON><PERSON><PERSON> ta<PERSON>n saldo Anda di pengaturan"}, "multisrp": {"title": "Tambahkan beberapa Frasa Pemulihan <PERSON>", "subtitle": "Impor dan gunakan beberapa dompet di MetaMask"}, "solana": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON>uat akun <PERSON> untuk memulai"}, "smartAccount": {"title": "<PERSON><PERSON> mengg<PERSON>kan akun cerdas", "subtitle": "<PERSON><PERSON><PERSON>, fitur lebih cerdas"}, "backupAndSync": {"title": "Memperkenalkan pencadangan dan <PERSON>", "subtitle": "Cadangkan akun Anda dan sinkronkan pengaturan."}}, "wallet": {"title": "Dompet", "tokens": "Token", "collectible": "<PERSON><PERSON><PERSON><PERSON>", "collectibles": "NFT", "defi": "<PERSON><PERSON><PERSON>", "perps": "Per<PERSON>", "transactions": "TRANSAKSI", "no_collectibles": "Tidak melihat NFT Anda?", "no_available_tokens": "Tidak melihat token <PERSON><PERSON>?", "add_tokens": "Impor token", "are_you_sure_exit": "Are you sure you want to exit?", "search_information_not_saved": "Your search information will not be saved.", "import_token": "Would you like to import this token?", "tokens_detected_in_account": "{{tokenCount}} {{tokensLabel}} baru ditemukan di akun ini", "token_toast": {"tokens_imported_title": "Token Diimpor", "tokens_imported_desc": "Be<PERSON><PERSON><PERSON> mengimpor {{tokenSymbols}}", "token_imported_title": "Token Diimpor", "token_imported_desc": "Ber<PERSON><PERSON> mengimpor {{tokenSymbol}}", "token_imported_desc_1": "You’ve imported a token.", "tokens_import_success_multiple": "You’ve imported {{tokensNumber}} tokens.", "tokens_hidden_title": "Menyembunyikan Token", "tokens_hidden_desc": "Menyembunyikan token yang terdeteksi dari dompet Anda", "token_hidden_title": "Menyembunyikan Token", "token_hidden_desc": "Menyembunyikan {{tokenSymbol}}"}, "hide_token": {"title": "Sembunyikan Token?", "desc": "Anda dapat menambahkan kembali token ini di masa mendatang dengan membuka “Impor token” dan mencari token tersebut.", "cancel_cta": "<PERSON><PERSON>", "confirm_cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "import": "Impor", "sort_by": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ai", "filter_by": "<PERSON><PERSON>", "networks": "<PERSON><PERSON><PERSON>", "popular": "Populer", "custom": "Kustom", "current_network": "<PERSON><PERSON>an saat ini", "popular_networks": "Jaringan populer", "all_networks": "<PERSON><PERSON><PERSON>", "declining_balance": "<PERSON><PERSON> ({{currency}} tinggi-rendah)", "alphabetically": "<PERSON><PERSON><PERSON> (A-Z)", "add_to_get_started": "Tambahkan kripto untuk memulai", "token_is_needed_to_continue": "{{tokenSymbol}} diperlukan untuk melanjutkan", "fund_your_wallet_to_get_started": "<PERSON><PERSON> dompet Anda untuk memulai di web3", "add_funds": "<PERSON><PERSON><PERSON> dana", "next": "Berikutnya", "buy_asset": "Beli {{asset}}", "no_tokens": "Anda tidak memiliki token!", "show_tokens_without_balance": "<PERSON><PERSON><PERSON><PERSON> token tanpa saldo", "no_nfts_yet": "Belum ada NFT", "nfts_autodetection_title": "Deteksi NFT", "nfts_autodetection_desc": "Izinkan MetaMask mendeteksi dan menampilkan NFT di dompet Anda secara otomatis.", "network_details_check": "Pemeriksaan detail jaringan", "network_with_chain_id": "Jaringan dengan ID chain", "chain_list_returned_different_ticker_symbol": "Simbol token ini tidak cocok dengan nama jaringan atau ID chain yang dimasukkan. Banyak token populer menggunakan simbol serupa, yang dapat digunakan penipu untuk mengelabui Anda agar mengirimkan token yang lebih berharga sebagai gantinya. Pastikan untuk memverifikasi semuanya sebelum melanjutkan.", "suggested_token_symbol": "Simbol ticker yang disarankan:", "potential_scam": "Ini merupakan potensi penipuan", "network_not_matching": "Jaringan ini tidak cocok dengan ID chain atau nama yang terkait. Banyak token populer yang menggunakan nama tersebut", "target_scam_network": "menjadikannya sebagai target penipuan. Penipu dapat mengelabui Anda agar mengirimkan mata uang yang lebih berharga sebagai gantinya. Pastikan untuk memverifikasi semuanya sebelum melanjutkan.", "use_the_currency_symbol": "menggunakan simbol mata uang", "use_correct_symbol": "Pastikan Anda menggunakan simbol yang benar sebelum melanjutkan", "chain_id_currently_used": "ID chain ini saat ini digunakan oleh", "incorrect_network_name_warning": "Berdasarkan catatan kami, nama jaringan mungkin tidak cocok dengan ID chain ini.", "suggested_name": "<PERSON><PERSON> yang disarankan:", "network_check_validation_desc": "men<PERSON>rang<PERSON> peluang Anda untuk terhubung ke jaringan berbahaya atau salah.", "cant_verify_custom_network_warning": "<PERSON>mi tidak dapat memverifikasi jaringan khusus. Untuk mencegah penyedia jahat merekam aktivitas jaringan <PERSON>, tambahkan jaringan yang dipercaya saja.", "nfts_autodetection_cta": "Aktifkan deteksi NFT di Pengaturan", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "add_collectibles": "Impor NFT", "no_transactions": "Belum ada transaksi!", "switch_network_to_view_transactions": "<PERSON><PERSON><PERSON> jar<PERSON>n untuk melihat transaksi", "send_button": "<PERSON><PERSON>", "deposit_button": "<PERSON><PERSON><PERSON><PERSON>", "copy_address": "<PERSON><PERSON>", "collectible_action_title": "Opsi Koleksi", "remove_token_title": "Anda ingin menyembunyikan token ini?", "remove_collectible_title": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus koleksi ini?", "refresh_metadata": "Segarkan Metadata", "token_removal_issue_title": "<PERSON><PERSON><PERSON><PERSON> masalah saat menghapus token.", "token_removal_issue_desc": "<PERSON><PERSON><PERSON><PERSON> masalah saat mencoba menghapus token. Coba lagi.", "collectible_removed_title": "<PERSON><PERSON><PERSON><PERSON>!", "collectible_removed_desc": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> men<PERSON> kembali dengan mengetuk \"Impor NFT\"", "remove": "Sembunyikan", "cancel": "<PERSON><PERSON>", "yes": "Ya", "private_key_detected": "Kunci pribadi terdeteksi", "do_you_want_to_import_this_account": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mengimpor akun ini?", "error": "<PERSON><PERSON><PERSON>", "logout_to_import_seed": "<PERSON>a harus keluar terlebih dulu untuk mengimpor <PERSON>.", "ready_to_explore": "Sudah siap menjelajahi aplikasi blockchain?", "unable_to_load": "Tidak dapat memuat saldo", "unable_to_find_conversion_rate": "tidak ada harga konversi", "display_nft_media_desc": "Untuk mengimpor NFT, aktifkan Tampilkan media NFT di Pengaturan > Keamanan dan privasi.", "display_nft_media_cta": "Aktifkan Tampilkan media NFT", "display_media_nft_warning": "Menampilkan media dan data NFT dapat mengekspos alamat IP ke server terpusat. Impor NFT hanya jika Anda memahami risiko yang terlibat.", "nfts_autodetect_title": "Autodeteksi NFT", "nfts_autodetect_cta": "Aktifkan autodeteksi NFT", "turn_on_network_check_cta": "Aktifkan pemeriksaan detail jaringan", "display_nft_media_cta_new_1": "Untuk melihat NFT, aktifkan Tampilkan media NFT di", "display_nft_media_cta_new_2": "<PERSON><PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON> dan <PERSON>.", "banner": {"title": "Fungsionalitas dasar tidak aktif", "link": "Aktifkan fungsionalitas dasar"}, "carousel": {"empty_state": "You're all caught up!"}}, "asset_details": {"token": "Token", "amount": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON> k<PERSON>", "decimal": "Token decimal", "network": "<PERSON><PERSON><PERSON>", "network_fee": "<PERSON><PERSON><PERSON>", "lists": "Daftar Token", "hide_cta": "Sembunyikan token", "options": {"view_on_portfolio": "Lihat di Portfolio", "view_on_block": "Lihat di block explorer", "token_details": "Detail token", "remove_token": "Hapus token"}}, "nft_details": {"bought_for": "<PERSON><PERSON><PERSON>", "highest_floor_price": "<PERSON><PERSON> dasar tertinggi", "data_unavailable": "data tidak tersedia", "price_unavailable": "harga tidak tersedia", "rank": "<PERSON><PERSON><PERSON>", "contract_address": "<PERSON><PERSON><PERSON> k<PERSON>", "token_id": "ID Token", "token_symbol": "Token symbol", "token_standard": "Standar token", "date_created": "Tanggal dibuat", "unique_token_holders": "Pemegang token unik", "tokens_in_collection": "Token dalam koleksi", "creator_address": "<PERSON><PERSON><PERSON>", "last_sold": "<PERSON><PERSON><PERSON>", "highest_current_bid": "Penawaran tertinggi saat ini", "options": {"view_on_os": "Lihat di OpenSea", "remove_nft": "Hapus NFT"}, "attributes": "Atribut", "disclaimer": "Penafian: MetaMask mengambil fail media dari URL sumber. URL ini terkadang diubah oleh pasar tempat NFT dicetak."}, "activity_view": {"title": "Aktivitas"}, "transactions_view": {"title": "Transaksi"}, "add_asset": {"title": "Impor token", "title_nft": "Impor NFT", "search_token": "Search", "custom_token": "Custom token", "tokens": {"cancel_add_token": "BATAL", "add_token": "IMPOR"}, "collectibles": {"cancel_add_collectible": "BATAL", "add_collectible": "IMPOR"}, "banners": {"search_desc": "Deteksi token yang ditingkatkan saat ini tersedia di jaringan {{network}}. ", "search_link": "Aktif<PERSON> da<PERSON>.", "custom_warning_desc": "Siapa pun dapat membuat token, termasuk membuat versi palsu dari token yang ada. Pelajari selengkapnya seputar", "custom_warning_link": "penipuan dan risiko k<PERSON>n.", "custom_info_desc": "Deteksi token belum tersedia di jaringan ini. Impor token secara manual dan pastikan keamanannya. <PERSON><PERSON><PERSON><PERSON> seputar ", "custom_info_link": "penipuan dan risiko keamanan token.", "custom_security_tips": "<PERSON><PERSON>"}}, "defi_positions": {"loading_positions": "Memuat posisi DeFi...", "no_visible_positions": "Tidak dapat menemukan yang Anda cari?", "not_supported": "<PERSON><PERSON> mungkin belum mendukung protokol Anda.", "error_cannot_load_page": "Kami tidak dapat memuat halaman ini.", "error_visit_again": "<PERSON>ba kunjungi lagi nanti.", "single_token": "<PERSON><PERSON> {{symbol}}", "two_tokens": "{{symbol}} +1 lainnya", "multiple_tokens": "{{symbol}} +{{count}} la<PERSON><PERSON>", "supply": "<PERSON><PERSON><PERSON><PERSON>", "stake": "Di-stake", "borrow": "Dipinjam", "reward": "<PERSON><PERSON>"}, "terms_and_conditions": {"title": "<PERSON><PERSON><PERSON> dan <PERSON>", "description": "<PERSON><PERSON>, <PERSON><PERSON>", "terms": "<PERSON><PERSON><PERSON> dan <PERSON>"}, "privacy_policy": {"title": "<PERSON><PERSON><PERSON><PERSON>", "fine_print_1": "<PERSON><PERSON> akan memberi tahu Anda apabila memutuskan untuk menggunakan data ini untuk tujuan lain. <PERSON>a dapat meninjau", "fine_print_2": "untuk informasi selengkapnya. Ingat, <PERSON>a dapat membuka pengaturan dan memilih keluar setiap saat.", "privacy_policy_button": "<PERSON><PERSON><PERSON><PERSON>", "agree": "<PERSON><PERSON>", "decline": "Tidak, terima kasih", "description_title": "Bantu kami meningkatkan MetaMask", "description_content_1": "<PERSON>mi ingin mengumpulkan data penggunaan dasar untuk meningkatkan MetaMask. <PERSON><PERSON>i bahwa kami tidak pernah menjual data yang Anda berikan di sini.", "description_content_2": "Saat kami men<PERSON><PERSON> metrik, maka akan selalu...", "description_content_3": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda saat mengumpulkan data penggunaan untuk profil Anda.", "checkbox": "Kami akan menggunakan data ini untuk mempelajari cara Anda berinteraksi dengan komunikasi pemasaran kami. Ka<PERSON> mungkin akan membagikan berita terkait (seperti fitur produk).", "action_description_1_prefix": "Pribadi:", "action_description_2_prefix": "Umum:", "action_description_3_prefix": "Opsional:", "action_description_1_description": "Klik dan tampilan pada aplikasi disimpan, tetapi tidak untuk detail lainnya (seperti alamat publik).", "action_description_2_description": "<PERSON><PERSON> men<PERSON>kan alamat IP sementara waktu untuk mendeteksi lokasi umum (seperti negara atau wilayah Anda), tetapi alamat tersebut tidak pernah disimpan.", "action_description_3_description": "Anda memutuskan jika ingin membagikan atau menghapus data penggunaan melalui pengaturan setiap saat.", "cta_no_thanks": "Tidak, terima kasih", "cta_i_agree": "<PERSON><PERSON>", "fine_print_1_legacy": "Data ini dikumpulkan sehingga bersifat anonim untuk tujuan Peraturan Perlindungan Data Umum (UE) 2016/679.", "fine_print_2a_legacy": "*Saat Anda menggunakan Infura sebagai penyedia RPC default di MetaMask, Infura akan mengumpulkan alamat IP dan alamat dompet Ethereum Anda saat mengirim transaksi. Kami tidak menyimpan informasi ini dengan cara yang memungkinkan sistem kami menghubungkan kedua bagian data tersebut. Anda bisa mengonfigurasi penyedia RPC", "fine_print_2b_legacy": "sebelum melanjutkan.\nUntuk informasi selengkapnya seputar cara MetaMask dan Infura berinteraksi dari perspektif pengumpulan data, lihat pembaruan kami", "fine_print_2c_legacy": ". Untuk informasi lebih lanjut terkait praktik privasi kami secara umum, lihat Kebijakan Privasi kami", "here_legacy": "di sini", "description_content_1_legacy": "MetaMask ingin mengumpulkan data penggunaan dasar untuk lebih memahami cara pengguna kami berinteraksi dengan aplikasi seluler. Data ini akan digunakan untuk terus meningkatkan kegunaan dan pengalaman pengguna produk kami.", "description_content_2_legacy": "MetaMask akan...", "action_description_1_legacy": "<PERSON><PERSON> selalu mengi<PERSON>kan Anda untuk memilih keluar melalui <PERSON>", "action_description_2_legacy": "Mengirimkan klik anonim dan acara tampilan laman", "action_description_3_legacy": "mengumpulkan informasi yang tidak kami perlukan untuk memberikan layanan (se<PERSON>i kunci, alamat, hash transaksi, atau saldo)", "action_description_4_legacy": "mengumpulkan alamat IP lengkap Anda*", "action_description_5_legacy": "menjual data And<PERSON>. Tidak akan pernah!", "action_description_never_legacy": "Tidak akan pernah", "toast_message": "<PERSON><PERSON> telah memperbarui kebijakan privasi", "toast_action_button": "<PERSON><PERSON><PERSON>", "toast_read_more": "Baca selengkapnya"}, "template_confirmation": {"ok": "<PERSON>e", "cancel": "<PERSON><PERSON>"}, "approval_result": {"ok": "<PERSON>e", "success": "<PERSON><PERSON><PERSON><PERSON>", "error": "Galat", "resultPageSuccessDefaultMessage": "<PERSON>si be<PERSON>.", "resultPageErrorDefaultMessage": "<PERSON><PERSON> gagal."}, "token": {"token_symbol": "Token symbol", "token_address": "<PERSON><PERSON><PERSON>", "token_decimal": "Token decimal", "search_tokens_placeholder": "<PERSON><PERSON>", "address_cant_be_empty": "<PERSON><PERSON><PERSON> token wajib diisi.", "address_must_be_valid": "Al<PERSON>t token harus berupa alamat yang valid.", "symbol_cant_be_empty": "Simbol token wajib diisi.", "symbol_length": "Symbol must be 11 characters or fewer", "decimals_cant_be_empty": "Desimal token wajib diisi.", "decimals_is_required": "Decimal is required. Find it on:", "no_tokens_found": "<PERSON><PERSON> tidak dapat menemukan token dengan nama tersebut.", "select_token": "<PERSON><PERSON><PERSON>", "address_must_be_smart_contract": "Alamat pribadi terdeteksi. <PERSON><PERSON><PERSON>n alamat kontrak token.", "billion_abbreviation": "M", "trillion_abbreviation": "T", "million_abbreviation": "Jt", "token_details": "Detail token", "contract_address": "<PERSON><PERSON><PERSON> k<PERSON>", "token_list": "Daftar token", "market_details": "Detail pasar", "market_cap": "<PERSON><PERSON>", "total_volume": "Volume Total (24 jam)", "volume_to_marketcap": "Volume/Kap <PERSON>", "circulating_supply": "<PERSON><PERSON><PERSON> yang beredar", "all_time_high": "<PERSON><PERSON><PERSON> sepanjang waktu", "all_time_low": "<PERSON><PERSON><PERSON> sepanjang waktu", "fully_diluted": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Tidak dikenal"}, "collectible": {"collectible_address": "<PERSON><PERSON><PERSON>", "collectible_type": "<PERSON><PERSON>", "collectible_token_id": "ID", "collectible_description": "<PERSON><PERSON><PERSON><PERSON>", "address_must_be_valid": "<PERSON><PERSON><PERSON> koleksi harus berupa alamat yang valid.", "address_must_be_smart_contract": "<PERSON>amat pribadi terdeteksi. <PERSON><PERSON><PERSON><PERSON> alamat kontrak koleksi.", "address_cant_be_empty": "<PERSON><PERSON><PERSON> k<PERSON> wajib diisi.", "token_id_cant_be_empty": "Pengenal koleksi wajib diisi.", "not_owner_error_title": "<PERSON><PERSON><PERSON><PERSON>.", "not_owner_error": "<PERSON>a bukan pemilik koleksi ini, jadi <PERSON>a tidak dapat men<PERSON>bahkan<PERSON>.", "ownership_verification_error_title": "Tidak dapat menambahkan NFT", "ownership_verification_error": "<PERSON>mi tidak dapat memverifikasi kepemilikan. Ini mungkin karena standar tidak didukung atau aset tidak ada di jaringan yang Anda pilih.", "powered_by_opensea": "<PERSON><PERSON><PERSON> o<PERSON>h", "id_placeholder": "Masukkan ID koleksi", "collectible_token_standard": "<PERSON><PERSON>", "collectible_last_sold": "<PERSON><PERSON><PERSON>", "collectible_last_price_sold": "<PERSON><PERSON> te<PERSON><PERSON> ter<PERSON>", "collectible_source": "Sumber", "collectible_link": "Tautan", "collectible_asset_contract": "<PERSON><PERSON><PERSON> aset", "share_check_out_nft": "Lihat NFT saya!", "share_via": "<PERSON><PERSON><PERSON><PERSON> melalui", "untitled_collection": "<PERSON><PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON>"}, "transfer": {"title": "Transfer", "send": "KIRIM", "receive": "TERIMA"}, "accounts": {"srp_index": "SRP #{{index}}", "snap_account_tag": "Snap (Beta)", "create_new_account": "Buat akun baru", "new_account": "<PERSON>kun baru", "import_account": "<PERSON><PERSON><PERSON> akun", "connect_hardware": "Hubungkan dompet perangkat keras", "imported": "Diimpor", "qr_hardware": "Perangkat keras QR", "remove_account_title": "Penghapusan akun", "remove_account_message": "Yakin ingin menghapus akun ini?", "no": "Tidak", "yes_remove_it": "Ya, hapus", "remove_hardware_account": "<PERSON><PERSON> akun perangkat keras", "remove_hw_account_alert_description": "Yakin ingin menghapus akun dompet perangkat keras ini? Anda harus menyinkronkan ulang dompet perangkat keras jika ingin menggunakan akun ini lagi dengan MetaMask Mobile.", "remove_snap_account": "<PERSON><PERSON> akun snap", "remove_snap_account_alert_description": "Akun ini akan dihapus dari dompet Anda. Pastikan Anda memiliki Frasa Pemulihan Rahas<PERSON> atau kunci pribadi asli untuk akun yang diimpor ini sebelum melanjutkan. <PERSON>a dapat mengimpor atau membuat akun lagi dari menu tarik-turun akun.", "remove_account_alert_remove_btn": "Hapus", "remove_account_alert_cancel_btn": "<PERSON><PERSON><PERSON><PERSON>", "accounts_title": "<PERSON><PERSON><PERSON>", "connect_account_title": "Hubungkan akun", "connect_accounts_title": "Hubungkan akun", "edit_accounts_title": "<PERSON> akun", "connected_accounts_title": "<PERSON>kun yang terhubung", "connect_description": "Bagi<PERSON>, saldo, aktivitas akun <PERSON>, dan izinkan situs untuk memulai trans<PERSON>i.", "select_accounts_description": "<PERSON><PERSON><PERSON> akun yang akan digunakan di situs ini:", "connect_multiple_accounts": "Hubung<PERSON> beberapa akun", "connect_more_accounts": "Hubungkan lebih banyak akun", "add": "Tambah", "cancel": "Batalkan", "connect": "Hubungkan", "connect_with_count": "Hu<PERSON>ng<PERSON> {{<PERSON><PERSON><PERSON><PERSON>}}", "select_all": "<PERSON><PERSON><PERSON> se<PERSON>a", "unselect_all": "<PERSON><PERSON> pilih semua", "permissions": "<PERSON><PERSON>", "revoke": "<PERSON><PERSON><PERSON>", "revoke_all": "<PERSON><PERSON><PERSON> semua", "ledger": "Ledger", "site_permission_to": "Situs ini memiliki izin untuk:", "address_balance_activity_permission": "<PERSON><PERSON><PERSON>, sa<PERSON> a<PERSON>, dan aktiv<PERSON>", "suggest_transactions": "Merekomendasikan transaksi untuk disetujui", "accounts_connected": "akun terhu<PERSON>ng", "account_connected": "akun terhu<PERSON>ng", "accounts_disconnected": "akun terputus k<PERSON>.", "account_disconnected": "akun terputus k<PERSON>.", "disconnect": "<PERSON><PERSON><PERSON> kone<PERSON>i", "disconnect_all": "<PERSON><PERSON><PERSON> semua kone<PERSON>i", "reconnect_notice": "<PERSON><PERSON> Anda memutus koneksi dari {{dappUrl}}, <PERSON><PERSON> harus menghubungkan kembali akun dan jaringan agar dapat menggunakan situs ini lagi.", "disconnect_all_accounts": "Putuskan koneksi semua akun", "deceptive_site_ahead": "Situs yang menipu terdeteksi", "deceptive_site_desc": "Situs yang akan Anda kunjungi tidak aman. Penyerang dapat mengelabui Anda untuk melakukan sesuatu yang berbahaya.", "learn_more": "Selengkapnya", "advisory_by": "Penasihat disediakan oleh Ethereum Phishing Detector dan <PERSON>ort", "potential_threat": "Potensi ancaman meliputi", "fake_metamask": "Versi palsu MetaMask", "srp_theft": "Pencurian frasa pemulihan rahasia atau kata sandi", "malicious_transactions": "Transaksi berbahaya yang mengaki<PERSON>kan aset dicuri", "secret_recovery_phrase": "<PERSON><PERSON>", "account_name": "<PERSON><PERSON> akun", "select_secret_recovery_phrase": "<PERSON><PERSON><PERSON>", "reveal_secret_recovery_phrase": "<PERSON><PERSON><PERSON><PERSON>", "add_new_hd_account_helper_text": "<PERSON><PERSON> akun baru Anda akan dibuat dari", "accounts": "akun", "show_accounts": "<PERSON><PERSON><PERSON><PERSON>", "hide_accounts": "Sembunyikan", "labels": {"bitcoin_testnet_account_name": "Akun Testnet Bitcoin", "bitcoin_account_name": "Akun Bitcoin", "bitcoin_signet_account_name": "Akun Signet Bitcoin", "bitcoin_regtest_account_name": "Akun Regtest Bitcoin", "solana_devnet_account_name": "<PERSON><PERSON><PERSON>", "solana_testnet_account_name": "<PERSON><PERSON><PERSON>", "solana_account_name": "<PERSON><PERSON><PERSON>"}, "error_messages": {"failed_to_create_account": "Gagal membuat akun {{clientType}}"}, "account_connect_create_initial_account": {"description": "<PERSON><PERSON><PERSON> diperlukan untuk terhubung ke situs ini.", "button": "<PERSON><PERSON><PERSON> akun <PERSON>"}, "no_accounts_found": "<PERSON>kun tidak <PERSON>n", "no_accounts_found_for_search": "<PERSON><PERSON>n yang sesuai dengan pencarian Anda tidak ditemukan", "search_your_accounts": "<PERSON><PERSON> akun <PERSON>"}, "toast": {"connected_and_active": "terhubung dan aktif.", "now_active": "sekarang aktif.", "network_added": "<PERSON><PERSON><PERSON><PERSON>", "network_removed": "<PERSON><PERSON><PERSON><PERSON>", "network_deleted": "<PERSON><PERSON><PERSON><PERSON>", "network_permissions_updated": "<PERSON><PERSON>n <PERSON>", "revoked": "dicabut.", "revoked_all": "<PERSON><PERSON><PERSON> akun di<PERSON>but.", "accounts_connected": "akun terhu<PERSON>.", "account_connected": "akun terhu<PERSON>.", "accounts_permissions_updated": "<PERSON><PERSON> akun <PERSON>ui", "accounts_disconnected": "akun terputus k<PERSON>.", "account_disconnected": "akun terputus k<PERSON>.", "disconnected": "koneksi terputus.", "disconnected_all": "<PERSON><PERSON><PERSON> akun terputus k<PERSON>.", "disconnected_from": "<PERSON><PERSON><PERSON><PERSON> dari {{dappHostName}} terputus", "permissions_updated": "<PERSON><PERSON>", "nft_detection_enabled": "Autodeteksi NFT diaktifkan"}, "connect_qr_hardware": {"title": "Hubungkan dompet perangkat keras berbasis QR", "description1": "Hubungkan dompet perangkat keras bercelah udara yang berkomunikasi melalui kode QR.", "description2": "<PERSON> kerjanya?", "description3": "Dompet perangkat keras bercelah udara yang didukung secara resmi meliputi:", "keystone": "Keystone", "ngravezero": "<PERSON><PERSON>", "learnMore": "<PERSON><PERSON><PERSON><PERSON>", "buyNow": "<PERSON><PERSON>", "tutorial": "Tutorial", "description4": "<PERSON><PERSON> (tutorial)", "description5": "1. <PERSON><PERSON>", "description6": "2. <PERSON><PERSON><PERSON> ···, lalu buka <PERSON>", "button_continue": "Lanjutkan", "hint_text": "Pindai dompet perangkat keras ke ", "purpose_connect": "hubungkan", "purpose_sign": "kon<PERSON><PERSON><PERSON><PERSON>", "select_accounts": "<PERSON><PERSON><PERSON>"}, "data_collection_modal": {"accept": "<PERSON>e", "content": "Anda menonaktifkan pengumpulan data untuk tujuan pemasaran kami. Ini hanya berlaku untuk perangkat ini. Jika Anda menggunakan MetaMask di perangkat lain, pastikan untuk keluar terlebih dahulu."}, "account_selector": {"prev": "SEBELUMNYA", "next": "SELANJUTNYA", "unlock": "<PERSON><PERSON>", "forget": "Lupakan perangkat ini"}, "address_selector": {"select_an_address": "<PERSON><PERSON><PERSON>"}, "app_settings": {"enabling_notifications": "Mengaktifkan notifikasi...", "updating_notifications": "<PERSON><PERSON><PERSON><PERSON> notifika<PERSON>...", "updating_account_settings": "<PERSON><PERSON><PERSON><PERSON> pengaturan akun...", "reset_notifications_title": "<PERSON><PERSON> not<PERSON>", "reset_notifications_description": "Mereset notifikasi berarti Anda menghapus kunci penyimpanan notifikasi dan mereset semua riwayat notifikasi. Yakin ingin melakukan ini?", "reset_notifications": "<PERSON><PERSON> not<PERSON>", "reset_notifications_success": "Kunci penyimpanan notifikasi dihapus/dibuat ulang dan riwayat notifikasi direset.", "notifications_dismiss_modal": "Lewatkan", "select_rpc_url": "Pilih URL RPC", "title": "<PERSON><PERSON><PERSON><PERSON>", "current_conversion": "<PERSON>", "current_language": "Bahasa Saat ini", "ipfs_gateway": "Gateway IPFS", "ipfs_gateway_content": "MetaMask menggunakan layanan pihak ketiga untuk menampilkan gambar NFT Anda yang disimpan di IPFS, menampilkan informasi terkait alamat ENS yang dimasukkan di bilah alamat browser Anda, serta mengambil ikon untuk token yang berbeda. Alamat IP Anda mungkin tidak memberikan perlindungan terhadap layanan ini saat Anda menggunakannya.", "ipfs_gateway_down": "Gateway IPFS Anda saat ini mati", "ipfs_gateway_desc": "Pilih gateway IPFS pilihan Anda.", "search_engine": "<PERSON><PERSON>", "new_RPC_URL": "<PERSON><PERSON>an R<PERSON> Baru", "state_logs": "Log Status", "add_network_title": "Tambahkan jaringan", "auto_lock": "<PERSON><PERSON><PERSON> otom<PERSON>", "auto_lock_desc": "<PERSON><PERSON><PERSON> jumlah waktu sebelum aplikasi terkunci secara otomatis.", "state_logs_desc": "Ini akan membantu MetaMask memperbaiki masalah apa pun yang mungkin Anda temui. Kirimkan kepada dukungan MetaMask melalui ikon hamburger > <PERSON><PERSON>, atau balas tiket yang ada, jika ada.", "autolock_immediately": "Langsung", "autolock_never": "Tidak pernah", "autolock_after": "Set<PERSON><PERSON> {{time}} detik", "autolock_after_minutes": "<PERSON><PERSON><PERSON> {{time}} menit", "reveal_seed_words": "Ungkap Frasa Seed", "reset_account": "<PERSON><PERSON>", "state_logs_button": "Unduh Log State", "reveal_seed_words_button": "UNGKAP FRASA SEED", "reset_account_button": "<PERSON><PERSON>", "reset_account_confirm_button": "Ya, reset", "reset_account_cancel_button": "<PERSON><PERSON>", "reset_account_modal_title": "Reset Akun?", "clear_approvals_modal_title": "Hapus Data Persetujuan?", "clear_approvals_modal_message": "Semua aplikasi perlu meminta akses untuk melihat informasi akun lagi.", "clear_browser_history_modal_title": "Hapus Riwayat Browser?", "clear_browser_history_modal_message": "<PERSON><PERSON> akan menghapus semua riwayat browser Anda. Lanjutkan?", "clear_cookies_modal_title": "<PERSON><PERSON>er", "clear_cookies_modal_message": "<PERSON><PERSON> akan menghapus cookie browser <PERSON><PERSON><PERSON>?", "reset_account_modal_message": "Aktivitas transaksi akan dihapus setelah Anda mereset akun.", "save_rpc_url": "SIMPAN", "invalid_rpc_prefix": "URL memerlukan awalan HTTPS yang sesuai", "invalid_rpc_url": "URL RPC tidak valid", "invalid_block_explorer_url": "URL Block Explorer Tidak Valid", "sync": "SINKRONKAN", "clear_approved_dapps": "HAPUS APLIKASI YANG DISETUJUI", "clear_browser_history": "HAPUS RIWAYAT BROWSER", "clear_approve_dapps_desc": "Hapus aplikasi yang di<PERSON>i", "clear_browser_history_desc": "Hapus riwayat browser", "clear_browser_cookies_desc": "Hapus cookie browser", "clear": "HAPUS", "protect_cta": "Lin<PERSON>ng<PERSON>", "protect_title": "<PERSON><PERSON><PERSON><PERSON>", "banner_social_login_enabled": "<PERSON><PERSON><PERSON> {{authConnection}}", "manage_recovery_method": "<PERSON><PERSON><PERSON> metode pemulihan", "video_failed": "Gagal Memuat Video.", "protect_desc": "Cadangkan Frasa Pemulihan Rahasia agar tidak kehilangan akses ke dompet. Pastikan untuk menyimpannya di tempat aman yang hanya dapat diakses oleh Anda dan tidak akan terlupakan", "protect_desc_no_backup": "Ini merupakan frasa 12 kata dompet Anda. Frasa ini dapat digunakan untuk mengontrol semua akun saat ini dan di masa mendatang, termasuk kemampuan untuk mengirim dana di dompet Anda. Simpan frasa ini dengan aman, JANGAN bagikan kepada siapa pun. MetaMask tidak dapat membantu Anda memulihkan kunci ini.", "learn_more": "Pelajari se<PERSON>g<PERSON>.", "seedphrase_not_backed_up": "Penting! Frasa Pemulihan Rahasia belum dicadangkan", "seedphrase_backed_up": "<PERSON><PERSON> <PERSON><PERSON>", "back_up_now": "Cadang<PERSON> se<PERSON>ng", "back_up_again": "Cadangkan lagi", "view_hint": "<PERSON><PERSON>", "privacy_mode": "Mode privasi", "privacy_mode_desc": "Situs web harus meminta akses untuk melihat informasi akun Anda.", "nft_opensea_mode": "Aktifkan API OpenSea", "nft_opensea_desc": "Menampilkan media dan data NFT dapat mengekspos alamat IP Anda ke server terpusat. Gunakan API OpenSea untuk mengambil data NFT. Deteksi otomatis NFT bergantung pada API OpenSea, dan tidak akan tersedia jika dinonaktifkan. Mengaktifkan deteksi otomatis NFT dapat mengekspos Anda kepada NFT palsu yang dikirim ke dompet Anda oleh siapa pun, dan dapat memungkinkan penyerang untuk mempelajari alamat IP dari alamat Ethereum Anda.", "nft_autodetect_mode": "Deteksi otomatis NFT", "nft_autodetect_desc": "Menampilkan media dan data NFT dapat mengekspos alamat IP Anda ke server terpusat. API pihak ketiga (seperti OpenSea) digunakan untuk mendeteksi NFT di dompet Anda. Ini memperlihatkan alamat akun Anda dengan layanan tersebut. Nonaktifkan API jika Anda tidak ingin aplikasi menarik data dari layanan tersebut.", "show_fiat_on_testnets": "Tampilkan konversi di jaringan uji", "show_fiat_on_testnets_desc": "Pilih ini untuk menampilkan konversi fiat di jaringan uji", "show_fiat_on_testnets_modal_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "show_fiat_on_testnets_modal_description": "Jika diminta untuk mengaktifkan fitur ini, <PERSON><PERSON> ditipu. Token ini tidak memiliki nilai uang dan hanya untuk tujuan pengujian. Fitur ini membantu pengembang memastikan aplikasinya berfungsi.", "show_fiat_on_testnets_modal_learn_more": "Selengkapnya.", "show_fiat_on_testnets_modal_button": "Lanjutkan", "show_hex_data": "Tampilkan Data Hex", "show_hex_data_desc": "Pilih ini untuk menampilkan bidang data hex pada layar kirim.", "show_custom_nonce": "Se<PERSON>ai<PERSON> nonce trans<PERSON>i", "custom_nonce_desc": "Aktifkan ini untuk mengubah nonce (nomor transaksi) di layar konfirmasi. Ini merupakan fitur lan<PERSON>, gunakan dengan hati-hati.", "accounts_identicon_title": "Account icon", "accounts_identicon_desc": "Choose from three different styles of unique icons that can help you identify accounts at a glance.", "jazzicons": "Jazzicons", "blockies": "Blockies", "general_title": "<PERSON><PERSON>", "general_desc": "<PERSON><PERSON><PERSON><PERSON> mata uang, mata uang utama, bah<PERSON>, dan mesin pencari", "advanced_title": "Lanjutan", "advanced_desc": "<PERSON>ks<PERSON> fitur pengembang, reset akun, reset testnet, log status, gateway IPFS, dan R<PERSON> khusus", "notifications_title": "Notif<PERSON><PERSON>", "notifications_desc": "<PERSON><PERSON><PERSON>", "allow_notifications": "<PERSON>zin<PERSON> notifikasi", "enable_push_notifications": "Aktifkan notifikasi push", "allow_notifications_desc": "Pantau terus segala yang terjadi di dompet Anda dengan notifikasi. Untuk menggunakan notifikasi, kami menggunakan profil untuk menyinkronkan beberapa pengaturan di seluruh perangkat Anda.", "notifications_opts": {"customize_session_title": "<PERSON><PERSON><PERSON><PERSON> not<PERSON>", "customize_session_desc": "Aktifkan jenis notifikasi yang ingin diterima:", "account_session_title": "Aktivitas akun", "account_session_desc": "<PERSON><PERSON><PERSON> akun yang ingin mendapatkan notifikasi:", "assets_sent_title": "<PERSON><PERSON>", "assets_sent_desc": "<PERSON> dan <PERSON>", "assets_received_title": "<PERSON><PERSON>", "assets_received_desc": "<PERSON> dan <PERSON>", "defi_title": "<PERSON><PERSON><PERSON>", "defi_desc": "Stake, swap, dan bridge", "snaps_title": "Snap", "snaps_desc": "Fitur dan pembaruan terkini", "products_announcements_title": "<PERSON><PERSON><PERSON> produk", "products_announcements_desc": "Produk dan fitur baru", "perps_title": "Perdagangan Perp"}, "contacts_title": "Kontak", "contacts_desc": "<PERSON><PERSON><PERSON>, edit, ha<PERSON>, dan kelola akun <PERSON>", "permissions_title": "<PERSON><PERSON>", "permissions_desc": "<PERSON><PERSON><PERSON> izin yang diberikan ke situs dan aplikasi", "no_permissions": "Tidak ada izin", "no_permissions_desc": "<PERSON><PERSON> <PERSON>a mengh<PERSON>kan akun ke situs atau aplikasi, <PERSON>a akan melihatnya di sini.", "security_title": "<PERSON><PERSON><PERSON> dan <PERSON>", "back": "Kembali", "security_desc": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun<PERSON> p<PERSON>, dan <PERSON>.", "networks_title": "<PERSON><PERSON><PERSON>", "networks_default_title": "<PERSON><PERSON><PERSON>", "network_delete": "<PERSON><PERSON> menghapus jaringan ini, <PERSON><PERSON> harus menambahkannya lagi untuk melihat aset di jaringan ini", "networks_default_cta": "<PERSON><PERSON><PERSON> jaringan ini", "add_rpc_url": "Tambahkan URL RPC", "add_block_explorer_url": "Tambahkan URL Block Explorer", "networks_desc": "Tambahkan dan edit jaringan R<PERSON> khusus", "network_name_label": "<PERSON><PERSON>", "network_name_placeholder": "<PERSON><PERSON> (opsional)", "network_rpc_url_label": "URL RPC", "network_rpc_name_label": "Nama RPC", "network_rpc_placeholder": "<PERSON><PERSON>an R<PERSON> Baru", "network_failover_rpc_url_label": "Pengalihan URL RPC", "failover": "<PERSON><PERSON><PERSON><PERSON>", "network_chain_id_label": "ID Chain", "network_chain_id_placeholder": "ID Chain", "network_symbol_label": "Simbol", "network_block_explorer_label": "URL Block Explorer", "network_block_explorer_placeholder": "URL Block Explorer (opsional)", "network_chain_id_warning": "ID Chain Tidak Valid", "network_other_networks": "<PERSON><PERSON><PERSON>", "network_rpc_networks": "<PERSON><PERSON><PERSON>", "network_add_network": "Tambahkan Jaringan", "network_add_custom_network": "Tambahkan jaringan khusus", "network_add": "Tambahkan", "network_save": "Simpan", "remove_network_title": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menghapus jaringan ini?", "remove_network": "Hapus", "cancel_remove_network": "<PERSON><PERSON>", "info_title": "Seputar MetaMask", "info_title_beta": "Tentang MetaMask Beta", "info_title_flask": "Tentang MetaMask Flask", "experimental_title": "Eksperimental", "experimental_desc": "WalletConnect dan lainn<PERSON>...", "legal_title": "<PERSON><PERSON><PERSON>", "conversion_title": "<PERSON>n<PERSON>i mata uang", "conversion_desc": "Tam<PERSON>lkan nilai fiat dalam menggunakan mata uang tertentu di seluruh aplikasi.", "primary_currency_title": "<PERSON>", "primary_currency_desc": "Pilih Asli untuk memprioritaskan tampilan nilai dalam mata uang asli chain (mis. ETH). Pilih Fiat untuk memprioritaskan tampilan nilai dalam mata uang fiat yang Anda pilih.", "primary_currency_text_first": "<PERSON><PERSON>", "primary_currency_text_second": "Fiat", "language_desc": "Terjemahkan aplikasi ke bahasa lain yang didukung.", "engine_desc": "Ubah mesin pencari awal yang digunakan saat memasukkan istilah penelusuran di bilah URL.", "reset_desc": "Tindakan ini akan menghapus aktivitas transaksi Anda. Data ini mungkin tidak dapat dipulihkan.", "rpc_desc": "Gunakan jaringan berkemampuan RPC khusus melalui URL alih-alih salah satu jaringan yang tersedia.", "hex_desc": "Pilih ini untuk menampilkan bidang data hex pada layar kirim.", "clear_privacy_title": "Hapus data privasi", "clear_privacy_desc": "Hapus data privasi sehingga semua situs web harus meminta akses untuk melihat informasi akun lagi.", "clear_history_desc": "Pilih opsi ini untuk menghapus seluruh riwayat penelusuran Anda.", "clear_cookies_desc": "Pilih opsi ini untuk menghapus cookie browser Anda.", "metametrics_title": "Berpartisipasi dalam MetaMetrics", "metametrics_description": "Izinkan MetaMetrics mengumpulkan data penggunaan dasar dan diagnostik untuk meningkatkan produk kami. Anda dapat menonaktifkan MetaMetrics untuk perangkat ini.", "data_collection_title": "Pengumpulan data untuk pemasaran", "data_collection_description": "<PERSON><PERSON> akan menggunakan MetaMetrics untuk mempelajari cara Anda berinteraksi dengan komunikasi pemasaran kami. <PERSON><PERSON> mungkin akan membagikan berita yang relevan (seperti fitur produk dan materi lainnya).", "batch_balance_requests_title": "Kelompokkan permintaan saldo akun", "batch_balance_requests_description": "Dapatkan informasi saldo terbaru untuk seluruh akun Anda sekalig<PERSON>. Menonaktifkan fitur ini berarti yang lainnya cenderung tidak menghubungkan satu akun dengan akun lainnya.", "third_party_title": "Dapatkan transaksi masuk", "third_party_description": "API pihak ketiga (Etherscan) digunakan untuk menampilkan transaksi masuk dalam riwayat. Nonaktifkan jika Anda tidak ingin kami mengambil data dari layanan tersebut.", "metametrics_opt_out": "<PERSON><PERSON><PERSON> keluar dari <PERSON>aMetrics", "metametrics_restart_required": "Anda harus memulai ulang aplikasi untuk menerapkan perubahan.", "create_password": "<PERSON>uat kata sandi", "invalid_password": "Kata sandi tidak valid", "invalid_password_message": "Kata sandi salah. Silakan coba lagi.", "security_heading": "<PERSON><PERSON><PERSON>", "general_heading": "<PERSON><PERSON>", "privacy_heading": "<PERSON><PERSON><PERSON><PERSON>", "failed_to_fetch_chain_id": "Tidak dapat mengambil ID chain. Apakah URL RPC Anda sudah benar?", "endpoint_returned_different_chain_id": "Titik akhir mengembalikan ID chain yang berbeda: %{chainIdReturned}", "chain_id_required": "ID chain wajib diisi. ID harus cocok dengan ID chain yang dikembalikan oleh jaringan. Anda dapat memasukkan angka heksadesimal dengan awalan desimal atau '0x'.", "invalid_hex_number": "Nomor heksadesimal tidak valid.", "invalid_hex_number_leading_zeros": "Nomor heksadesimal tidak valid. Hapus semua nol di depan.", "invalid_number": "Nomor tidak valid. Masukkan angka heksadesimal dengan awalan desimal atau '0x'.", "invalid_number_leading_zeros": "Nomor tidak valid. Hapus semua nol di depan.", "invalid_number_range": "Nomor tidak valid. Masukkan angka antara 1 dan %{maxSafeChainId}", "hide_zero_balance_tokens_title": "Sembunyikan Token Tanpa Saldo", "hide_zero_balance_tokens_desc": "Men<PERSON>gah token tanpa saldo ditampilkan di daftar token <PERSON><PERSON>.", "token_detection_title": "Autodeteksi token", "token_detection_description": "Kami men<PERSON>kan API pihak ketiga untuk mendeteksi dan menampilkan token baru yang dikirim ke dompet Anda. Nonaktifkan jika Anda tidak ingin aplikasi menarik data dari layanan tersebut.", "theme_button_text": "<PERSON><PERSON> Tema", "theme_title": "<PERSON><PERSON> ({{theme}})", "theme_description": "Ubah tampilan aplikasi Anda dengan mengatur tema.", "theme_os": "Sistem", "theme_light": "Terang", "theme_dark": "<PERSON><PERSON><PERSON>", "mainnet": "Mainnet", "test_network_name": "<PERSON><PERSON><PERSON>", "custom_network_name": "<PERSON><PERSON><PERSON>", "popular": "Populer", "delete": "Hapus", "account": "akun", "accounts": "akun", "network": "<PERSON><PERSON><PERSON>", "networks": "<PERSON><PERSON><PERSON>", "network_exists": "<PERSON><PERSON><PERSON> ini telah ditambahkan.", "unMatched_chain": "Menurut catatan kami, URL ini tidak sesuai dengan penyedia yang dikenal untuk ID chain ini.", "unMatched_chain_name": "ID chain ini tidak sesuai dengan nama jaringan.", "url_associated_to_another_chain_id": "URL ini terhubung dengan ID chain lain.", "chain_id_associated_with_another_network": "Informasi yang Anda masukkan terhubung dengan ID chain yang ada. Perbarui informasi Anda atau", "network_already_exist": "Anda sudah memiliki jaringan dengan ID chain atau URL RPC yang sama. Masukkan ID chain atau URL RPC yang baru", "edit_original_network": "edit jaringan asli", "find_the_right_one": "Temukan yang tepat di:", "delete_metrics_title": "Hapus data MetaMetrics", "delete_metrics_description_part_one": "Ini akan menghapus riwayat", "delete_metrics_description_part_two": "MetaMetrics", "delete_metrics_description_part_three": "data yang terhubung dengan dompet Anda.", "delete_metrics_description_before_delete": "Dompet dan akun Anda akan tetap sama seperti sekarang setelah data ini dihapus. Proses ini dapat memerlukan waktu hingga 30 hari. Lihat", "delete_metrics_description_after_delete_part_one": "Anda memulai tindakan ini pada", "delete_metrics_description_after_delete_part_two": ". Proses ini bisa memerlukan waktu hingga 30 hari. Lihat", "delete_metrics_description_privacy_policy": "<PERSON><PERSON><PERSON><PERSON>.", "delete_metrics_button": "Hapus data MetaMetrics", "check_status_button": "Periksa Status", "delete_metrics_confirm_modal_title": "Hapus data MetaMetrics?", "delete_metrics_confirm_modal_description": "<PERSON><PERSON> akan menghapus semua data MetaMetrics Anda. Lanjutkan?", "delete_wallet_data_title": "Reset dompet", "delete_wallet_data_description": "Tindakan ini akan menghapus semua data terkait dompet dari perangkat Anda. Akun Anda terdapat di blockchain dan tidak terkait dengan MetaMask. Akun Anda dapat dipulihkan kapan saja menggunakan Frasa Pemulihan Rahasia.", "delete_wallet_data_button": "Reset dompet", "delete_data_status_title": "Penghapusan Status Tugas", "delete_data_status_description": "Status saat ini adalah", "delete_metrics_error_title": "<PERSON>mi tidak dapat menghapus data ini sekarang.", "delete_metrics_error_description": "Permintaan ini tidak dapat diselesaikan sekarang karena masalah server sistem analitis, coba lagi nanti.", "ok": "<PERSON>e", "clear_sdk_connections_title": "Hapus semua Koneksi SDK MetaMask", "clear_sdk_connections_text": "<PERSON><PERSON><PERSON> koneksi akan dihapus dan aplikasi terdesentralisasi (dapps) perlu meminta koneksi lagi", "sdk_connections": "Koneksi SDK MetaMask", "manage_sdk_connections_title": "<PERSON><PERSON><PERSON> k<PERSON>", "manage_sdk_connections_text": "Hapus koneksi ke situs dan/atau MetaMask SDK.", "fiat_on_ramp": {"title": "Beli & Jual <PERSON>", "description": "Wilayah dan lainnya...", "current_region": "Wilayah Saat Ini", "reset_region": "<PERSON><PERSON>", "no_region_selected": "Tidak ada wilayah yang dipilih", "sdk_activation_keys": "Kunci Aktivasi SDK", "activation_keys_description": "<PERSON><PERSON><PERSON> Aktivasi akan mengaktifkan fitur atau penyedia tertentu.", "add_activation_key": "Tambahkan Kunci Aktivasi", "edit_activation_key": "<PERSON><PERSON>", "paste_or_type_activation_key": "Tempel atau ketik Kunci Aktivasi", "add_label": "Tambahkan label ke kunci ini", "label": "Label", "key": "<PERSON><PERSON><PERSON>", "add": "Tambah", "update": "<PERSON><PERSON><PERSON>", "cancel": "Batalkan", "deposit_provider_logout_button": "<PERSON><PERSON><PERSON> dari {{depositProviderName}}", "deposit_provider_logged_out": "<PERSON><PERSON><PERSON> dari {{depositProviderName}}"}, "request_feature": "<PERSON><PERSON> fitur", "contact_support": "Hubungi dukungan", "display_nft_media": "Tampilkan Media NFT", "display_nft_media_desc": "Menampilkan media dan data NFT mengekspos alamat IP ke OpenSea atau pihak ketiga lainnya. Autodeteksi NFT mengandalkan fitur ini, dan tidak akan tersedia jika fitur ini dinonaktifkan.", "autodetect_nft_desc": "Izinkan MetaMask menambahkan NFT yang Anda miliki menggunakan layanan pihak ketiga (seperti OpenSea). Autodeteksi NFT mengekspos alamat IP dan akun Anda ke layanan ini. Mengaktifkan fitur ini dapat mengaitkan alamat IP dengan alamat Ethereum Anda dan menampilkan NFT palsu yang di-airdrop oleh penipu. Anda dapat menambahkan token secara manual untuk menghindari risiko ini.", "display_nft_media_desc_new": "Alamat IP dapat diketahui oleh OpenSea atau pihak ketiga lainnya saat Anda menampilkan media dan data NFT. Autodeteksi NFT mengandalkan fitur ini, dan tidak akan tersedia saat dinonaktifkan. Jika media NFT sepenuhnya disimpan di IPFS, maka masih dapat ditampilkan bahkan pada saat fitur ini dinonaktifkan.", "use_safe_chains_list_validation_desc_1": "MetaMask menggunakan layanan pihak ketiga yang disebut ", "use_safe_chains_list_validation_desc_2": "untuk menampilkan detail jaringan yang akurat dan terstandardisasi. Hal ini dapat mengurangi kemungkinan Anda untuk terhubung ke jaringan berbahaya atau salah. Saat menggunakan fitur ini, alamat IP akan diketahui oleh  ", "snaps": {"title": "Snap", "description": "<PERSON><PERSON><PERSON> dan kelo<PERSON>", "snap_ui": {"link": {"accessibilityHint": "Terbuka di tab baru"}}, "snap_settings": {"remove_snap_section_title": "Hapus <PERSON>", "remove_snap_section_description": "<PERSON>dakan ini akan mengh<PERSON>, data<PERSON>, serta izin yang diberikan.", "remove_button_label": "Hapus {{snapName}}", "remove_account_snap_warning": {"title": "Hapus <PERSON>", "description": "Menghapus Snap ini akan menghapus akun-akun berikut dari <PERSON>:", "remove_account_snap_alert_description_1": "<PERSON><PERSON>", "remove_account_snap_alert_description_2": "untuk mengonfirma<PERSON><PERSON> bahwa Anda ingin menghapus snap ini:", "banner_title": "Pastikan Anda dapat mengakses setiap akun yang dibuat oleh Snap ini sendiri sebelum menghapusnya", "cancel_button": "Batalkan", "continue_button": "Lanjutkan", "remove_snap_button": "Hapus <PERSON>", "remove_snap_error": "<PERSON><PERSON> men<PERSON> {{snapName}}", "remove_snap_success": "{{snapName}} dihapus"}}, "snap_details": {"install_date": "Diinstal pada {{date}}", "install_origin": "Instal Asal", "enabled": "Diaktifkan", "version": "<PERSON><PERSON><PERSON>"}, "keyring_account_list_item": {"account_name": "<PERSON><PERSON> akun", "public_address": "<PERSON><PERSON><PERSON>"}, "snap_permissions": {"approved_date": "Di<PERSON><PERSON><PERSON><PERSON> pada {{date}}", "permission_section_title": "<PERSON><PERSON>", "permission_requested_now": "<PERSON><PERSON><PERSON>", "human_readable_permission_titles": {"endowment:long-running": "Jalankan sepanjang waktu", "endowment:network-access": "Akses internet", "endowment:transaction-insight": "<PERSON><PERSON><PERSON><PERSON> wa<PERSON>", "endowment:cronjob": "<PERSON><PERSON><PERSON><PERSON> dan jalankan tindakan berkala", "endowment:rpc": {"snaps": "Izinkan Snap lain untuk terhubung langsung dengan Snap ini", "dapps": "Izinkan dapp untuk terhubung langsung dengan Snap ini"}, "snap_confirm": "<PERSON><PERSON><PERSON><PERSON> dialog khusus", "snap_manageState": "Simpan dan kelola data di perangkat", "snap_notify": "<PERSON><PERSON><PERSON><PERSON>", "snap_getBip32Entropy": "<PERSON><PERSON><PERSON> akun dan aset {{protocol}}", "snap_getBip32PublicKey": "Lihat kunci publik untuk {{protocol}}", "snap_getBip44Entropy": "<PERSON><PERSON><PERSON> akun dan aset {{protocol}}", "snap_getEntropy": "Dapatkan kunci arbitrer unik untuk Snap ini", "endowment:keyring": "Izinkan permintaan untuk menambah dan mengontrol akun Ethereum", "wallet_snap": "<PERSON><PERSON><PERSON><PERSON> ke {{otherSnapName}}", "endowment:webassembly": "Mendukung WebAssembly", "endowment:ethereum-provider": "Akses penyedia Ethereum", "endowment:unknown": "<PERSON><PERSON> tidak dikenal", "snap_getLocale": "<PERSON><PERSON> bahasa pilihan", "endowment:caveat:transaction-origin": "Lihat asal situs web yang mengirimkan transaksi", "endowment:extend-runtime": "Perpanjang waktu pengoperasian", "snap_dialog": "<PERSON><PERSON><PERSON><PERSON> dialog khusus", "snap_manageAccounts": "Tambah dan kontrol akun Ethereum", "endowment:signature-insight": "Menampilkan modal wawasan tanda tangan", "endowment:protocol": "Menyediakan data protokol untuk satu chain atau lebih", "snap_getPreferences": "Lihat informasi seperti bahasa pilihan Anda dan mata uang fiat", "endowment:lifecycle-hooks": "Gunakan lifecycle hook", "endowment:name-lookup": "Menyediakan pencarian domain dan alamat", "endowment:page-home": "Menampilkan layar khusus"}}}, "privacy_browser_subheading": "Hapus data privasi atau browser", "analytics_subheading": "<PERSON><PERSON><PERSON>", "transactions_subheading": "Transaksi", "network_provider": "<PERSON><PERSON><PERSON>", "token_nft_ens_subheading": "Autodeteksi Token, NFT, dan <PERSON>", "security_check_subheading": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "symbol_required": "Simbol wajib diisi.", "blockaid_desc": "Fitur ini memperingatkan Anda tentang aktivitas jahat dengan meninjau permintaan transaksi dan tanda tangan secara aktif.", "security_alerts": "<PERSON><PERSON><PERSON> keamanan", "security_alerts_desc": "Fitur ini memperingatkan Anda tentang aktivitas berbahaya dengan meninjau permintaan transaksi dan tanda tangan secara lokal. Selalu lakukan uji tuntas sendiri sebelum menyetujui permintaan apa pun. Tidak ada jaminan bahwa fitur ini akan mendeteksi semua aktivitas berbahaya. Dengan mengaktifkan fitur ini, Anda menyetujui persyaratan penggunaan penyedia.", "dismiss_smart_account_update_heading": "<PERSON><PERSON> saran \"<PERSON><PERSON><PERSON> ke <PERSON>kun <PERSON>\"", "dismiss_smart_account_update_desc": "Aktifkan ini agar saran \"Beralih ke <PERSON>kun <PERSON>\" tidak muncul lagi di akun mana pun. Akun cerdas memungkinkan transaksi yang lebih cepat, biaya jaringan yang lebih rendah, serta pembayaran yang lebih fleksibel untuk akun tersebut.", "use_smart_account_heading": "<PERSON><PERSON><PERSON> akun cerdas", "use_smart_account_desc": "Aktifkan ini untuk mengalihkan akun yang dibuat dalam MetaMask ke akun cerdas secara otomatis setiap saat fitur relevan tersedia, seperti transaksi yang lebih cepat, biaya jaringan yang lebih rendah, dan fleksibilitas pembayaran untuk transaksi tersebut.", "use_smart_account_learn_more": "Selengkapnya.", "smart_transactions_opt_in_heading": "Transaksi Pintar", "smart_transactions_opt_in_desc_supported_networks": "Aktifkan Transaksi Pintar untuk transaksi yang lebih andal dan aman pada jaringan yang didukung.", "smart_transactions_learn_more": "Selengkapnya.", "simulation_details": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "simulation_details_description": "Aktifkan ini untuk mengestimasikan perubahan saldo transaksi sebelum Anda mengonfirmasikannya. Ini tidak menjamin hasil akhir transaksi <PERSON>a. ", "simulation_details_learn_more": "Selengkapnya.", "aes_crypto_test_form_title": "Kripto AES - <PERSON><PERSON><PERSON>", "aes_crypto_test_form_description": "Bagian khusus dikembangkan untuk pengujian E2E. Apabila terlihat di aplikasi, laporkan ke dukungan MetaMask.", "developer_options": {"title": "Opsi Pengembang", "generate_trace_test": "Buat U<PERSON>", "generate_trace_test_desc": "<PERSON><PERSON><PERSON> jejak <PERSON> dari <PERSON>."}}, "aes_crypto_test_form": {"generate_random_salt": "Buat Salt Acak", "salt_bytes_count": "Jumlah byte salt yang dihitung", "generate": "Buat", "generate_encryption_key": "<PERSON>uat kunci enkripsi dari kata sandi", "password": "<PERSON>a sandi", "salt": "Salt", "encrypt_with_key": "Enkripsikan dengan kunci", "encrypt": "<PERSON>k<PERSON><PERSON><PERSON>", "encryption_key": "<PERSON><PERSON><PERSON>", "data": "Data", "decrypt_with_key": "<PERSON><PERSON><PERSON><PERSON><PERSON> dengan kunci", "decrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sdk": {"disconnect_title": "<PERSON>uskan koneksi semua situs?", "disconnect_all_info": "<PERSON><PERSON> koneksi ke semua situs di<PERSON>, <PERSON><PERSON> ha<PERSON> member<PERSON>n izin untuk menghubungkannya kembali.", "disconnect": "<PERSON><PERSON><PERSON> kone<PERSON>i", "disconnect_all": "<PERSON><PERSON><PERSON> semua kone<PERSON>i", "disconnect_all_accounts": "Putuskan koneksi semua <PERSON>", "manage_connections": "<PERSON><PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "loading": "Menghubungkan ke MetaMask...", "unkown_dapp": "Nama Aplikasi Terdesentralisasi (DAPP) tidak tersedia", "unknown": "Tidak dikenal", "no_connections": "Tidak ada koneksi", "no_connections_desc": "<PERSON><PERSON> <PERSON>a mengh<PERSON>kan akun ke situs atau aplikasi, <PERSON>a akan melihatnya di sini."}, "sdk_session_item": {"connected_accounts": "{{accountsLength}} akun terhu<PERSON>."}, "sdk_disconnect_modal": {"disconnect_all": "<PERSON>uskan koneksi dari semua situs?", "disconnect_all_desc": "<PERSON><PERSON> Anda memutuskan koneksi akun dari semua situs, <PERSON><PERSON> ha<PERSON> memberikan izin untuk menghubungkannya kembali.", "disconnect_account": "Putuskan koneksi akun?", "disconnect_all_accounts": "Putuskan koneksi semua <PERSON>", "disconnect_all_accounts_desc": "<PERSON><PERSON> Anda memutuskan koneksi semua akun dari {{dapp}}, <PERSON><PERSON> harus memberikan izin untuk menghubungkannya kembali.", "disconnect_account_desc": "<PERSON><PERSON> Anda memutuskan koneksi {{account}} dari {{dapp}}, <PERSON><PERSON> harus memberikan izin untuk menghubungkannya kembali.", "disconnect_confirm": "<PERSON><PERSON><PERSON>i", "cancel": "<PERSON><PERSON>"}, "sdk_return_to_app_modal": {"title": "Kembali ke aplikasi", "postNetworkSwitchTitle": "<PERSON><PERSON><PERSON> be<PERSON>", "description": "Ke<PERSON>li ke aplikasi untuk terus menggunakan layanannya."}, "sdk_feedback_modal": {"ok": "<PERSON>e", "title": "Akun tidak dapat terhubung", "info": "Pindai kode QR di aplikasi terdesentralisasi (dAPP) untuk terhubung kembali ke MetaMask"}, "app_information": {"title": "Informasi", "links": "Tautan", "privacy_policy": "<PERSON><PERSON><PERSON><PERSON>", "terms_of_use": "<PERSON><PERSON><PERSON><PERSON> pengg<PERSON>an", "attributions": "Atribusi", "support_center": "Kunjungi <PERSON> Du<PERSON>ngan kami", "web_site": "Kunjungi situs web kami", "contact_us": "<PERSON><PERSON><PERSON><PERSON>"}, "reveal_credential": {"seed_phrase_title": "<PERSON><PERSON><PERSON><PERSON>", "private_key_title": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi", "show_private_key": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi", "private_key_title_for_account": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi untuk \"{{accountName}}\"", "cancel": "<PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "confirm": "Selanjutnya", "seed_phrase_explanation": ["<PERSON><PERSON>", "<PERSON><PERSON>", "memberi", "akses penuh ke dompet, dana, dan akun <PERSON>.\n\n", "MetaMask merupakan", "dompet dengan kendali penuh.", "<PERSON><PERSON><PERSON>,", "<PERSON><PERSON> ad<PERSON> pem<PERSON>."], "private_key_explanation": "Simpan di tempat yang aman dan rahasia.", "private_key_warning": "Ini merupakan kunci pribadi untuk akun yang dipilih saat ini: {{accountName}}. <PERSON><PERSON> pernah mengungkapkan kunci ini. Siapa pun yang memiliki kunci pribadi Anda dapat mengontrol akun <PERSON>, termasuk mentransfer semua dana <PERSON>a.", "seed_phrase_warning_explanation": ["<PERSON>ikan tidak ada yang melihat layar Anda.", "Dukungan MetaMask tidak akan pernah memintanya."], "private_key_warning_explanation": "<PERSON>an pernah mengungkapkan kunci ini. Siapa pun yang memiliki kunci pribadi Anda dapat mengontrol akun <PERSON>, termasuk mentransfer semua dana <PERSON>.", "reveal_credential_modal": ["{{credentialName}} memberikan ", "akses penuh ke akun dan dana Anda.\n\n<PERSON>an membagikannya kepada siapa pun.\n", "akses penuh ke dompet dan dana Anda. \n\n<PERSON>an bagikan ini kepada siapa pun.\n", "Dukungan MetaMask tidak akan memintanya,", "tetapi pemancing akan berusaha men<PERSON>."], "seed_phrase": "<PERSON><PERSON>", "private_key": "Kunci pribadi Anda", "copy_to_clipboard": "<PERSON>in ke papan klip", "enter_password": "<PERSON><PERSON>kkan kata sandi untuk melanjutkan", "seed_phrase_copied_ios": "Frasa Pemulihan <PERSON>has<PERSON> untuk sementara disalin ke papan klip\n", "seed_phrase_copied_android": "Fr<PERSON> disalin ke papan klip", "seed_phrase_copied_time": "(disimpan selama 1 menit)", "private_key_copied": "Kunci pribadi untuk sementara disalin ke papan klip", "private_key_copied_time": "(disimpan selama 1 menit)", "private_key_copied_ios": "Kunci pribadi untuk sementara disalin ke papan klip\n", "private_key_copied_android": "<PERSON>nci pribadi disalin ke papan klip\n", "warning_incorrect_password": "Kata sandi salah", "unknown_error": "Tidak dapat membuka akun <PERSON>. Silakan coba lagi.", "hardware_error": "Ini merupakan akun dompet perangkat keras, Anda tidak dapat mengekspor kunci pribadi.", "seed_warning": "Ini merupakan frasa 12 kata dompet Anda. Frasa ini dapat digunakan untuk mengontrol semua akun saat ini dan di masa mendatang, termasuk kemampuan untuk mengirim dana mereka. Simpan frasa ini dengan aman, JANGAN bagikan kepada siapa pun.", "text": "TEKS", "qr_code": "KODE QR", "hold_to_reveal_credential": "<PERSON><PERSON> untuk menampilkan {{credentialName}}", "reveal_credential": "<PERSON><PERSON><PERSON><PERSON> {{credentialName}}", "keep_credential_safe": "<PERSON><PERSON> kea<PERSON> {{credentialName}} <PERSON><PERSON>", "srp_abbreviation_text": "SRP", "srp_text": "<PERSON><PERSON>", "private_key_text": "Kunci Privat", "got_it": "<PERSON><PERSON><PERSON>", "learn_more": "<PERSON><PERSON><PERSON><PERSON>"}, "screenshot_deterrent": {"title": "<PERSON><PERSON><PERSON> keamanan", "description": "Tangkapan layar bukan cara yang aman untuk melacak {{credentialName}}. Simpan di tempat yang tidak dicadangkan secara online untuk menjaga keamanan akun Anda.", "srp_text": "<PERSON><PERSON>", "priv_key_text": "<PERSON><PERSON><PERSON>"}, "password_reset": {"password_title": "<PERSON>a sandi", "password_desc": "<PERSON><PERSON><PERSON> kata sandi yang kuat untuk membuka aplikasi MetaMask di perangkat Anda. <PERSON><PERSON> kata sandi hilang, <PERSON><PERSON> me<PERSON><PERSON>an <PERSON><PERSON> untuk mengimpor kembali dompet Anda.", "password_learn_more": "Pelajari se<PERSON>g<PERSON>.", "change_password": "Ubah kata sandi", "password_hint": "Petunjuk kata sandi"}, "fund_actionmenu": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_description": "Transfer bank atau kartu dengan biaya rendah", "buy": "Bel<PERSON>", "buy_description": "Cocok untuk membeli token tertentu", "sell": "<PERSON><PERSON>", "sell_description": "Jual kripto untuk dapat uang tunai"}, "asset_overview": {"send_button": "<PERSON><PERSON>", "buy_button": "Bel<PERSON>", "token_marketplace": "Token marketplace", "sell_button": "<PERSON><PERSON>", "receive_button": "Terima", "portfolio_button": "Portfolio", "deposit_button": "<PERSON><PERSON><PERSON><PERSON>", "earn_button": "Dapatkan", "perps_button": "Per<PERSON>", "add_collectible_button": "Tambahkan", "info": "Informasi", "swap": "<PERSON><PERSON>", "bridge": "Bridge", "earn": "Dapatkan", "disabled_button": {"buy": "Pembelian tidak didukung untuk akun ini", "sell": "Penjualan tidak didukung untuk akun ini", "swap": "Swap tidak didukung untuk akun ini", "deposit": "Deposit tidak didukung untuk akun ini", "bridge": "Bridge tidak didukung untuk akun ini", "send": "Pengiriman tidak didukung untuk akun ini", "action": "Tindakan ini tidak didukung untuk akun ini", "earn": "Penghasilan tidak didukung untuk akun ini", "perps": "Perdagangan perp tidak didukung untuk akun ini"}, "description": "<PERSON><PERSON><PERSON><PERSON>", "totalSupply": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "were_unable": "<PERSON>mi tidak dapat memuat saldo", "balance": "<PERSON><PERSON><PERSON> <PERSON><PERSON> artikel dukungan", "troubleshooting_missing": "pem<PERSON>han masalah saldo yang hilang", "for_help": "untuk mendapatkan bantuan.", "troubleshoot": "Troubleshoot", "deposit_description": "Transfer bank atau kartu dengan biaya rendah", "buy_description": "Cocok untuk membeli token tertentu", "sell_description": "Jual kripto untuk dapat uang tunai", "swap_description": "Pertukaran antar token", "bridge_description": "Transfer token antar jaringan", "send_description": "<PERSON><PERSON> kripto ke akun mana pun", "receive_description": "Te<PERSON> kripto", "earn_description": "Dapatkan imbalan atas token Anda", "perps_description": "Kontrak perdagangan perp", "chart_time_period": {"1d": "<PERSON> ini", "7d": "7 hari terakhir", "1w": "<PERSON><PERSON>", "1m": "<PERSON><PERSON><PERSON>", "3m": "3 bulan te<PERSON>hir", "1y": "<PERSON><PERSON>", "3y": "3 tahun terakhir", "all": "<PERSON><PERSON><PERSON>"}, "chart_time_period_navigation": {"1d": "1H", "7d": "7H", "1w": "1M", "1m": "1B", "3m": "3B", "1y": "1T", "3y": "3T", "all": "<PERSON><PERSON><PERSON>"}, "no_chart_data": {"title": "Tidak ada data grafik", "description": "Kami tidak dapat mengambil data untuk token ini"}, "your_balance": "<PERSON><PERSON>", "unable_to_load_balance": "Tidak dapat memuat saldo Anda", "about": "Tentang", "about_content_display": {"show_more": "<PERSON><PERSON><PERSON><PERSON>", "show_less": "Ciutkan"}, "activity": "Aktivitas {{symbol}}", "disclaimer": "Data pasar disediakan oleh satu atau beberapa sumber data pihak ketiga, termasuk CoinGecko. Konten pihak ketiga tersebut disediakan semata-mata untuk tujuan informasi dan tidak boleh dianggap sebagai saran untuk membeli, <PERSON><PERSON><PERSON>, atau menggunakan aset tertentu. MetaMask tidak menyarankan penggunaan konten ini untuk tujuan tertentu dan tidak bertanggung jawab atas keakuratannya."}, "account_details": {"title": "Detail Akun", "share_account": "Bagikan", "view_account": "<PERSON>hat akun di Etherscan", "show_private_key": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi", "account_copied_to_clipboard": "<PERSON>amat publik disalin ke papan klip", "share_public_key": "Bagikan kunci publik saya:"}, "enable_nft-auto-detection": {"title": "Aktifkan autodeteksi NFT", "description": "Izinkan MetaMask mendeteksi dan menampilkan NFT dengan autodeteksi. <PERSON>a akan dapat:", "immediateAccess": "Mengakses NFT secara cepat", "navigate": "Menavigasikan aset digital dengan mudah", "dive": "Menggunakan NFT secara langsung", "allow": "Izinkan", "notRightNow": "Tidak se<PERSON>"}, "detected_tokens": {"title": "{{tokenCount}} token baru ditemukan", "title_plural": "{{tokenCount}} token baru ditemukan", "import_cta": "Impor ({{tokenCount}})", "hide_cta": "Sembunyi<PERSON>", "token_address": "<PERSON><PERSON><PERSON>: ", "token_lists": "Daftar token: {{listNames}}", "token_more": " + {{remainingListCount}} lagi", "confirm": {"cancel_cta": "<PERSON><PERSON>", "confirm_cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "import": {"title": "Impor token yang dipilih?", "desc": "<PERSON>ya token terpilih yang akan muncul di dompet Anda. <PERSON><PERSON> se<PERSON>u dapat mengimpor token tersembunyi nanti dengan mencarinya."}, "hide": {"title": "Anda yakin?", "desc": "<PERSON><PERSON> Anda menyembunyikan token, token tersebut tidak akan ditampilkan di dompet <PERSON><PERSON>. <PERSON><PERSON>, <PERSON><PERSON> ma<PERSON>h dapat menambah<PERSON>nya dengan mencarinya."}}, "address_copied_to_clipboard": "Alamat token disalin ke papan klip"}, "qr_scanner": {"invalid_qr_code_title": "Kode QR Tidak Valid", "invalid_qr_code_message": "Kode QR yang Anda coba pindai tidak valid.", "allow_camera_dialog_title": "Izinkan akses kamera", "allow_camera_dialog_message": "<PERSON><PERSON> izin Anda untuk memindai kode QR", "scanning": "memindai...", "ok": "<PERSON>e", "continue": "Lanjutkan", "cancel": "<PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "attempting_to_scan_with_wallet_locked": "Tampaknya Anda mencoba memindai kode QR, <PERSON>a harus membuka dompet untuk dapat menggunakannya.", "attempting_sync_from_wallet_error": "Tampaknya Anda mencoba menyinkronkan dengan ekstensi. <PERSON><PERSON><PERSON>, <PERSON><PERSON> harus menghapus dompet saat ini. \n\n<PERSON><PERSON><PERSON> Anda menghapus atau menginstal ulang versi baru aplikasi, pilih opsi untuk \"Menyinkronkan dengan Ekstensi MetaMask\". Penting! Sebelum menghapus dompet Anda, pastikan Anda telah mencadangkan Frasa Pemulihan Rahasia.", "not_allowed_error_title": "Aktifkan akses kamera", "not_allowed_error_desc": "Untuk memindai kode QR, berikan MetaMask akses ke kamera dari menu pengaturan perangkat Anda.", "unrecognized_address_qr_code_title": "Kode QR tak dikenal", "unrecognized_address_qr_code_desc": "Ma<PERSON>, kode QR ini tidak terkait dengan alamat akun atau alamat kontrak.", "url_redirection_alert_title": "<PERSON>a akan mengunjungi tautan eksternal", "url_redirection_alert_desc": "Tautan dapat digunakan untuk mencoba menipu atau mengelabui orang, jadi pastikan untuk mengunjungi situs web yang aman saja.", "label": "Pindai kode QR", "open_settings": "<PERSON><PERSON><PERSON><PERSON>", "camera_not_available": "Kamera tidak tersedia"}, "action_view": {"cancel": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "gas_fee_token_modal": {"title": "<PERSON><PERSON><PERSON> token", "title_pay_eth": "Bayar dengan ETH", "native_toggle_wallet": "Bayar biaya jaringan menggunakan saldo di dompet Anda.", "list_balance": "Saldo:", "insufficient_balance": "<PERSON> tidak cukup", "native_toggle_metamask": "MetaMask sedang menambah saldo untuk menyelesaikan transaksi ini.", "title_pay_with_other_tokens": "<PERSON><PERSON> dengan token lainnya"}, "transaction": {"transaction_id": "ID Transaksi", "alert": "PERINGATAN", "amount": "<PERSON><PERSON><PERSON>", "details": "Detail", "next": "Selanjutnya", "back": "Kembali", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "edit": "Edit", "cancel": "<PERSON><PERSON>", "save": "Simpan", "speedup": "Percepat", "sign_with_keystone": "Tandatangani dengan dompet perangkat keras", "sign_with_ledger": "Tandatangani dengan Ledger", "from": "<PERSON><PERSON>", "gas_fee": "Biaya gas", "gas_fee_fast": "CEPAT", "gas_fee_average": "SEDANG", "gas_fee_slow": "LAMBAT", "hex_data": "Data Hex", "custom_nonce": "<PERSON><PERSON>", "custom_nonce_tooltip": "Ini merupakan nomor transaksi suatu akun. Nonce untuk transaksi pertama adalah 0 dan akan bertambah secara berurutan.", "this_is_an_advanced": "Ini merupakan fitur lanjutan yang digunakan untuk membatalkan atau mempercepat transaksi yang berstatus menunggu.", "current_suggested_nonce": "Nonce yang disarankan saat ini:", "edit_transaction_nonce": "<PERSON> nonce <PERSON>i", "think_of_the_nonce": "Anggap nonce sebagai nomor transaksi sebuah akun. Setiap nonce akun dimulai dengan 0 untuk transaksi pertama dan berlanjut secara berurutan.", "nonce_warning": "Peringatan: <PERSON><PERSON> m<PERSON> akan mengalami masalah dengan transaksi di masa mendatang jika melanjutkannya. <PERSON><PERSON><PERSON> dengan hati-hati.", "review_details": "DETAIL", "review_data": "DATA", "data": "Data", "data_description": "Data yang terkait dengan transaksi ini", "review_function_type": "JENIS FUNGSI", "review_function": "<PERSON><PERSON><PERSON>", "review_hex_data": "Data Hex", "insufficient": "<PERSON> tidak cukup", "insufficient_amount": "<PERSON><PERSON> me<PERSON>an {{amount}} {{tokenSymbol}} lagi untuk menyelesaikan transaksi ini.", "buy_more_eth": "Beli lebih banyak ETH", "buy_more": "<PERSON><PERSON> lebih banyak", "more_to_continue": "<PERSON><PERSON><PERSON>an lebih banyak {{ticker}} untuk melanjutkan", "you_can_also_send_funds": "Anda juga dapat mengirim dana dari akun lain.", "token_marketplace": "Token marketplace", "token_Marketplace": "Token Marketplace", "go_to_faucet": "<PERSON>njungi faucet", "get_ether": "Dapatkan Ether untuk jaringan {{networkName}}.", "insufficient_tokens": "{{token}} tidak cukup", "invalid_address": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "invalid_from_address": "<PERSON><PERSON><PERSON> pen<PERSON>m tidak valid", "invalid_amount": "<PERSON><PERSON><PERSON> t<PERSON> valid", "invalid_gas": "Jumlah gas tidak valid", "invalid_gas_price": "Harga gas tidak valid", "high_gas_price": "Biaya gas yang ditetapkan mungkin terlalu tinggi ({{currentGasPrice}}). Pertimbangkan untuk menurunkan jumlahnya.", "low_gas_price": "Harga gas sangat rendah", "invalid_collectible_ownership": "Anda tidak memiliki koleksi ini", "known_asset_contract": "<PERSON><PERSON><PERSON> k<PERSON> aset yang di<PERSON>i", "max": "<PERSON><PERSON>", "recipient_address": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "to": "<PERSON>", "total": "Total", "loading": "Memuat...", "conversion_not_available": "Tingkat konversi tidak tersedia", "value_not_available": "Tidak Tersedia", "rate_not_available": "Konversi tidak tersedia", "optional": "Opsional", "no_address_for_ens": "<PERSON><PERSON><PERSON> untuk nama ENS tidak ditemukan", "lets_try": "Ya, mari kita coba", "approve_warning": "<PERSON>gan menyetu<PERSON>i tin<PERSON> ini, <PERSON><PERSON> memberikan izin bagi kontrak ini untuk digunakan hingga", "cancel_tx_title": "Mencoba membatalkan?", "cancel_tx_message": "Mengirimkan upaya ini tidak menjamin percepatan transaksi awal Anda. Jika upaya pembatalan ber<PERSON>il, <PERSON><PERSON> akan dikenakan biaya transaksi di atas.", "speedup_tx_title": "Mencoba mempercepat?", "speedup_tx_message": "Mengirimkan upaya ini tidak menjamin percepatan transaksi awal Anda. Jika upaya percepatan ber<PERSON>il, <PERSON><PERSON> akan dikenakan biaya transaksi di atas.", "nevermind": "<PERSON><PERSON><PERSON><PERSON>", "edit_network_fee": "Edit biaya gas", "edit_priority": "Edit prioritas", "gas_cancel_fee": "Biaya pembatalan Gas", "gas_speedup_fee": "Biaya percepatan Gas", "use_max": "Gunakan maks", "set_gas": "Atur", "cancel_gas": "<PERSON><PERSON>", "transaction_fee_estimated": "Estimasi biaya gas", "transaction_fee": "Biaya gas", "transaction_fee_less": "Tidak ada biaya", "total_amount": "<PERSON><PERSON><PERSON> total", "view_data": "Lihat Data", "adjust_transaction_fee": "<PERSON><PERSON><PERSON><PERSON> biaya <PERSON>i", "could_not_resolve_ens": "Tidak dapat menyelesaikan ENS", "asset": "<PERSON><PERSON>", "balance": "<PERSON><PERSON>", "token_id": "ID Token", "not_enough_for_gas": "Anda memiliki 0 {{ticker}} di akun Anda untuk membayar biaya transaksi.", "send": "<PERSON><PERSON>", "confirm_with_qr_hardware": "Konfirmasikan dengan dompet perangkat keras", "confirm_with_ledger_hardware": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "submitted": "Di<PERSON><PERSON>", "failed": "Gaga<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "signed": "<PERSON><PERSON><PERSON>", "tokenContractAddressWarning_1": "<PERSON><PERSON><PERSON> ini merupakan ", "tokenContractAddressWarning_2": "alamat kont<PERSON> token.", "tokenContractAddressWarning_3": "<PERSON>ka Anda men<PERSON>m token ke alamat ini, maka token milik <PERSON>a akan hilang.", "smartContractAddressWarning": "<PERSON>amat ini merupakan alamat kontrak cerdas. Pastikan Anda memahami fungsi dari alamat ini, jika tidak, dana <PERSON>a be<PERSON>iko hilang.", "continueError": "<PERSON>a me<PERSON> r<PERSON>, la<PERSON><PERSON><PERSON><PERSON>", "gas_education_title_ethereum": "Biaya gas Ethereum", "gas_education_title": "Biaya gas", "gas_education_1": "Biaya gas dibayarkan kepada penambang kripto yang memproses transaksi di", "gas_education_2_ethereum": "jaringan Ethereum.", "gas_education_2": "<PERSON>ingan.", "gas_education_3": "MetaMask tidak mengambil keuntungan dari biaya gas.", "gas_education_4": "Biaya gas berfluktuasi berdasarkan lalu lintas jaringan dan kompleksitas transaksi.", "gas_education_learn_more": "Pelajari selengkapnya seputar biaya gas", "confusable_title": "<PERSON><PERSON><PERSON>", "confusable_msg": "<PERSON><PERSON> telah mendeteksi karakter yang membingungkan dalam nama ENS. Periksa nama ENS untuk menghindari potensi penipuan.", "similar_to": "se<PERSON>a dengan", "contains_zero_width": "berisi karakter non-cetak", "dapp_suggested_gas": "Biaya gas ini telah disarankan oleh %{origin}. Biaya ini menggunakan estimasi gas lama yang kurang akurat. <PERSON><PERSON>, mengedit biaya gas ini dapat menyebabkan masalah pada transaksi Anda. <PERSON><PERSON> ada per<PERSON>, hubungi %{origin}.", "dapp_suggested_eip1559_gas": "Biaya gas ini telah disarankan oleh %{origin}. Mengubahnya dapat menyebabkan masalah pada transaksi Anda. <PERSON><PERSON> ada per<PERSON>, hubungi %{origin}.", "address_invalid": "Alamat penerima tidak valid.", "ens_not_found": "Belum mengatur alamat untuk nama ini.", "unknown_qr_code": "Kode QR tidak valid. Harap coba lagi.", "invalid_qr_code_sync": "Kode QR tidak valid. Harap pindai kode QR sinkronisasi dari dompet perangkat keras.", "no_camera_permission": "Kamera tidak di<PERSON>. Harap berikan izin dan coba lagi", "invalid_qr_code_sign": "Kode QR tidak valid. Harap periksa perangkat keras Anda dan coba lagi.", "no_camera_permission_android": "Izinkan MetaMask mengakses kamera Anda untuk melanjutkan. Anda juga perlu mengubah pengaturan sistem.", "mismatched_qr_request_id": "Data transaksi tidak sesuai. Gunakan dompet perangkat keras untuk menandatangani kode QR di bawah ini dan ketuk 'Dapatkan Tanda Tangan'.", "fiat_conversion_not_available": "Konversi fiat tidak tersedia untuk saat ini", "hex_data_copied": "Data hex disalin ke papan klip", "invalid_recipient": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "invalid_recipient_description": "<PERSON><PERSON><PERSON> al<PERSON> dan pastikan sudah valid", "swap_tokens": "<PERSON><PERSON> token", "fromWithColon": "Dari:"}, "custom_gas": {"total": "Total", "advanced_options": "Lanjutan", "basic_options": "<PERSON><PERSON>", "hide_advanced_options": "Sembunyikan lanjutan", "gas_limit": "Batas Gas:", "gas_price": "Harga Gas: (GWEI)", "save": "Simpan", "warning_gas_limit": "Batas gas harus lebih besar dari 20999", "warning_gas_limit_estimated": "Estimasi batas gas adalah {{gas}}, gunakan sebagai nilai minimum", "cost_explanation": "Biaya gas mencakup biaya pemrosesan transaksi Anda di jaringan Ethereum. MetaMask tidak mengambil keuntungan dari biaya ini. Semakin tinggi biayanya, semakin besar peluang transaksi Anda diproses."}, "spend_limit_edition": {"save": "Simpan", "title": "<PERSON> i<PERSON>", "spend_limit": "<PERSON><PERSON> batas penggunaan", "allow": "Izinkan", "allow_explanation": "untuk menarik dan menggunakan hingga jumlah berikut:", "proposed": "<PERSON><PERSON> per<PERSON>an yang di<PERSON>kan", "requested_by": "<PERSON>as penggunaan diminta oleh", "custom_spend_limit": "<PERSON><PERSON> k<PERSON>", "max_spend_limit": "Ma<PERSON><PERSON><PERSON> batas penggunaan maks", "minimum": "Minimum 1,00 {{tokenSymbol}}", "cancel": "<PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "allow_to_access": "Berikan izin untuk mengakses", "allow_to_address_access": "Berikan alamat ini untuk mengakses", "allow_to_transfer_all": "Berikan izin untuk mengakses dan mentransfer seluruh", "spend_cap": "Permintaan batas penggunaan untuk", "token": "token", "nft": "NFT", "you_trust_this_site": "Dengan <PERSON><PERSON><PERSON>, <PERSON>a mengizinkan pihak ketiga berikut untuk mengakses dana <PERSON>.", "you_trust_this_third_party": "Hal ini memungkinkan pihak ketiga untuk mengakses dan mentransfer NFT tanpa pemberitahuan lebih lanjut sampai Anda mencabut aksesnya.", "you_trust_this_address": "<PERSON>paka<PERSON> Anda memercayai alamat ini? <PERSON><PERSON> member<PERSON>n <PERSON>, <PERSON>a mengizinkan alamat ini untuk mengakses dana <PERSON>a.", "edit_permission": "<PERSON> i<PERSON>", "edit": "Edit", "transaction_fee_explanation": "Biaya transaksi dikaitkan dengan izin ini.", "view_details": "Lihat detail", "view_transaction_details": "Lihat detail transaksi", "view_data": "Lihat Data", "transaction_details": "Detail Transaksi", "site_url": "URL Situs", "permission_request": "<PERSON><PERSON><PERSON><PERSON>", "details_explanation": "{{host}} dapat mengakses dan menggunakan hingga jumlah maks dari akun ini.", "amount": "<PERSON><PERSON><PERSON>:", "allowance": "Tunjangan:", "to": "Ke:", "contract": "Kontrak ({{address}})", "contract_name": "<PERSON>a k<PERSON>:", "contract_address": "<PERSON><PERSON><PERSON> kontrak:", "invalid_contract_address": " Contract address is invalid. Enter a new one", "contract_allowance": "Tunjangan:", "data": "Data", "function_approve": "Fungsi: <PERSON><PERSON><PERSON><PERSON>", "function": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "all_set": "Sudah siap!", "all_set_desc": "<PERSON>a telah berhasil mengatur izin untuk situs ini.", "must_be_at_least": "Minimal {{allowance}}", "access_up_to": "<PERSON><PERSON><PERSON> hing<PERSON>:", "spending_cap": "Batas pen<PERSON>:", "approve_asset": "<PERSON><PERSON> yang di<PERSON>:"}, "browser": {"title": "Browser", "reload": "<PERSON><PERSON> ul<PERSON>", "share": "Bagikan", "bookmark": "Bookmark", "add_to_favorites": "Tambahkan ke Favorit", "error": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "go_back": "Kembali", "go_forward": "<PERSON><PERSON>", "home": "Be<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "open_in_browser": "<PERSON><PERSON>", "change_url": "Ubah url", "switch_network": "<PERSON><PERSON><PERSON>", "dapp_browser": "BROWSER DAPP", "dapp_browser_message": "MetaMask merupakan dompet dan browser untuk web terdesentralisasi. Ayo lihat!", "featured_dapps": "DAPPS UNGGULAN", "my_favorites": "FAVORIT SAYA", "search": "Cari atau ketik URL", "welcome": "Selamat datang!", "remove": "Hapus", "new_tab": "Tab baru", "tabs_close_all": "<PERSON><PERSON><PERSON>", "tabs_done": "Se<PERSON><PERSON>", "no_tabs_title": "Tidak Ada Tab Terbuka", "no_tabs_desc": "Untuk menelusuri web yang terdesentralisasi, tambahkan tab baru", "got_it": "<PERSON><PERSON><PERSON>", "max_tabs_title": "<PERSON><PERSON><PERSON> tab maksimum tercapai", "max_tabs_desc": "Saat ini kami hanya mendukung 5 tab yang terbuka sekaligus. Tutup tab yang ada sebelum menambahkan tab baru.", "failed_to_resolve_ens_name": "<PERSON><PERSON> tidak dapat menyelesaikan nama ENS tersebut", "remove_bookmark_title": "Hapus favorit", "remove_bookmark_msg": "Yakin ingin menghapus situs ini dari favorit Anda?", "yes": "Ya", "undefined_account": "<PERSON>kun belum ditentukan", "close_tab": "Tutup tab", "switch_tab": "Alihkan tab", "protocol_alert_options": {"ignore": "<PERSON><PERSON><PERSON><PERSON>", "allow": "Izinkan"}, "protocol_alerts": {"tel": "Situs web ini telah diblokir agar tidak melakukan panggilan telepon secara otomatis", "mailto": "Situs web ini telah diblokir agar tidak menulis surel secara otomatis.", "generic": "Situs web ini telah diblokir agar tidak membuka aplikasi eksternal secara otomatis"}, "ipfs_gateway_off_title": "Gateway IPFS tidak aktif", "ipfs_gateway_off_content": "Untuk melihat situs ini, aktifkan gateway IPFS di Pengaturan Privasi dan <PERSON>."}, "backup_alert": {"title": "Lindungi dompet Anda", "left_button": "Ingatkan saya nanti", "right_button": "Lindungi dompet"}, "add_favorite": {"title": "Tambahkan Favorit", "title_label": "<PERSON><PERSON>", "url_label": "Url", "add_button": "Tambahkan", "cancel_button": "<PERSON><PERSON>"}, "approval": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "deeplink": "<PERSON><PERSON><PERSON>", "qr_code": "KODE QR"}, "transactions": {"tx_review_confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tx_review_transfer": "Transfer", "tx_review_contract_deployment": "Penempatan Kontrak", "tx_review_transfer_from": "Transfer Dari", "tx_review_unknown": "Metode Tidak Dikenal", "tx_review_approve": "<PERSON><PERSON><PERSON><PERSON>", "tx_review_increase_allowance": "Tingkat<PERSON>", "tx_review_set_approval_for_all": "Atur Persetujuan Untuk Semua", "tx_review_staking_claim": "<PERSON><PERSON><PERSON>", "tx_review_staking_deposit": "Deposit Stake", "tx_review_staking_unstake": "Batalkan stake", "tx_review_lending_deposit": "<PERSON><PERSON><PERSON><PERSON>", "tx_review_lending_withdraw": "<PERSON><PERSON><PERSON>", "tx_review_perps_deposit": "<PERSON><PERSON><PERSON> yang didanai", "sent_ether": "<PERSON><PERSON><PERSON>", "self_sent_ether": "Mengirim Ether ke <PERSON>", "received_ether": "<PERSON><PERSON><PERSON>", "sent_dai": "Mengirim DAI", "self_sent_dai": "Mengirim DAI ke Diri Sendiri", "received_dai": "Menerima DAI", "sent_tokens": "Mengirim Token", "ether": "<PERSON><PERSON>", "sent_unit": "Mengirim {{unit}}", "self_sent_unit": "Mengirim {{unit}} ke <PERSON><PERSON>", "received_unit": "<PERSON><PERSON><PERSON> {{unit}}", "sent_collectible": "Mengirim Koleksi", "sent": "Mengirim", "received": "<PERSON><PERSON><PERSON>", "receive": "Terima", "swap": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON>", "redeposit": "<PERSON><PERSON><PERSON><PERSON> ulang", "interaction": "Interaksi", "contract_deploy": "Penempatan Kontrak", "to_contract": "<PERSON><PERSON><PERSON>", "tx_details_free": "<PERSON><PERSON><PERSON>", "tx_details_not_available": "Tidak tersedia", "smart_contract_interaction": "Interaksi kontrak cerdas", "swaps_transaction": "Transaksi swap", "bridge_transaction": "Bridge", "approve": "<PERSON><PERSON><PERSON><PERSON>", "increase_allowance": "Tingkat<PERSON>", "set_approval_for_all": "Atur Persetujuan Untuk Semua", "hash": "Hash", "from": "<PERSON><PERSON>", "to": "<PERSON>", "details": "Detail", "amount": "<PERSON><PERSON><PERSON>", "fee": {"transaction_fee_in_ether": "Biaya Transaksi", "transaction_fee_in_usd": "B<PERSON>ya <PERSON> (USD)"}, "gas_used": "Gas yang Di<PERSON>n (Unit)", "gas_limit": "Batas Gas (Unit)", "gas_price": "<PERSON>rga Gas (GWEI)", "base_fee": "<PERSON><PERSON><PERSON> (GWEI)", "priority_fee": "<PERSON><PERSON>ya <PERSON> (GWEI)", "multichain_priority_fee": "Biaya Prioritas", "max_fee": "Biaya Maks Per Gas", "total": "Total", "view_on": "<PERSON><PERSON>", "view_on_etherscan": "Lihat di Etherscan", "view_full_history_on": "Lihat riwayat lengkapnya di", "view_full_history_on_etherscan": "Lihat riwayat lengkapnya di Etherscan", "hash_copied_to_clipboard": "Hash transaksi disalin ke papan klip", "address_copied_to_clipboard": "<PERSON><PERSON><PERSON> disalin ke papan klip", "transaction_error": "<PERSON>t pada transaksi", "address_to_placeholder": "<PERSON><PERSON><PERSON>, al<PERSON><PERSON> publik (0x), atau ENS", "address_from_balance": "Saldo:", "status": "Status", "date": "Tanggal", "nonce": "<PERSON><PERSON>", "from_device_label": "dari perang<PERSON> ini", "import_wallet_row": "Akun ditambahkan ke perangkat ini", "import_wallet_label": "<PERSON><PERSON><PERSON>", "import_wallet_tip": "Seluruh transaksi mendatang yang dilakukan dari perangkat ini akan menyertakan label \"dari perangkat ini\" di samping stempel waktu. Untuk transaksi yang diberi tanggal sebelum menambahkan akun, riwayat ini tidak akan menunjukkan transaksi keluar mana saja yang berasal dari perangkat ini.", "sign_title_scan": "Pindai ", "sign_title_device": "dengan dompet perangkat keras Anda", "sign_description_1": "<PERSON><PERSON><PERSON> menanda<PERSON>gani dengan dompet perangkat keras,", "sign_description_2": "ketuk Dapatkan Tanda Tangan", "sign_get_signature": "Dapatkan Tanda Tangan", "transaction_id": "ID Transaksi", "network": "<PERSON><PERSON><PERSON>", "request_from": "<PERSON><PERSON><PERSON><PERSON> dari", "network_fee": "<PERSON><PERSON><PERSON>", "network_fee_tooltip": "<PERSON><PERSON><PERSON> yang di<PERSON>an untuk memproses transaksi di jaringan.", "smart_account_upgrade": "Tingkatkan ke akun cerdas", "smart_account_downgrade": "<PERSON><PERSON><PERSON> ke akun standar", "batched_transactions": "Transaksi batch", "gas_modal": {"edit_network_fee": "Edit biaya jaringan", "advanced_gas_fee": "<PERSON><PERSON><PERSON> lan<PERSON>", "site_suggested": "Situs yang disarankan", "advanced": "Lanjutan", "low": "Rendah", "medium": "Pasar", "high": "Agresif", "network_suggested": "<PERSON><PERSON><PERSON> yang disaran<PERSON>", "gas_limit": "Batas Gas", "save": "Simpan", "max_base_fee": "Biaya Dasar Ma<PERSON>", "priority_fee": "Biaya Prioritas", "current_priority_fee": "Saat ini: {{min}} - {{max}} GWEI", "historical_priority_fee": "12 jam: {{min}} - {{max}} GWEI", "estimated_base_fee": "Saat ini: {{value}} GWEI", "historical_base_fee": "12 jam: {{min}} - {{max}} GWEI", "gas_price": "Harga gas", "field_required": "{{field}} wajib diisi", "max_base_fee_required": "Biaya dasar maks wajib diisi", "gas_price_required": "Harga gas wajib diisi", "priority_fee_required": "Biaya prioritas wajib diisi", "gas_limit_required": "Batas gas wajib diisi", "only_numbers_allowed": "<PERSON><PERSON> angka yang <PERSON>an", "negative_values_not_allowed": "<PERSON><PERSON> negatif tida<PERSON>", "max_base_fee_must_be_greater_than_priority_fee": "Biaya dasar maks harus lebih besar dari biaya prioritas", "gas_limit_too_low": "Batas gas harus lebih besar dari 21000", "priority_fee_too_high": "Biaya prioritas harus kurang dari biaya dasar maks", "no_zero_value": "{{field}} harus lebih besar dari 0", "speed": "Kecepatan", "only_integers_allowed": "<PERSON><PERSON> bilangan bulat yang di<PERSON>an"}}, "smart_transactions": {"status_submitting_header": "Mengirimkan transaksi Anda", "status_submitting_description": "Est<PERSON><PERSON> {{timeLeft}}", "status_success_header": "Transaksi <PERSON>", "status_submitting_past_estimated_deadline_header": "<PERSON><PERSON> telah <PERSON>", "status_submitting_past_estimated_deadline_description": "Jika transaksi tidak diselesaikan dalam waktu {{timeLeft}}, transaksi akan dibatalkan dan Anda tidak akan dikenakan biaya gas.", "status_cancelled_header": "Transaksi <PERSON>", "status_cancelled_description": "Transaksi Anda tidak dapat disel<PERSON>, sehingga dibatalkan agar Anda tidak perlu membayar biaya gas yang tidak seharusnya.", "status_failed_header": "Trans<PERSON><PERSON> gagal", "status_failed_description": "<PERSON>bahan pasar yang mendadak dapat menyebabkan kegagalan. <PERSON><PERSON> ma<PERSON>, hubungi dukungan pelanggan MetaMask.", "view_transaction": "<PERSON><PERSON>", "view_activity": "Lihat aktivitas", "return_to_dapp": "<PERSON><PERSON><PERSON> ke {{dappName}}", "try_again": "Coba lagi", "create_new": "Buat {{txType}} baru", "swap": "tukar", "send": "kirim"}, "address_book": {"recents": "Terbaru", "save": "Simpan", "delete_contact": "<PERSON><PERSON> k<PERSON>", "delete": "Hapus", "cancel": "<PERSON><PERSON>", "add_to_address_book": "Tambahkan ke buku alamat", "enter_an_alias": "<PERSON><PERSON><PERSON><PERSON> alias", "add_this_address": "Tambahkan alamat ini ke buku alamat Anda", "next": "Selanjutnya", "enter_an_alias_placeholder": "mis., Vitalik B.", "add_contact_title": "Tambahkan kontak", "add_contact": "Tambahkan kontak", "edit_contact_title": "<PERSON>", "edit_contact": "<PERSON>", "edit": "Edit", "address_already_saved": "Kontak sudah disimpan", "address": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "nickname": "<PERSON><PERSON>", "add_input_placeholder": "<PERSON><PERSON><PERSON> (0x), atau ENS", "between_account": "Transfer antarakun saya", "others": "<PERSON><PERSON><PERSON>", "memo": "Memo", "network": "<PERSON><PERSON><PERSON>"}, "duplicate_address": {"title": "<PERSON>i adalah alamat dup<PERSON>", "body": "Daftar kontak Anda menampilkan alamat ini di lebih dari satu jaringan. Pastikan untuk memilih kontak yang benar sebelum Anda mengirim dana.", "button": "<PERSON><PERSON><PERSON>"}, "transaction_submitted": {"title": "Transaksi <PERSON>", "your_tx_hash_is": "Hash transaksi <PERSON>a adala<PERSON>:", "view_on_etherscan": "Lihat di Etherscan"}, "networks": {"title": "<PERSON><PERSON><PERSON>", "other_networks": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "status_ok": "Operasional Seluruh Sistem", "status_not_ok": "<PERSON><PERSON><PERSON> men<PERSON> ma<PERSON>ah", "want_to_add_network": "Ingin menambahkan jaringan ini?", "add_custom_network": "Tambahkan jaringan khusus", "network_infomation": "Ini memungkinkan jaringan untuk digunakan dalam MetaMask", "network_endorsement": "MetaMask tidak mendukung jaringan khusus atau keamanannya.", "learn_about": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ar", "network_risk": "penipuan dan risiko keamanan jaringan", "network_display_name": "<PERSON><PERSON> ta<PERSON>", "network_chain_id": "ID Chain", "network_rpc_url": "URL Jaringan", "network_rpc_url_label": "URL RPC Jaringan", "network_rpc_url_warning_punycode": "Penyerang terkadang meniru situs dengan membuat perubahan kecil pada alamat situs. Pastikan Anda berinteraksi dengan URL Jaringan yang dituju sebelum melanjutkan. Versi Punycode:", "new_default_network_url": "URL jaringan default baru", "current_label": "Saat ini", "new_label": "<PERSON><PERSON>", "review": "Tinjau", "view_details": "Lihat detail", "network_details": "<PERSON>ail jaringan", "network_select_confirm_use_safe_check": "<PERSON><PERSON><PERSON><PERSON> akan mengaktifkan pemeriksaan detail Jaringan. Anda dapat menonaktifkan pemeriksaan detail Jaring<PERSON> di ", "network_settings_security_privacy": "Pengaturan > <PERSON><PERSON><PERSON> dan privasi", "network_currency_symbol": "Simbol Mata Uang", "network_block_explorer_url": "URL Block Explorer", "search": "<PERSON>i jaringan yang ditambahkan sebelumnya", "search-short": "Search", "add": "Tambah", "continue": "Lanjutkan", "cancel": "<PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "edit_network_details": "Edit detail jaringan", "malicious_network_warning": "Penyedia jaringan jahat dapat berbohong tentang status blockchain dan merekam aktivitas jaringan Anda. <PERSON>ya tambahkan jaringan kustom yang Anda percayai.", "security_link": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "network_warning_title": "Informasi Jaringan", "additional_network_information_title": "Informasi <PERSON>", "network_warning_desc": "Koneksi jaringan ini mengandalkan pihak ketiga. Koneksi ini mungkin kurang bisa diandalkan atau memungkinkan pihak ketiga melacak aktivitas.", "additonial_network_information_desc": "Beberapa jaringan ini mengandalkan pihak ketiga. Koneksi ini kurang dapat diandalkan atau memungkinkan pihak ketiga melacak aktivitas.", "connect_more_networks": "Hubung<PERSON> jaringan la<PERSON>ya", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/the-risks-of-connecting-to-an-unknown-network/", "switch_network": "<PERSON><PERSON><PERSON> ke jaringan", "switch": "<PERSON><PERSON><PERSON>", "select_all": "<PERSON><PERSON><PERSON> se<PERSON>a", "deselect_all": "<PERSON><PERSON> pilih semua", "new_network": "Jaringan baru ditambahkan", "network_name": "<PERSON><PERSON><PERSON> {{networkName}}", "network_added": " kini tersedia di pemilih jaringan.", "provider": "Penyedia dipercaya untuk memberi tahu saldo dompet Anda dan menyiarkan transaksinya dengan penuh tanggung jawab", "no_match": "Tidak menemukan hasil yang cocok.", "empty_popular_networks": "Anda telah menambahkan semua jaringan yang populer. Anda dapat menemukan lebih banyak jaringan", "add_other_network_here": "di sini.", "you_can": "<PERSON><PERSON> <PERSON>", "add_network": "menambahkan lebih banyak jaringan secara manual.", "add_specific_network": "Tambahkan {{network_name}}", "update_network": "Perbarui {{network_name}}", "select_network": "<PERSON><PERSON><PERSON>", "enabled_networks": "<PERSON><PERSON><PERSON> yang <PERSON>", "additional_networks": "<PERSON><PERSON><PERSON>", "all_popular_networks": "<PERSON><PERSON><PERSON> jaringan populer", "show_test_networks": "<PERSON><PERSON><PERSON><PERSON>", "deprecated_goerli": "Sehubungan dengan perubahan protokol Ethereum: <PERSON><PERSON><PERSON> u<PERSON> mungkin tidak dapat beroperasi dengan baik dan akan segera dihentikan.", "network_deprecated_title": "Jaringan ini tidak digunakan lagi", "network_deprecated_description": "<PERSON><PERSON><PERSON> yang Anda coba hubungkan tidak lagi didukung di Metamask.", "edit_networks_title": "<PERSON>"}, "permissions": {"title_this_site_wants_to": "Situs ini ingin:", "title_dapp_url_wants_to": "{{dappUrl}} ingin:", "title_dapp_url_has_approval_to": "{{dappUrl}} memiliki per<PERSON> untuk:", "use_enabled_networks": "Menggunakan jaringan aktif", "wants_to_see_your_accounts": "<PERSON><PERSON><PERSON> akun <PERSON>a dan menyarankan transaksi", "requesting_for": "<PERSON><PERSON><PERSON> untuk ", "requesting_for_accounts": "Meminta {{numberOfAccounts}} akun", "requesting_for_networks": "Meminta {{numberOfNetworks}} jaringan", "n_networks_connect": "{{numberOfNetworks}} jar<PERSON>n terhubung ", "network_connected": "<PERSON><PERSON>n terhubung ", "see_your_accounts": "<PERSON><PERSON><PERSON> akun <PERSON>a dan menyarankan transaksi", "connected_to": "<PERSON><PERSON><PERSON><PERSON><PERSON>gan ", "manage_permissions": "<PERSON><PERSON><PERSON>", "edit": "Edit", "cancel": "<PERSON><PERSON>", "got_it": "<PERSON><PERSON><PERSON>", "connection_details_title": "Detail <PERSON>i", "connection_details_description": "Anda terhubung ke situs ini menggunakan browser MetaMask pada {{connectionDateTime}}", "title_add_network_permission": "Tambahkan izin jaringan", "add_this_network": "Tambahkan jaringan ini", "permitted_networks": "<PERSON><PERSON><PERSON> yang <PERSON>an", "choose_from_permitted_networks": "<PERSON><PERSON>h dari jaringan yang di<PERSON>an", "this_site_cant": "Situs ini tidak dapat digunakan bersama dengan jaringan Anda saat ini. Tambahkan izin jaringan atau pilih jaringan yang sudah diizinkan.", "non_permitted_network_description": "Situs ini tidak dapat digunakan bersama dengan jaringan Anda saat ini. Tambahkan izin jaringan atau pilih jaringan yang sudah diizinkan.", "edit_permissions": "<PERSON> i<PERSON>", "permitted_networks_info_sheet_description": "Ini merupakan daftar jaringan yang sebelumnya telah Anda beri izin untuk digunakan di situs ini. Pilih satu dari daftar atau edit izin jaringan untuk situs ini.", "connect_an_account": "Hubungkan akun", "sdk_connection": "SDK {{originator_platform}} v{{api_version}}"}, "account_dapp_connections": {"account_summary_header": "Hubungkan situs web ini dengan MetaMask"}, "select": {"cancel": "<PERSON><PERSON>", "done": "Se<PERSON><PERSON>"}, "signature_request": {"title": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "sign": "<PERSON>da tangani", "sign_requested": "<PERSON>da tangan Anda sedang diminta", "signing": "Tanda tangani pesan ini?", "account_title": "Akun:", "balance_title": "Saldo:", "message": "<PERSON><PERSON>", "message_from": "<PERSON><PERSON> dari", "learn_more": "Selengkapnya", "read_more": "Baca selengkapnya"}, "watch_asset_request": {"title": "Tambahkan Token yang <PERSON>", "cancel": "BATAL", "add": "TAMBAHKAN TOKEN", "message": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menambahkan token ini?", "token": "Token", "balance": "<PERSON><PERSON>"}, "unit": {"eth": "ETH", "sai": "SAI", "dai": "DAI", "negative": "-", "divisor": "/", "token_id": "#", "colon": ":", "point": ".", "week": "minggu", "day": "hari", "hour": "jam", "minute": "mnt", "second": "dtk", "empty_data": "0x"}, "biometrics": {"enable_touchid": "<PERSON>uka dengan <PERSON>?", "enable_faceid": "<PERSON><PERSON> dengan ID Wajah?", "enable_fingerprint": "<PERSON><PERSON> dengan <PERSON>?", "enable_biometrics": "Buka dengan Biometrik?", "enable_device_passcode_ios": "Buka dengan kode sandi perangkat?", "enable_device_passcode_android": "<PERSON>uka dengan PIN perangkat?"}, "authentication": {"auth_prompt_title": "Autentikas<PERSON>", "auth_prompt_desc": "Harap autentikasikan untuk menggunakan MetaMask", "fingerprint_prompt_title": "Autentikas<PERSON>", "fingerprint_prompt_desc": "Gunakan sidik jari untuk membuka MetaMask", "fingerprint_prompt_cancel": "<PERSON><PERSON>"}, "accountApproval": {"title": "PERMINTAAN CONNECT", "walletconnect_title": "PERMINTAAN WALLETCONNECT", "action": "<PERSON>bungkan ke situs ini?", "action_reconnect": "Untuk melanjutkan kone<PERSON>i, pilih nomor yang Anda lihat di aplikasi terdesentralisasi (dApp)", "action_reconnect_deeplink": "Hubungkan kembali ke aplikasi terdesentralisasi (dApp) ini?", "connect": "Hubungkan", "resume": "Lanjutkan", "cancel": "<PERSON><PERSON>", "donot_rememberme": "Lupa koneksi aplikasi terdesentralisasi (dApp) ini?", "disconnect": "<PERSON><PERSON><PERSON> kone<PERSON>i", "permission": "Lihat", "address": "alamat publik", "sign_messages": "<PERSON>da tangani pesan", "on_your_behalf": "atas nama Anda", "warning": "<PERSON><PERSON> mengeklik hubung<PERSON>, <PERSON><PERSON> mengizinkan aplikasi ini untuk melihat alamat publik Anda. Ini merupakan langkah keamanan penting untuk melindungi data Anda dari potensi risiko phishing."}, "import_private_key": {"title": "<PERSON><PERSON><PERSON>", "description_one": "Kunci pribadi yang diimpor dicadangkan ke akun Anda dan disinkronkan secara otomatis saat Anda masuk dengan login Google atau Apple yang sama.", "description_srp": "Akun yang diimpor dapat dilihat di dompet Anda tetapi tidak dapat dipulihkan dengan Frasa Pemulihan Rahasia MetaMask.", "learn_more": "Selengkapnya", "learn_more_here": "tentang cara kerja kunci yang diimpor.", "learn_more_srp": "Pelajari selengkapnya tentang akun yang diimpor", "here": "di sini.", "subtitle": "Tempel string kunci pribadi Anda", "cta_text": "Impor", "example": "mis.\n3a1076bf45ab87712ad64ccb3b10217737f7faacbf2872e88fdd9a537d8fe266", "error_title": "<PERSON><PERSON><PERSON><PERSON>", "error_message": "<PERSON>mi tidak dapat mengimpor kunci pribadi tersebut. Pastikan Anda memasukkannya dengan benar.", "error_empty_message": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi Anda.", "or_scan_a_qr_code": "atau Pindai Kode QR"}, "import_private_key_success": {"title": "Akun berhasil diimpor!", "description_one": "<PERSON>ni Anda dapat melihat akun Anda di MetaMask."}, "import_new_secret_recovery_phrase": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> frasa pemulihan rahasia dompet Anda. <PERSON><PERSON> dapat mengimpor frasa pemulihan rahasia Ethereum, Solana, atau Bitcoin.", "subtitle": "<PERSON><PERSON><PERSON> frasa pemulihan rahasia", "cta_text": "Lanjutkan", "paste": "Tempel", "clear": "<PERSON><PERSON> semua", "srp_number_of_words_option_title": "<PERSON><PERSON><PERSON> kata", "12_word_option": "Saya memiliki frasa 12 kata", "24_word_option": "<PERSON>a memiliki frasa 24 kata", "error_title": "<PERSON><PERSON><PERSON><PERSON>", "error_message": "<PERSON><PERSON> tidak dapat mengimpor frasa pemulihan rahasia tersebut. Pastikan Anda memas<PERSON>nya dengan benar.", "error_empty_message": "<PERSON>a perlu memasukkan frasa pemulihan rahasia.", "error_number_of_words_error_message": "Fr<PERSON> terdiri dari 12 atau 24 kata", "error_srp_is_case_sensitive": "Masukan tidak valid! Frasa Pemulihan Rahasia peka kapital.", "error_srp_word_error_1": "<PERSON><PERSON> ", "error_srp_word_error_2": " salah atau salah eja.", "error_multiple_srp_word_error_1": "<PERSON><PERSON> ", "error_multiple_srp_word_error_2": " dan ", "error_multiple_srp_word_error_3": " salah atau salah eja.", "error_invalid_srp": "Frasa Pemulihan <PERSON>", "error_duplicate_srp": "Frasa Pemulihan <PERSON> ini telah diimpor.", "success_1": "<PERSON><PERSON>", "success_2": "diimpor"}, "first_incoming_transaction": {"title": "{{asset}} telah didep<PERSON>itkan di akun <PERSON>a", "amount": "<PERSON><PERSON><PERSON>:", "account": "Akun:", "from": "Dari:", "cta_text": "<PERSON>e"}, "secure_your_wallet": {"title": "Amankan do<PERSON>et Anda", "step_1": "Langkah ke-1:", "step_1_description": "<PERSON>uat kata sandi", "step_2": "Langkah ke-2:", "step_2_description": "<PERSON><PERSON><PERSON> domp<PERSON>", "info_text_1": "Luangkan waktu beberapa saat untuk menyelesaikan pengaturan dompet MetaMask.", "info_text_2": "Ini akan memastikan Andalah satu-satunya yang dapat mengakses dana dan memungkinkan Anda memulihkan dompet jika perangkat hilang", "cta_text": "<PERSON>uat kata sandi", "creating_password": "Membuat kata sandi...", "srp_list_selection": "<PERSON><PERSON><PERSON>"}, "account_backup_step_1": {"remind_me_later": "Ingatkan saya nanti", "remind_me_later_subtext": "(Tidak direkomendasikan)", "title": "Amankan do<PERSON>et Anda", "info_text_1_1": "<PERSON><PERSON> tunggu sampai dana <PERSON>a hilang. Lindungi dompet <PERSON>a dengan menyimpan", "info_text_1_2": "<PERSON><PERSON>", "info_text_1_3": "di tempat yang aman.", "info_text_1_4": "Ini merupakan satu-satunya cara untuk memulihkan dompet Anda jika aplikasi terkunci atau Anda memiliki perangkat baru.", "cta_text": "<PERSON><PERSON>", "cta_subText": "Sangat direkomendasikan", "skip_button_cancel": "<PERSON><PERSON><PERSON>", "skip_button_confirm": "<PERSON><PERSON>", "skip_title": "<PERSON><PERSON> keamanan akun?", "skip_check": "<PERSON><PERSON> kehilangan Frasa P<PERSON>uli<PERSON> ini, <PERSON>a tidak dapat mengakses dompet ini.", "what_is_seedphrase_title": "Apa itu <PERSON>asa <PERSON>uli<PERSON>?", "what_is_seedphrase_text_1": "<PERSON><PERSON>, yang juga disebut seed phrase atau mnemonik, merupakan serangkaian kata yang memungkinkan Anda untuk mengakses dan mengendalikan dompet kripto. Untuk memindahkan dompet ke MetaMask, <PERSON><PERSON> me<PERSON>an frasa ini.", "what_is_seedphrase_text_2": "Anda harus merahasiakan dan mengamankan Frasa Pemulihan <PERSON>. Jika seseorang mendapatkan Frasa Pemuli<PERSON>, mereka akan mendapatkan kendali atas akun <PERSON>a.", "what_is_seedphrase_text_3": "Simpan di tempat yang aman dan hanya bisa diakses oleh <PERSON>. <PERSON><PERSON>, MetaMask sekali pun tidak dapat membantu Anda memulihkannya.", "what_is_seedphrase_text_4": "Siapa pun yang memiliki <PERSON>asa <PERSON> milik <PERSON> dapat:", "seedPhrase_point_1": "<PERSON><PERSON><PERSON> semua u<PERSON>", "seedPhrase_point_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seedPhrase_point_3": "Mengubah informasi login <PERSON>a", "what_is_seedphrase_confirm": "<PERSON><PERSON><PERSON>"}, "account_backup_step_1B": {"title": "Amankan do<PERSON>et Anda", "subtitle_1": "<PERSON><PERSON><PERSON>", "subtitle_2": "dompet <PERSON>.", "cta_text": "<PERSON><PERSON>", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "why_important": "Mengapa penting?", "manual_title": "Manual", "manual_subtitle": "Tuliskan Frasa Pemulihan <PERSON>ia pada selembar kertas dan simpan di tempat yang aman.", "manual_security": "Tingkat keamanan: <PERSON><PERSON> kuat", "risks_title": "<PERSON><PERSON><PERSON><PERSON>:", "risks_1": "<PERSON><PERSON> keh<PERSON>n", "risks_2": "Anda lupa di mana Anda men<PERSON>ya", "risks_3": "<PERSON><PERSON> lain menemukannya", "other_options": "Opsi lain: <PERSON><PERSON>k harus pada selembar kertas!", "tips_title": "Kiat:", "tips_1": "Simpan di brankas bank", "tips_2": "Simpan di tempat aman", "tips_3": "Simpan di beberapa tempat rahasia", "why_secure_title": "Lindungi dompet Anda", "why_secure_1": "<PERSON>an tunggu sampai dana <PERSON>a hilang. Lindungi dompet Anda dengan menyimpan Frasa Pemulihan Rahas<PERSON> di tempat yang aman.", "why_secure_2": "Ini merupakan satu-satunya cara untuk memulihkan dompet Anda jika aplikasi terkunci atau Anda memiliki perangkat baru."}, "account_backup_step_2": {"cancel_backup_title": "Batalkan Pencadangan", "cancel_backup_message": "<PERSON><PERSON>an agar <PERSON><PERSON> menyi<PERSON>an <PERSON>uli<PERSON> untuk memulihkan dompet Anda.", "cancel_backup_ok": "Ya, saya akan menanggung risikonya", "cancel_backup_no": "Tidak, cadangkan Frasa Pemulihan Rahasia", "title": "<PERSON><PERSON> pena dan kertas", "info": "<PERSON><PERSON><PERSON> adalah <PERSON>.", "info_2_1": "<PERSON>a akan diminta untuk", "info_2_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> kem<PERSON>i", "info_2_3": "untuk konfirmasi", "cta_text": "<PERSON>e"}, "account_backup_step_3": {"cancel_backup_title": "Batalkan Pencadangan", "cancel_backup_message": "<PERSON><PERSON>an agar <PERSON><PERSON> menyi<PERSON>an <PERSON>uli<PERSON> untuk memulihkan dompet Anda.", "cancel_backup_ok": "Ya, saya akan menanggung risikonya", "cancel_backup_no": "Tidak, cadangkan Frasa Pemulihan Rahasia", "title": "Apakah ada yang melihatnya?", "info_text": "Pastikan tidak ada orang lain atau robot yang melihat layar Anda. <PERSON><PERSON>asa <PERSON> di<PERSON>in, ini dapat digunakan pada perangkat lain untuk mencuri dana Anda", "cta_text": "TIDAK ADA YANG MELIHAT SAYA"}, "account_backup_step_4": {"cancel_backup_title": "Batalkan Pencadangan", "cancel_backup_message": "<PERSON><PERSON>an agar <PERSON><PERSON> menyi<PERSON>an <PERSON>uli<PERSON> untuk memulihkan dompet Anda.", "cancel_backup_ok": "Ya, saya akan menanggung risikonya", "cancel_backup_no": "Tidak, cadangkan Frasa Pemulihan Rahasia", "back": "Kembali", "title": "<PERSON><PERSON>", "info_text_1": "Tuliskan dengan hati-hati kata-kata ini di atas kertas. Urutannya harus benar.", "info_text_2": "Anda akan diminta untuk memasukkannya kembali di layar berikutnya", "cta_text": "SAYA TELAH MENYALIN FRASA", "confirm_password": "Kon<PERSON><PERSON><PERSON><PERSON> kata sandi <PERSON>a", "before_continiuing": "<PERSON>bel<PERSON> me<PERSON>, kon<PERSON><PERSON><PERSON>an kata sandi Anda terlebih dulu", "confirm": "KONFIRMASIKAN"}, "account_backup_step_5": {"error_title": "Ups!", "error_message": "<PERSON><PERSON><PERSON> kata-ka<PERSON>a salah. Pastikan Anda <PERSON>nya dengan benar dan kembali ke layar sebelumnya jika perlu", "back": "Kembali", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info_text": "<PERSON><PERSON><PERSON><PERSON> setiap kata sesuai urutan yang ditampilkan di layar sebelumnya.", "cta_text": "KONFIRMASIKAN FRASA", "modal_title": "Frasa Pemulihan <PERSON> di<PERSON>fi<PERSON>!", "modal_text": "Hal ini untuk memastikan Anda mengikuti langkah keamanan ini", "modal_button": "SELANJUTNYA"}, "account_backup_step_6": {"title": "<PERSON><PERSON>", "info_text": "MetaMask tidak dapat memulihkan Frasa Pemulihan Rahas<PERSON> yang hilang", "tip_1": "Simpan beberapa cadangan Frasa Pemulihan <PERSON>", "tip_2": "Simpan frasa pada pengelola kata sandi tepercaya dan cadangan kertas di tempat yang aman", "tip_3": "<PERSON>an pernah membagikan frasa ini kepada siapa pun", "disclaimer": "* <PERSON>a dapat men<PERSON>n <PERSON> dengan membuka", "disclaimer_bold": "Pengaturan > <PERSON><PERSON><PERSON> dan <PERSON>", "cta_text": "MENGERTI!", "modal_title": "Selamat!", "modal_text": "Frasa telah dicadangkan dan semua siap!", "modal_button": "SELESAI", "copy_seed_phrase": "SALIN Frasa Pemulihan Rahasia KE PAPAN KLIP"}, "manual_backup": {"progressOne": "Buat Kata Sandi", "progressTwo": "Amankan do<PERSON>et", "progressThree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "manual_backup_step_1": {"steps": "Lang<PERSON>h {{currentStep}} dari {{totalSteps}}", "action": "<PERSON><PERSON><PERSON>", "info-1": "<PERSON><PERSON> adalah", "info-2": "dompet <PERSON>.", "info-3": "Catatlah dalam urutan yang benar dan simpan dengan aman. <PERSON>ka seseorang memiliki Frasa Pemulihan <PERSON>, maka dia dapat mengakses dompet Anda.", "info-4": "<PERSON>an pernah membaginya dengan siapa pun.", "continue": "Lanjutkan", "reveal": "Ketuk untuk menampilkan", "watching": "<PERSON>ikan tidak ada yang melihat layar Anda.", "view": "Lihat", "confirm_password": "Kon<PERSON><PERSON><PERSON><PERSON> kata sandi <PERSON>a", "before_continiuing": "<PERSON><PERSON>kkan kata sandi untuk melanjutkan", "enter_current_password": "<PERSON><PERSON>kkan kata sandi saat ini", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "got_it": "<PERSON><PERSON><PERSON>", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON>a sandi"}, "manual_backup_step_2": {"steps": "Lang<PERSON>h {{currentStep}} dari {{totalSteps}}", "action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>h kata-kata yang hilang dalam urutan yang benar.", "complete": "Selesaikan <PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "error-title": "<PERSON><PERSON> tepat", "error-description": "<PERSON><PERSON><PERSON> kembali <PERSON> dan coba lagi.", "success-title": "Sempurna!", "success-description": "Benar sekali! Dan ingat: jangan pernah membagikan frasa ini kepada siapa pun.", "success-button": "<PERSON><PERSON><PERSON>", "error-button": "Coba lagi", "continue": "Lanjutkan"}, "manual_backup_step_3": {"steps": "Lang<PERSON>h {{currentStep}} dari {{totalSteps}}", "congratulations": "Se<PERSON>at", "success": "Dompet Anda telah terlindungi. Ingatlah untuk menjaga Frasa Pemulihan Rahas<PERSON> agar tetap aman, ini merupakan tanggung jawab Anda!", "hint": "Tinggalkan petunjuk?", "recover": "MetaMask tidak dapat memulihkan dompet Anda jika hilang. Anda dapat menemukan Frasa Pemulihan Rahas<PERSON> di Pengaturan > <PERSON><PERSON><PERSON> dan <PERSON>.", "learn": "<PERSON><PERSON><PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "recovery_hint": "Petun<PERSON>k pem<PERSON>", "leave_hint": "Tinggalkan petunjuk untuk diri Anda. <PERSON><PERSON> lokas<PERSON>/tempat Anda menyimpannya untuk mengingatkan diri Anda cara mengaksesnya. Informasi ini tidak boleh hilang dari perangkat Anda.", "no_seedphrase": "<PERSON><PERSON> gunakan ini untuk menulis Frasa <PERSON>.", "example": "mis., rumah <PERSON>", "save": "Simpan", "cancel": "<PERSON><PERSON>"}, "phishing": {"ethereum_phishing_detection": "Deteksi Phishing Ethereum", "ethereum_phishing_detector": "Detektor Phishing Ethereum", "intro": "saat ini ada dalam daftar peringatan domain MetaMask. Ini berarti bahwa berdasarkan informasi yang tersedia bagi kami, MetaMask yakin bahwa domain ini dapat membahayakan keamanan Anda saat ini dan, sebagai fitur keamanan tambahan, MetaMask telah membatasi akses ke situs ini. Untuk mengubahnya, harap baca sisa peringatan ini untuk mendapatkan petunjuk tentang cara melanjutkannya dengan risiko Anda sendiri.", "reasons": "Ada berbagai alasan mengapa situs dapat muncul di daftar peringatan kami, dan daftar peringatan kami dikompilasi dari daftar industri lain yang banyak digunakan. Alasan tersebut dapat mencakup penipuan atau risiko keamanan yang diketahui, seperti domain yang dites positif pada", "list_content": "Domain pada daftar peringatan ini dapat meliputi situs web berbahaya dan situs web sah yang telah disusupi oleh pelaku kejahatan.", "to_read_more": "Untuk membaca selengkapnya seputar situs ini", "review_on_etherscam": "tinjau domain di Etherscan.", "warning": "<PERSON><PERSON> diperhatikan bahwa daftar peringatan ini disusun secara sukarela. Daftar ini mungkin tidak akurat atau tidak lengkap. <PERSON><PERSON> karena domain tidak muncul dalam daftar ini bukan jaminan implisit dari keamanan domain tersebut. <PERSON><PERSON><PERSON> biasa, transaksi Anda merupakan tanggung jawab Anda sendiri. Ji<PERSON> Anda ingin berinteraksi dengan domain yang tertera dalam daftar peringatan kami, <PERSON><PERSON> dapat melakukannya dengan", "continue_on_your_own": "melanjutkan dan menanggung risikonya sendiri.", "file_an_issue_intro": "Jika menurut Anda domain ini salah ditandai atau jika situs web sah yang diblokir telah menyelesaikan masalah keamanannya,", "file_an_issue": "silakan ajukan masalah ini.", "back_to_safety": "<PERSON><PERSON><PERSON> ke keamanan", "site_might_be_harmful": "Situs web ini mungkin berbahaya", "metamask_flagged_site": "MetaMask menandai situs yang Anda coba kunjungi sebagai situs yang berpotensi menipu. Penyerang dapat menipu Anda agar melakukan sesuatu yang berbahaya.", "you_may_proceed_anyway": "<PERSON>a dapat juga", "proceed_anyway": "tetap lan<PERSON>", "but_please_do_so_at_your_own_risk": "tetapi dengan risiko yang ditanggung sendiri.", "report_detection_problem": "Laporkan masalah deteksi", "share_on_twitter": "Jika Anda merasa ini berman<PERSON>at, bagikan di X!", "share_text": "MetaMask melindungi saya agar tidak mengunjungi situs yang berpotensi membahayakan: {{url}}. Tetap aman!"}, "notifications": {"timeout": "<PERSON><PERSON><PERSON> habis", "no_date": "Tidak dikenal", "yesterday": "<PERSON><PERSON><PERSON>", "staked": "Di-stake", "received": "Diterima", "unstaked": "<PERSON><PERSON>", "mark_all_as_read": "Tandai semua telah dibaca", "to": "<PERSON>", "rate": "<PERSON><PERSON><PERSON> (termasuk biaya)", "unstaking_requested": "Permintaan pembatalan stake", "stake_completed": "Stake selesai", "withdrawal_completed": "Penarikan se<PERSON>ai", "unstake_completed": "Pembatalan stake selesai", "withdrawal_requested": "Permintaan penarikan", "stake_ready_to_be_withdrawn": "Stake siap untuk ditarik", "swap_completed": "Swap {{from}} dengan {{to}}", "swap": "<PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON> ke {{address}}", "menu_item_title": {"metamask_swap_completed": "<PERSON><PERSON><PERSON> {{symbolIn}} dengan {{symbolOut}}", "erc20_sent": "<PERSON><PERSON><PERSON> ke {{address}}", "erc20_received": "<PERSON><PERSON><PERSON> dari {{address}}", "eth_sent": "<PERSON><PERSON><PERSON> ke {{address}}", "eth_received": "<PERSON><PERSON><PERSON> dari {{address}}", "erc721_sent": "Mengirim NFT ke {{address}}", "erc1155_sent": "Mengirim NFT ke {{address}}", "erc721_received": "Menerima NFT dari {{address}}", "erc1155_received": "Menerima NFT dari {{address}}", "rocketpool_stake_completed": "Di-stake", "rocketpool_unstake_completed": "Pembatalan stake selesai", "lido_stake_completed": "Di-stake", "lido_withdrawal_requested": "Permintaan pembatalan stake", "lido_withdrawal_completed": "Pembatalan stake selesai", "lido_stake_ready_to_be_withdrawn": "Permintaan penarikan"}, "menu_item_description": {"lido_withdrawal_requested": "<PERSON><PERSON><PERSON><PERSON> Anda untuk membatalkan stake {{amount}} {{symbol}} telah dikirim", "lido_stake_ready_to_be_withdrawn": "<PERSON><PERSON> dapat menarik stake {{symbol}} yang <PERSON>an"}, "modal": {"title_sent": "Mengirim {{symbol}}", "title_received": "<PERSON><PERSON><PERSON> {{symbol}}", "title_unstake_requested": "Permintaan pembatalan stake", "title_untake_ready": "Penarikan siap", "title_stake": "Men-stake {{symbol}}", "title_unstake_completed": "Pembatalan stake selesai", "title_swapped": "<PERSON><PERSON><PERSON> {{symbolIn}} dengan {{symbolOut}}", "label_address_to": "<PERSON>", "label_address_from": "<PERSON><PERSON>", "label_address_to_you": "Untuk (Anda)", "label_address_from_you": "<PERSON><PERSON> (Anda)", "label_asset": "<PERSON><PERSON>", "label_account": "<PERSON><PERSON><PERSON>", "label_unstaking_requested": "Permintaan Pembatalan Stake", "label_unstake_ready": "Penarikan siap", "label_staked": "Di-stake", "label_received": "Diterima", "label_unstaking_confirmed": "Pembatalan Stake Dikonfirmasi", "label_swapped": "<PERSON><PERSON><PERSON>", "label_to": "<PERSON>"}, "received_from": "<PERSON><PERSON><PERSON> {{amount}} {{ticker}} dari {{address}}", "nft_sent": "Mengirim NFT ke {{address}}", "erc721_sent": "Mengirim NFT ke {{address}}", "erc1155_sent": "Mengirim NFT ke {{address}}", "erc721_received": "Menerima NFT dari {{address}}", "erc1155_received": "Menerima NFT dari {{address}}", "received_nft": "Menerima NFT dari {{address}}", "pending_title": "Transaksi di<PERSON>", "pending_deposit_title": "Deposit sedang diproses!", "pending_withdrawal_title": "Penarikan sedang diproses!", "cancelled_title": "Transaksi dibatalkan!", "success_title": "Transaksi #{{nonce}} Selesai!", "speedup_title": "Mempercepat #{{nonce}}!", "success_deposit_title": "Deposit Selesai!", "success_withdrawal_title": "Penarikan Selesai!", "error_title": "Ups! <PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han :/", "received_title": "<PERSON><PERSON> men<PERSON> {{amount}} {{assetType}}", "metamask_swap_completed_title": "<PERSON><PERSON><PERSON>", "erc20_sent_title": "<PERSON> terkirim", "erc20_received_title": "<PERSON>", "eth_sent_title": "<PERSON> terkirim", "eth_received_title": "<PERSON>", "rocketpool_stake_completed_title": "Stake selesai", "rocketpool_unstake_completed_title": "Pembatalan stake selesai", "lido_stake_completed_title": "Stake selesai", "lido_withdrawal_requested_title": "Permintaan penarikan", "lido_withdrawal_completed_title": "Penarikan se<PERSON>ai", "lido_stake_ready_to_be_withdrawn_title": "Stake siap untuk penarikan", "erc721_sent_title": "NFT terkirim", "erc721_received_title": "NFT diterima", "erc1155_sent_title": "NFT terkirim", "erc1155_received_title": "NFT diterima", "default_message_title": "MetaMask", "default_message_description": "Ketuk untuk melihat", "received_payment_title": "Pembayaran instan diterima", "pending_message": "<PERSON><PERSON><PERSON> konfi<PERSON><PERSON>", "pending_deposit_message": "Menunggu deposit selesai", "pending_withdrawal_message": "<PERSON>unggu penarikan selesai", "error_message": "Ketuk untuk melihat transaksi ini", "error_retrieving_fcm_token": "Error while getting and saving FCM token", "error_fcm_not_found": "getFCMToken: No FCM token found", "error_checking_permission": "Error checking if a user has push notifications permission", "success_message": "Ketuk untuk melihat transaksi ini", "speedup_message": "Mencoba untuk mempercepat transaksi", "success_deposit_message": "<PERSON> sudah siap digunakan", "success_withdrawal_message": "Dana telah dipindahkan ke dompet Anda", "cancelled_message": "Ketuk untuk melihat transaksi ini", "received_message": "Ketuk untuk melihat transaksi ini", "received_payment_message": "<PERSON><PERSON> men<PERSON> {{amount}} DAI", "eth_received_message": "<PERSON>a menerima beberapa ETH", "metamask_swap_completed_message": "<PERSON><PERSON><PERSON> {{symbolIn}} dengan {{symbolOut}}", "erc20_sent_message": "<PERSON><PERSON><PERSON> ke {{address}}", "erc20_received_message": "<PERSON><PERSON><PERSON> dari {{address}}", "eth_sent_message": "<PERSON><PERSON><PERSON> ke {{address}}", "rocketpool_stake_completed_message": "Di-stake", "rocketpool_unstake_completed_message": "Pembatalan stake selesai", "lido_stake_completed_message": "Di-stake", "lido_withdrawal_requested_message": "Permintaan pembatalan stake", "lido_withdrawal_completed_message": "Pembatalan stake selesai", "lido_stake_ready_to_be_withdrawn_message": "Permintaan penarikan", "push_notification_content": {"funds_sent_title": "<PERSON> terkirim", "funds_sent_description": "Anda ber<PERSON><PERSON> men<PERSON>m {{amount}} {{symbol}}", "funds_sent_default_description": "<PERSON>a ber<PERSON><PERSON> men<PERSON>m beberapa <PERSON>", "funds_received_title": "<PERSON>", "funds_received_description": "Anda men<PERSON> {{amount}} {{symbol}}", "funds_received_default_description": "<PERSON>a men<PERSON> be<PERSON>", "metamask_swap_completed_title": "<PERSON><PERSON><PERSON>", "metamask_swap_completed_description": "Swap MetaMask berhasil", "nft_sent_title": "NFT terkirim", "nft_sent_description": "<PERSON>a telah ber<PERSON>il mengirim NFT", "nft_received_title": "NFT diterima", "nft_received_description": "<PERSON><PERSON> menerima NFT baru", "rocketpool_stake_completed_title": "Stake selesai", "rocketpool_stake_completed_description": "Stake RocketPool berhasil", "rocketpool_unstake_completed_title": "Pembatalan stake selesai", "rocketpool_unstake_completed_description": "Pembatalan stake RocketPool berhasil", "lido_stake_completed_title": "Stake selesai", "lido_stake_completed_description": "Stake <PERSON> be<PERSON>", "lido_stake_ready_to_be_withdrawn_title": "Stake siap untuk penarikan", "lido_stake_ready_to_be_withdrawn_description": "Stake Lido kini siap untuk ditarik", "lido_withdrawal_requested_title": "Permintaan penarikan", "lido_withdrawal_requested_description": "Permintaan penarikan Lido telah di<PERSON>rimkan", "lido_withdrawal_completed_title": "Penarikan se<PERSON>ai", "lido_withdrawal_completed_description": "Penarikan <PERSON>", "perps_position_liquidated_title": "Posisi dilikuidasi", "perps_position_liquidated_description_long": "Long {{symbol}} telah ditutup.", "perps_position_liquidated_description_short": "Short {{symbol}} telah ditutup.", "perps_stop_loss_triggered_title": "Stop loss terpicu", "perps_stop_loss_triggered_description_long": "Long {{symbol}} ditutup pada stop loss Anda.", "perps_stop_loss_triggered_description_short": "Short {{symbol}} ditutup pada stop loss Anda.", "perps_take_profit_triggered_title": "Take profit terpicu", "perps_take_profit_triggered_description_long": "Long {{symbol}} ditutup pada take profit Anda.", "perps_take_profit_triggered_description_short": "Short {{symbol}} ditutup pada take profit Anda.", "perps_limit_order_filled_title": "Order limit terpenuhi", "perps_limit_order_filled_description_long": "Posisi long {{symbol}} kini dibuka.", "perps_limit_order_filled_description_short": "Posisi short {{symbol}} kini dibuka."}, "prompt_title": "<PERSON><PERSON>", "notifications_enabled_error_title": "<PERSON><PERSON><PERSON><PERSON>", "notifications_enabled_error_desc": "<PERSON>mi tidak dapat mengaktifkan notifikasi. Coba lagi nanti.", "prompt_desc": "Aktifkan notifikasi dari Pengaturan untuk mendapatkan peringatan penting tentang aktivitas dompet dan banyak lagi.", "prompt_ok": "Aktifkan", "prompt_cancel": "<PERSON><PERSON> saja", "wc_connected_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dengan {{title}}", "wc_signed_title": "Ditandatangani", "wc_sent_tx_title": "Mengirim transaksi", "wc_connected_rejected_title": "<PERSON>a telah menolak permintaan koneksi", "wc_signed_rejected_title": "<PERSON>a telah menolak permintaan tanda tangan", "wc_signed_failed_title": "<PERSON><PERSON><PERSON><PERSON> penandatanganan ini gagal", "wc_sent_tx_rejected_title": "<PERSON>a telah menolak permintaan trans<PERSON>i", "approved_tx_rejected_title": "<PERSON>a telah menolak member<PERSON>n izin", "wc_description": "<PERSON><PERSON> periksa permohonan ini", "wallet": "Dompet", "web3": "Web3", "staking_provider": "Penyedia Stake", "network_fee_not_available": "Biaya Jaringan tidak tersedia", "empty": {"title": "Tak ada apa pun di sini", "message": "Di sinilah Anda dapat menemukan notifikasi saat terjadi aktivitas di dompet. "}, "list": {"0": "<PERSON><PERSON><PERSON>", "1": "Dompet", "2": "<PERSON><PERSON><PERSON>"}, "copied_to_clipboard": "<PERSON><PERSON>in ke papan klip", "address_copied_to_clipboard": "<PERSON><PERSON><PERSON> disalin ke papan klip", "transaction_id_copied_to_clipboard": "ID Transaksi disalin ke papan klip", "activation_card": {"title": "Aktifkan notifikasi", "description_1": "Pantau terus segala perkembangan yang terjadi di dompet Anda dengan notifikasi.", "description_2": "Untuk menggunakan fitur ini, kami akan membuat ID anonim untuk akun Anda. Ini hanya digunakan untuk menyinkronkan data di MetaMask dan tidak terhubung dengan aktivitas atau pengidentifikasi lainnya, sehingga privasi tetap terjaga.", "learn_more": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda saat menggunakan fitur ini.", "manage_preferences_1": "Notifikasi dapat dinonaktifkan setiap saat ", "manage_preferences_2": "Pengaturan > Notifikasi.", "cancel": "<PERSON><PERSON>", "cta": "Aktifkan"}}, "protect_your_wallet_modal": {"title": "Lindungi dompet Anda", "body_for_password": "Lindungi dompet Anda dengan mengatur kata sandi dan menyi<PERSON>an <PERSON> (wajib).", "body_for_seedphrase": "<PERSON>ni nilai tersebut telah ditambahkan ke dompet Anda, lindungi dompet dengan mengatur kata sandi dan menyimpan <PERSON><PERSON> (wajib).", "button": "Lindungi dompet"}, "transaction_update_retry_modal": {"title": "Pembaruan Transaksi Gagal", "text": "<PERSON><PERSON><PERSON><PERSON> Anda ingin mencobanya lagi?", "cancel_button": "<PERSON><PERSON>", "retry_button": "Coba lagi"}, "payment_request": {"title": "Minta", "search_top_picks": "<PERSON><PERSON><PERSON> teratas", "search_assets": "<PERSON><PERSON> aset", "search_results": "<PERSON><PERSON>", "search_no_tokens_found": "Tidak ada token yang di<PERSON>ukan", "your_tokens": "To<PERSON>", "enter_amount": "<PERSON><PERSON><PERSON> j<PERSON>", "choose_asset": "<PERSON><PERSON><PERSON> aset untuk meminta", "request_error": "Permintaan tidak valid, harap coba lagi", "reset": "Reset", "next": "Selanjutnya", "amount_placeholder": "0,00", "link_copied": "Tautan disalin ke papan klip", "send_link_title": "<PERSON><PERSON>", "description_1": "<PERSON>tan permintaan Anda siap di<PERSON>rim!", "description_2": "<PERSON><PERSON><PERSON> tautan ini kepada teman, dan ini akan meminta mereka untuk mengirim", "copy_to_clipboard": "<PERSON>in ke papan klip", "qr_code": "KODE QR", "send_link": "<PERSON><PERSON>", "request_qr_code": "Kode QR Permintaan Pembayaran", "balance": "<PERSON><PERSON>"}, "receive_request": {"title": "Terima", "share_title": "Bagikan Alamat", "share_description": "<PERSON><PERSON> email atau pesan teks", "qr_code_title": "KODE QR", "qr_code_description": "<PERSON><PERSON><PERSON> yang dapat dipindai yang bisa membaca alamat <PERSON>a", "request_title": "Minta", "request_description": "<PERSON>a aset dari teman", "buy_title": "Bel<PERSON>", "buy_description": "Beli kripto dengan kartu debit atau transfer bank", "public_address": "<PERSON><PERSON><PERSON>", "public_address_qr_code": "<PERSON><PERSON><PERSON>", "coming_soon": "<PERSON><PERSON><PERSON> hadir...", "request_payment": "<PERSON><PERSON>", "copy": "<PERSON><PERSON>", "scan_address": "<PERSON><PERSON>i alamat untuk menerima pembay<PERSON>", "copy_address": "<PERSON><PERSON>"}, "experimental_settings": {"wallet_connect_dapps": "Sesi WalletConnect", "wallet_connect_dapps_desc": "Lihat daftar sesi WalletConnect aktif", "wallet_connect_dapps_cta": "<PERSON><PERSON>", "network_not_supported": "Tidak mendukung jaringan saat ini", "select_provider": "<PERSON><PERSON><PERSON> penyedia favorit Anda", "switch_network": "Harap beralih ke mainnet atau sepolia"}, "walletconnect_sessions": {"no_active_sessions": "Anda tidak memiliki sesi aktif", "end_session_title": "<PERSON><PERSON><PERSON>", "end": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "session_ended_title": "<PERSON><PERSON>", "session_ended_desc": "<PERSON>si yang dipilih telah dihentikan", "session_already_exist": "Sesi ini sudah terhubung.", "close_current_session": "Tutup sesi saat ini sebelum memulai yang baru."}, "paymentRequest": {"title": "PERMINTAAN PEMBAYARAN", "title_complete": "PEMBAYARAN SELESAI", "confirm": "BAYAR", "cancel": "TOLAK", "is_requesting_you_to_pay": "meminta Anda untuk membayar", "total": "TOTAL:"}, "webview_error": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON>mi tidak dapat memuat halaman tersebut", "return_home": "<PERSON><PERSON><PERSON> ke halaman beranda"}, "offline_mode": {"title": "Anda sedang offline", "text": "Tidak dapat terhubung ke hosting blockchain.", "try_again": "Coba lagi", "learn_more": "<PERSON><PERSON><PERSON><PERSON>"}, "walletconnect_return_modal": {"title": "Semua sudah siap!", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> dapat kembali ke browser Anda"}, "account_bar": {"depositing_to": "Menyetorkan ke:"}, "fiat_on_ramp": {"buy_eth": "Beli ETH", "buy": "Beli {{ticker}}", "purchased_currency": "Membeli {{currency}}", "network_not_supported": "Tidak mendukung jaringan saat ini", "switch_network": "Beralih ke Mainnet", "switch": "<PERSON><PERSON><PERSON>", "purchases": "Pembelian", "purchase_method": "<PERSON><PERSON>", "amount_to_buy": "<PERSON><PERSON><PERSON> yang harus dibeli", "transak_webview_title": "Transak", "moonpay_webview_title": "MoonPay", "wyre_user_agreement": "<PERSON><PERSON><PERSON><PERSON>", "wyre_terms_of_service": "<PERSON><PERSON><PERSON><PERSON>", "best_deal": "Penawaran terbaik", "purchase_method_title": {"wyre_first_line": "Biaya 0% saat Anda menggunakan", "wyre_second_line": "Apple Pay.", "wyre_sub_header": "Berlaku hingga 1 Juli 2020", "first_line": "Bagaimana Anda ingin men<PERSON>", "second_line": "pembelian ini?"}, "buy_ticker": "Beli {{ticker}}", "buy_ticker_stablecoins": "Beli {{ticker}} dan Stablecoin", "multiple_payment_methods": "Beberapa Metode Pembayaran", "debit_credit_bank_transfers_country": "Debit/kredit dan transfer bank berdasarkan negara.", "debit_credit_bank_transfers_more_country": "Debit/kredit, transfer bank, dan metode lainnya berdasarkan negara.", "options_fees_vary": "100+ negara, biaya dan batasan bervariasi", "moonpay_options_fees_vary": "145+ ne<PERSON>, biaya dan batasan berva<PERSON>si", "some_states_excluded": "Beberapa negara bagian dikecualikan", "purchase_method_modal_close": "<PERSON><PERSON><PERSON>", "transak_cta": "Beli ETH melalui Transak", "transak_cta_ticker": "<PERSON>i {{ticker}} me<PERSON><PERSON>ak", "apple_pay": "Apple Pay", "via": "me<PERSON>ui", "fee": "biaya", "Fee": "Biaya", "limited_time": "waktu terbatas", "supported_countries": "<PERSON>eg<PERSON> yang didukung", "no_countries_result": "Tidak ada negara yang didukung yang cocok dengan “{{searchString}}”", "wyre_loading_rates": " ", "fast_no_registration": "Cepat - Tak perlu mendaftar", "debit_credit_card_required": "Kartu Debit atau Kredit. Tidak mendukung Apple Cash.", "select_card_country": "<PERSON><PERSON>h negara tempat kartu Anda terdaftar (bukan lokasi Anda saat ini).", "search_country": "<PERSON><PERSON> negara", "wyre_countries": "55+ <PERSON><PERSON>, biaya dan batasan berva<PERSON>si", "wyre_fees_us": "2,9% + $0,30 + biaya gas (biaya minimum $5)", "wyre_fees_us_fee": "Biaya 2,9% + $0,30 + gas (minimum $5)", "wyre_fees_outside_us_fee": "Biaya 3,9% + $0,30 + gas (minimum $5)", "wyre_estimated": "Estimasi {{amount}} {{currency}}", "wyre_modal_text": "<PERSON>mba<PERSON> dengan <PERSON>, yang dipersemba<PERSON><PERSON> o<PERSON>h <PERSON> didukung di Amerika Serikat 🇺🇸 kecuali untuk CT, HI, NC, NH, NY, VA, dan VT.", "wyre_minimum_deposit": "Deposit minimum adalah {{amount}}", "wyre_maximum_deposit": "Deposit maksimum adalah {{amount}}", "apple_pay_purchase": "Pembelian {{currency}}", "apple_pay_provider_total_label": "{{provider}} (melalui MetaMask)", "buy_with": "<PERSON><PERSON>", "plus_fee": "Ditambah biaya {{fee}}", "date": "Tanggal", "from": "<PERSON><PERSON>", "to": "<PERSON>", "status": "Status", "completed": "Se<PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "failed": "Gaga<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "total_amount": "<PERSON><PERSON><PERSON> total", "gas_education_carousel": {"step_1": {"title": "Sebelum Anda membeli {{ticker}}, pahami biaya gas", "average_gas_fee": "Biaya gas rata-rata saat ini:", "subtitle_1": "Jika Anda berencana untuk menukar atau mentransfer {{ticker}}, beli lebih banyak untuk menutupi biaya gas.", "subtitle_2": "Biaya gas untuk transaksi tidak terkait dengan biaya pembelian {{ticker}}.", "subtitle_3": "MetaMask tidak mengambil keuntungan dari biaya gas."}, "step_2": {"title": "Apa itu biaya gas?", "subtitle_1": "Gas mengatur transaksi di jaringan Ethereum. Ini merupakan biaya yang dibay<PERSON>an dalam {{ticker}} kepada penambang kripto yang memproses transaksi Anda.", "subtitle_2": "MetaMask tidak mengambil keuntungan dari biaya gas.", "learn_more": "Pelajari selengkapnya seputar biaya gas"}, "step_3": {"title": "<PERSON><PERSON>a banyak yang saya butuhkan?", "subtitle_1": "Biaya gas berfluktuasi berdasarkan lalu lintas jaringan dan jenis trans<PERSON>i.", "subtitle_2": "Transaksi yang rumit se<PERSON>i “menukar” mungkin memerlukan biaya 5x - 10x lebih mahal dibandingkan transaksi “kirim”.", "subtitle_3": "Cara terbaik untuk memperkirakan biaya gas adalah dengan", "subtitle_4": "mencoba bertransaksi terlebih dulu", "subtitle_5": "dan lihat berapa biaya gas yang diperlukan.", "cta": "Lanjutkan untuk membeli {{ticker}}"}}}, "fiat_on_ramp_aggregator": {"buy": "beli", "sell": "jual", "orders": "Transfers", "All": "<PERSON><PERSON><PERSON>", "Buy": "Bel<PERSON>", "Sell": "<PERSON><PERSON>", "token_marketplace": "Token marketplace", "Purchased": "<PERSON><PERSON><PERSON>", "Sold": "<PERSON><PERSON><PERSON><PERSON>", "empty_orders_list": "No bank or card transfers yet.", "empty_buy_orders_list": "<PERSON>a tidak memiliki pesanan pem<PERSON>ian", "empty_sell_orders_list": "Anda tidak memiliki pesanan pen<PERSON>", "purchased_currency": "Membeli {{currency}}", "sold_currency": "{{currency}} terjual", "order_status_pending": "<PERSON><PERSON><PERSON>", "order_status_processing": "Memproses", "order_status_completed": "Se<PERSON><PERSON>", "order_status_failed": "Gaga<PERSON>", "order_status_cancelled": "Di<PERSON><PERSON><PERSON>", "network_switcher": {"title": "<PERSON><PERSON><PERSON> {{rampType}} tidak didukung", "description": "Untuk {{rampType}} k<PERSON><PERSON>, <PERSON>a harus beralih ke jaringan yang didukung", "no_networks_found": "<PERSON><PERSON><PERSON> yang didukung tidak ditemukan"}, "onboarding": {"what_to_expect": "<PERSON>", "quotes": "Fitur beli kripto kami mengumpulkan kuotasi dari vendor terintegrasi, menawarkan kuotasi dari sumber tersebut agar bisa mendapatkan kripto langsung ke dompet Anda tanpa masa tunggu.", "quotes_sell": "Kini Anda dapat langsung mencairkan uang tunai di MetaMask! Dapatkan penawaran terkini dari penyedia tepercaya selagi kami memberikan panduan langkah demi langkah.", "benefits": "Biaya gas yang harus dibayar semakin sedikit, dan ada banyak jaringan, token, serta metode pembayaran yang didukung", "benefits_sell": "Pertukaran dari kripto ke mata uang lokal kini semakin mudah.", "get_started": "<PERSON><PERSON>"}, "payment_method": {"payment_method": "<PERSON><PERSON>", "cash_destination": "<PERSON><PERSON><PERSON>", "instant": "Instan", "less_than": "<PERSON><PERSON> dari", "minute": "mnt", "minutes": "menit", "hour": "jam", "hours": "jam", "business_day": "hari kerja", "business_days": "hari kerja", "lowest_limit": "batas pembelian terendah", "medium_limit": "batas pembelian medium", "highest_limit": "batas pembelian tertinggi", "lowest_sell_limit": "batas penjualan terendah", "medium_sell_limit": "batas penjualan medium", "highest_sell_limit": "batas penjualan tertinggi", "continue_to_amount": "Lanjutkan ke jumlah", "no_payment_methods_title": "Tidak ada Metode Pembayaran di {{regionName}}", "no_cash_destinations_title": "Tidak Ada Destinasi Tunai di {{regionName}}", "no_payment_methods_description": "Saat ini tidak ada metode pembayaran yang didukung di wilayah Anda. <PERSON>hon periksa kembali secepatnya; kami sering melakukan ekspansi dukungan ke wilayah baru.\n\nJika Anda tidak sengaja memilih {{regionName}}, klik tombol di bawah untuk mereset wilayah Anda.", "no_cash_destinations_description": "Saat ini tidak ada destinasi tunai yang didukung di wilayah Anda. <PERSON>iksa kembali secepatnya; kami memperluas dukungan ke wilayah baru secara berkala.\n\nJika tidak sengaja memilih {{regionName}}, klik tombol di bawah untuk mereset wilayah Anda.", "reset_region": "<PERSON><PERSON>"}, "continue": "Lanjutkan", "new_quotes_in": "Kuotasi baru di", "fetching_new_quotes": "Mengambil kuotasi baru...", "quotes_expire_in": "<PERSON><PERSON><PERSON> be<PERSON> dalam", "get_new_quotes": "Dapatkan kuotasi baru", "explore_more_options": "<PERSON><PERSON><PERSON><PERSON> opsi lain", "one_more_option": "1 opsi lain", "more_options": "{{count}} opsi lain", "previously_used": "Digunakan sebelumnya", "best_rate": "<PERSON><PERSON><PERSON>", "most_reliable": "<PERSON><PERSON> dapat <PERSON>", "quotes_timeout": "<PERSON><PERSON>tu kuotasi habis", "request_new_quotes": "Minta kuotasi baru untuk mendapatkan tarif terbaik saat ini.", "terms_of_service": "<PERSON><PERSON><PERSON><PERSON>", "amount_to_buy": "<PERSON><PERSON><PERSON> yang harus dibeli", "amount_to_sell": "<PERSON><PERSON><PERSON> yang akan dijual", "want_to_buy": "Anda ingin membeli", "want_to_sell": "<PERSON>a ingin menjual", "current_balance": "Saldo saat ini", "amount": "<PERSON><PERSON><PERSON>", "send_cash_to": "<PERSON><PERSON> uang tunai ke", "get_quotes": "Dapatkan kuotasi", "done": "Se<PERSON><PERSON>", "fetching_quotes": "Mengambil kuotasi", "select_a_quote": "<PERSON><PERSON><PERSON>", "recommended_quote": "<PERSON><PERSON><PERSON> yang <PERSON>n", "select_a_cryptocurrency": "<PERSON><PERSON><PERSON> mata uang kripto", "select_a_cryptocurrency_description": "Select from the list of tokens available.", "search_by_cryptocurrency": "<PERSON>i berda<PERSON>kan mata uang kripto", "search_by_currency": "<PERSON>i mata uang", "update_payment_method": "<PERSON><PERSON><PERSON> metode pem<PERSON>", "select_payment_method": "<PERSON><PERSON><PERSON> metode pembayaran", "select_cash_destination": "<PERSON><PERSON>h ke mana uang tunai akan dikirim", "select_region_currency": "<PERSON><PERSON><PERSON> W<PERSON>", "no_tokens_match": "Tidak ada token yang cocok dengan “{{searchString}}”", "no_currency_match": "Tidak ada mata uang yang cocok dengan “{{searchString}}”", "compare_rates": "Bandingkan tarif dari penyedia ini. Kuotasi diurutkan berdasarkan harga keseluruhan.", "pay_with": "<PERSON><PERSON>", "continue_with": "<PERSON><PERSON><PERSON><PERSON><PERSON> melal<PERSON> {{provider}}", "minimum": "Deposit minimum adalah", "maximum": "Deposit maksimum adalah", "insufficient_balance": "<PERSON><PERSON><PERSON> ini lebih tinggi dari saldo <PERSON>", "insufficient_native_balance": "{{currency}} tidak cukup untuk menutup biaya gas.", "enter_larger_amount": "<PERSON><PERSON><PERSON><PERSON> jumlah yang lebih besar untuk melanjutkan", "enter_smaller_amount": "<PERSON><PERSON><PERSON><PERSON> jumlah yang lebih kecil untuk melanjutkan", "enter_lower_gas_fees": "<PERSON><PERSON><PERSON><PERSON> jumlah yang lebih rendah untuk membayar biaya gas", "max": "MAKS", "try_again": "Coba lagi", "error": "<PERSON><PERSON><PERSON>", "something_went_wrong": "<PERSON>s, terja<PERSON> k<PERSON>han", "report_this_issue": "Laporkan masalah ini", "no_providers_available": "Penyedia tidak tersedia", "try_different_amount_to_buy_input": "Coba pilih metode pembayaran lain atau coba tambahkan atau kurangi jumlah yang ingin Anda beli!", "try_different_amount_to_sell_input": "Coba pilih destinasi pencairan uang tunai yang berbeda atau coba tambah atau kurangi jumlah yang ingin Anda jual!", "webview_received_error": "Kode status kes<PERSON><PERSON> peneri<PERSON>an WebView: {{code}}", "no_tokens_available_title": "Token Tidak Tersedia", "no_tokens_available": "Saat ini tidak ada token yang tersedia untuk dibeli di {{network}} dengan metode pembayaran yang dipilih.", "no_sell_tokens_available": "Saat ini tidak ada token yang tersedia untuk dijual di {{network}} dengan destinasi tunai yang dipilih.", "this_network": "jaringan ini", "change_payment_method": "Ubah metode pembayaran", "change_cash_destination": "Ubah destinasi tunai", "try_different_region": "Coba wilayah lain", "return_home": "Ke<PERSON><PERSON> ke <PERSON>ar Be<PERSON>a", "region": {"buy_crypto_tokens": "Beli To<PERSON>", "sell_crypto_tokens": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "Metode pembayaran dan token yang tersedia untuk Anda ditentukan oleh integrasi pihak ketiga kami dan dapat bervariasi bergantung wilayah Anda serta dukungan integrasi kami.", "sell_description": "Opsi destinasi tunai dan token dapat berbeda tergantung wilayah Anda.", "search_by_country": "<PERSON><PERSON> be<PERSON> negara", "search_by_state": "Cari berdasarkan state", "no_region_results": "Wilayah tidak cocok", "your_region": "Wilayah Anda", "select_region": "<PERSON><PERSON><PERSON> wilayah <PERSON>", "select_region_title": "<PERSON><PERSON><PERSON>", "select_country_registered": "<PERSON><PERSON>h negara tempat kartu Anda terdaftar (terlepas dari lokasi Anda saat ini).", "unsupported": "Wilayah Tidak Did<PERSON>ng", "unsupported_description": "<PERSON><PERSON> be<PERSON>ja keras untuk memperluas cakupan ke wilayah Anda sesegera mungkin. <PERSON><PERSON><PERSON>, lihat artikel dukungan kami untuk mengetahui cara lain yang dapat Anda lakukan untuk {{rampType}} kripto.", "unsupported_link": "<PERSON><PERSON>jungi <PERSON>", "popular_regions": "Wilayah Populer", "regions": "Wilayah"}, "order_details": {"details_main": "Detail Order", "successful": "<PERSON><PERSON><PERSON>!", "your": "<PERSON><PERSON>", "available_in_account": "kini tersedia di akun <PERSON>a", "delayed_bank_transfer": "Diperlukan waktu beberapa hari untuk melihat uang tunai di rekening bank Anda.", "crypto": "kripto", "failed": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "failed_description": "<PERSON><PERSON><PERSON><PERSON>, dan {{provider}} tidak dapat menyelesaikan pesanan <PERSON>. Silakan coba lagi atau dengan penyedia lain.", "continue_order_description": "Untuk melanju<PERSON>kan pesanan, pilih tombol di bagian bawah halaman ini.", "the_provider": "penyedia", "processing": "<PERSON><PERSON><PERSON><PERSON>", "processing_card_description": "Pembelian Kredit/Debit umumnya memerlukan waktu beberapa menit", "processing_bank_description": "Transfer bank umumnya memerlukan waktu beberapa hari kerja", "details": "<PERSON>ail pesanan", "via": "me<PERSON>ui", "purchase_amount": "<PERSON><PERSON><PERSON>", "amount_received_total": "Jumlah Total yang Diterima", "etherscan": "Lihat detail lengkapnya di", "start_new_order": "<PERSON><PERSON> pesanan baru", "continue_order": "Lanjutkan pesanan ini", "questions": "<PERSON> per<PERSON>aan?", "contact_support": "Hubungi <PERSON>", "support": "Dukungan", "view_order_status": "Lihat status pesanan di {{provider}}", "id": "ID Order", "date_and_time": "<PERSON><PERSON> dan <PERSON>", "payment_method": "<PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "token_amount": "<PERSON><PERSON><PERSON>", "token_quantity_sold": "<PERSON><PERSON><PERSON>", "exchange_rate": "<PERSON><PERSON>", "total_fees": "Biaya Total", "amount": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>", "a_block_explorer": "block explorer"}, "send_transaction": {"sell_crypto": "<PERSON><PERSON>", "send_description": "Anda akan mengirim {{cryptocurrency}} ke {{provider}}, yang nantinya akan mengirimkan uang tunai ke {{paymentMethod}}", "next": "Berikutnya", "hold_to_send": "<PERSON><PERSON> untuk men<PERSON>m", "send": "<PERSON><PERSON>", "sent": "Mengirim!"}, "notifications": {"purchase_failed_title": "Pembelian {{currency}} gagal! Silakan coba lagi. <PERSON>hon maaf atas ketidaknyamanan yang timbul!", "purchase_failed_description": "Verifikasikan metode pembayaran dan dukungan kartu", "purchase_cancelled_title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "purchase_cancelled_description": "Verifikasikan metode pembayaran dan dukungan kartu", "purchase_completed_title": "Pembelian {{amount}} {{currency}} berhasil!", "purchase_completed_description": "{{currency}} Anda kini telah tersedia", "purchase_pending_title": "Memproses pembelian {{currency}} Anda", "purchase_pending_description": "<PERSON><PERSON> perlu beberapa menit...", "sale_failed_title": "<PERSON><PERSON><PERSON> gagal", "sale_failed_description": "Pesanan tidak dapat diselesaikan.", "sale_cancelled_title": "<PERSON><PERSON><PERSON>", "sale_cancelled_description": "Pesanan tidak dapat diselesaikan.", "sale_completed_title": "<PERSON><PERSON><PERSON>", "sale_completed_description": "<PERSON><PERSON><PERSON> berhasil!.", "sale_pending_title": "Pemrosesan penjualan {{currency}}", "sale_pending_description": "<PERSON><PERSON>an sedang diproses.", "no_date": "Tidak dikenal"}, "deposit_order_title": "Deposit {{currency}}"}, "swaps": {"title": "<PERSON><PERSON><PERSON>", "onboarding": {"get_the": "Dapatkan", "best_price": "harga terbaik", "from_the": "dari", "top_liquidity": "sumber likuiditas", "sources": "teratas.", "find_the": "Temukan", "best_swap": "pertukaran terbaik", "across": "di seluruh...", "want_to_learn_more": "Ingin mempelajari selengkapnya?", "learn_more": "Pelajari selengkapnya seputar MetaMask Swaps", "what_are": "Apa itu pertukaran token?", "review_audits": "Tinjau audit kontrak resmi kami", "start_swapping": "<PERSON><PERSON>"}, "feature_off_title": "Sementara tidak tersedia", "feature_off_body": "MetaMask Swaps sedang menja<PERSON> pem<PERSON>. <PERSON><PERSON><PERSON> kembali lagi nanti.", "wrong_network_title": "Pertukaran tidak tersedia", "wrong_network_body": "Anda hanya dapat menukar token di Jaringan Utama Ethereum.", "unallowed_asset_title": "Tidak dapat menukar token ini", "unallowed_asset_body": "Beberapa token dengan mekanisme unik saat ini tidak mendukung pertukaran.", "convert_from": "Konversikan dari", "convert_to": "Konversikan ke", "verify": "Verifikas<PERSON><PERSON>", "verified_on_sources": "<PERSON><PERSON><PERSON><PERSON><PERSON>i pada {{sources} sumber.", "verify_on": "<PERSON><PERSON><PERSON> veri<PERSON><PERSON><PERSON>n alamat <PERSON> di", "verify_address_on": "V<PERSON><PERSON><PERSON><PERSON><PERSON> alamat <PERSON> di", "added_manually": "Token ini telah ditambahkan secara manual.", "verify_this_token_on": "Verifikasikan token ini di", "only_verified_on": "{{symbol}} hanya terveri<PERSON><PERSON>i pada {{occurrences}} sumber.", "block_explorer": "penjelajah blok", "a_block_explorer": "penjelajah blok", "make_sure_trade": "dan pastikan ini merupakan token yang ingin Anda perdagangkan.", "token_verification": "Verifi<PERSON><PERSON> token", "token_multiple": "Beberapa token dapat menggunakan nama dan simbol yang sama.", "token_check": "Periksa", "token_to_verify": "untuk memverifikasi bahwa ini merupakan token yang Anda cari.", "continue": "Lanjutkan", "select_a_token": "<PERSON><PERSON><PERSON> token", "search_token": "<PERSON><PERSON><PERSON><PERSON> nama token atau tempel alamat", "no_tokens_result": "Tidak ada token yang cocok dengan “{{searchString}}”", "find_token_address": "<PERSON><PERSON><PERSON> alamat token", "cant_find_token": "Tidak dapat menemukan token?", "manually_pasting": "Tambahkan token secara manual dengan menempelkan alamatnya.", "token_address_can_be_found": "<PERSON><PERSON><PERSON> k<PERSON> dapat di<PERSON>n di", "gathering_token_details": "<PERSON><PERSON><PERSON><PERSON><PERSON> detail token...", "error_gathering_token_details": "Ups, terjadi kes<PERSON>han saat mengumpulkan detail token.", "Import": "Impor", "invalid_token_contract_address": "<PERSON>amat kontrak token tidak valid.", "please_verify_on_explorer": "<PERSON><PERSON> veri<PERSON> di", "add_warning": "Siapa pun dapat membuat token, termasuk membuat versi palsu dari token yang ada yang mengeklaim sebagai perwakilan proyek.", "import_token": "Impor token?", "contract": "Kontrak:", "available_to_swap": "{{asset}} tersedia untuk ditukar.", "use_max": "Gunakan maks", "not_enough": "{{symbol}} tidak cukup untuk menyelesaikan pertukaran ini", "max_slippage": "<PERSON><PERSON>", "max_slippage_amount": "Selip maks {{slippage}}", "slippage_info": "Jika harga berubah antara waktu penempatan dan konfirmasi order Anda, ini disebut “selip”. Pertukaran akan otomatis dibatalkan jika selip melebihi pengaturan “selip maks”.", "slippage_warning": "Pastikan Anda merasa yakin dengan yang Anda lakukan!", "allows_up_to_decimals": "{{symbol}} memungkinkan hingga {{decimals}} desimal", "get_quotes": "Dapatkan kuotasi", "starting": "<PERSON><PERSON><PERSON>...", "fetching_quotes": "Mengambil kuotasi", "finalizing": "Menyelesaikan...", "quote": "<PERSON><PERSON><PERSON>", "of": "dari", "checking": "Memeriksa", "fetching_new_quotes": "Mengambil kuotasi baru...", "you_need": "<PERSON><PERSON>", "more_to_complete": "lebih banyak untuk menyelesaikan pertukaran ini.", "more_gas_to_complete": "lebih banyak gas untuk menyelesaikan pertukaran ini.", "token_marketplace": "Token marketplace", "market_price_unavailable_title": "<PERSON><PERSON><PERSON> tarif Anda sebelum melanju<PERSON>kan", "market_price_unavailable": "Harga pasar tidak tersedia sehingga dampak harga tidak diketahui. <PERSON><PERSON> verifikasikan bahwa Anda merasa nyaman dengan jumlah token yang akan Anda terima sebelum menukar.", "price_difference": "<PERSON><PERSON><PERSON> harga {{amount}}", "price_difference_title": "<PERSON><PERSON><PERSON> ha<PERSON>", "price_difference_body": "<PERSON><PERSON><PERSON> harga pasar dapat dipengaruhi oleh biaya yang diambil oleh per<PERSON>, ukuran pasar, ukuran perdagangan, atau inefisiensi pasar.", "price_impact_title": "<PERSON><PERSON><PERSON> harga", "price_impact_body": "Dampak harga merupakan selisih antara harga pasar saat ini dan jumlah yang diterima selama pelaksanaan transaksi. Dampak harga merupakan fungsi dari ukuran perdagangan yang relatif terhadap ukuran kumpulan likuiditas.", "quotes_update_often": "<PERSON><PERSON><PERSON> diper<PERSON><PERSON> terus-menerus", "quotes_update_often_text": "<PERSON><PERSON><PERSON> diperbarui terus-menerus untuk mencerminkan kondisi pasar saat ini.", "about_to_swap": "<PERSON><PERSON> akan <PERSON>", "for": "untuk", "new_quotes_in": "Kuotasi baru di", "i_understand": "<PERSON><PERSON>", "quotes_expire_in": "<PERSON><PERSON><PERSON> be<PERSON> dalam", "saving": "Menyimpan", "n_quotes": "{{numberOfQuotes}} kuotasi", "view_details": "Lihat detail", "estimated_gas_fee": "Estimasi biaya gas", "gas_fee": "Biaya gas", "included": "<PERSON><PERSON><PERSON>", "max_gas_fee": "Biaya gas maks", "edit": "Edit", "quotes_include_fee": "Kuotasi mencakup {{fee}}% biaya MetaMask", "quotes_include_gas_and_metamask_fee": "Kuotasi mencakup biaya gas dan biaya MetaMask sebesar {{fee}}%", "tap_to_swap": "Ketuk untuk Menukar", "swipe_to_swap": "Usap untuk <PERSON>kar", "swipe_to": "Usap untuk", "swap": "<PERSON><PERSON>", "completed_swap": "<PERSON><PERSON>!", "metamask_swap_fee": "Biaya MetaMask Swap", "fee_text": {"fee_is_applied": "Biaya sebesar {{fee}} otomatis diperhitungkan ke dalam kuotasi ini. Anda membayarnya sebagai ganti lisensi untuk menggunakan perangkat lunak pengumpul informasi penyedia likuiditas MetaMask.", "fee_is_not_applied": "MetaMask tidak menyertakan biaya tambahan dalam kuotasi ini."}, "enable": {"this_will": "<PERSON><PERSON> akan", "enable_asset": "memungkinkan {{asset}}", "for_swapping": "untuk ditukar", "edit_limit": "<PERSON> batas"}, "quotes_overview": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "quote_details": "Detail kuotasi", "receiving": "<PERSON><PERSON><PERSON>", "overall_value": "<PERSON><PERSON>", "best": "<PERSON><PERSON><PERSON>", "rate": "Terbaik", "quote_details_max_slippage": "<PERSON><PERSON>", "source": "Sumber", "estimated_network_fees": "Estimasi biaya gas", "guaranteed_amount": "<PERSON><PERSON><PERSON> yang dijamin", "quote_source_dex": {"1": "<PERSON><PERSON><PERSON> ini berasal dari", "2": "protokol pertukaran", "3": "terdesentralisasi."}, "quote_source_rfq": {"1": "<PERSON><PERSON><PERSON> ini berasal dari", "2": "pembuat pasar swasta", "3": "yang merespon dengan kuotasi secara langsung."}, "quote_source_agg": {"1": "<PERSON><PERSON><PERSON> ini berasal dari", "2": "agregator", "3": "yang membandingkan harga dan membagi order di antara berbagai bursa terdesentralisasi."}, "quote_source_cnt": {"1": "<PERSON><PERSON><PERSON> ini berasal dari", "2": "kontrak pintar", "3": "yang mengemas dan membuka token asli yang dapat menghemat biaya bahan gas."}, "quotes_timeout": "<PERSON><PERSON>tu kuotasi habis", "request_new_quotes": "Minta kuotasi baru untuk mendapatkan tarif terbaik saat ini.", "quotes_not_available": "Kuotasi tidak tersedia", "try_adjusting": "Sesuaikan jumlahnya dan coba lagi.", "error_fetching_quote": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengambil kuotasi", "unexpected_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON><PERSON> tidak terduga, minta kuotasi baru untuk mendapatkan harga terbaik saat ini. (kesalahan: {{error}})", "get_new_quotes": "Dapatkan kuotasi baru", "try_again": "Coba lagi", "terms_of_service": "<PERSON><PERSON><PERSON><PERSON> layanan", "transaction_label": {"swap": "<PERSON><PERSON> {{sourceToken}} ke {{destinationToken}}", "approve": "<PERSON><PERSON><PERSON><PERSON> {{sourceToken}} untuk ditukar: <PERSON>ng<PERSON> {{upTo}}"}, "notification_label": {"swap_pending": "Pertukaran Tertunda ({{sourceToken}} ke {{destinationToken}})", "swap_confirmed": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> ({{sourceToken}} ke {{destinationToken}})", "approve_pending": "Menyetujui {{sourceToken}} untuk ditukar", "approve_confirmed": "{{sourceToken}} di<PERSON><PERSON><PERSON><PERSON> untuk ditukar"}, "medium_selected_warning": "<PERSON><PERSON><PERSON> sensitif ter<PERSON><PERSON> waktu. “Sedang” tidak disarankan.", "high_recommendation": "<PERSON><PERSON><PERSON><PERSON> bias<PERSON>a sensitif ter<PERSON><PERSON> waktu. “Tinggi” akan membantu menghindari potensi kerugian akibat per<PERSON>han kondisi pasar.", "recommended": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "recommended_gas": "Biaya gas yang direkomendasikan", "gas_included_tooltip_explanation": "Kuotasi ini mencakup biaya gas dengan menyesuaikan jumlah token yang dikirim atau diterima. <PERSON>a mungkin akan menerima ETH dalam transaksi terpisah pada daftar aktivitas Anda.", "gas_included_tooltip_explanation_link_text": "Pelajari selengkapnya seputar biaya gas", "gas_education_title": "Estimasi biaya gas", "gas_education_1": "Biaya gas dibayarkan kepada penambang kripto yang memproses transaksi di", "gas_education_2_ethereum": "jaringan Ethereum.", "gas_education_2": "<PERSON>ingan.", "gas_education_3": "MetaMask tidak mengambil keuntungan dari biaya gas.", "gas_education_4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gas_education_5": "biaya gas”", "gas_education_6": "merupakan biaya aktual yang kami perkirakan. <PERSON><PERSON><PERSON> pastinya tergantung pada kondisi jaringan.", "gas_education_7": "“Biaya gas maks”", "gas_education_8": "merupakan jumlah terbesar yang akan Anda gunakan. Saat jaringan tidak stabil, ini bisa menjadi jumlah yang besar.", "gas_education_learn_more": "Pelajari selengkapnya seputar biaya gas"}, "protect_wallet_modal": {"title": "Lindungi dompet Anda", "top_button": "Lindungi dompet", "bottom_button": "Ingatkan saya nanti", "text": "<PERSON>an tunggu sampai dana <PERSON>a hilang. Lindungi dompet Anda dengan menyimpan Frasa Pemulihan Rahas<PERSON> di tempat yang aman.", "text_bold": "Ini merupakan satu-satunya cara untuk memulihkan dompet Anda jika aplikasi terkunci atau Anda memiliki perangkat baru.", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "deeplink": {"invalid": "<PERSON><PERSON><PERSON> tidak valid", "not_supported": "Tidak mendukung pranala"}, "error_screen": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Informasi Anda tidak dapat ditampilkan. <PERSON><PERSON>, dompet dan dana Anda tetap aman.", "try_again_button": "Coba lagi", "submit_ticket_1": "<PERSON><PERSON> laporkan masalah ini agar kami dapat memperbaikinya:", "submit_ticket_2": "<PERSON><PERSON> tang<PERSON>pan layar dari layar ini.", "submit_ticket_3": "<PERSON><PERSON>", "submit_ticket_4": "pesan kesalahan ke papan klip.", "submit_ticket_5": "<PERSON><PERSON><PERSON> tiket", "submit_ticket_6": "di sini.", "submit_ticket_7": "<PERSON><PERSON> sertakan pesan kesalahan dan tangkapan layar.", "submit_ticket_8": "<PERSON><PERSON><PERSON> laporan masalah", "submit_ticket_9": "Harap sertakan detail tentang hal yang terjadi.", "bug_report_prompt_title": "<PERSON><PERSON> tahu kami hal yang terjadi", "bug_report_prompt_description": "Tambahkan detail sehingga kami dapat mengetahui apa yang salah.", "bug_report_thanks": "Terima kasih! Kami akan segera memerik<PERSON>ya.", "save_seedphrase_1": "<PERSON><PERSON> kesalahan ini terus muncul,", "save_seedphrase_2": "<PERSON><PERSON><PERSON>", "save_seedphrase_3": "dan instal ulang aplik<PERSON>nya. Ingat: tan<PERSON>, <PERSON><PERSON> tidak dapat memuli<PERSON>kan dompet.", "copied_clipboard": "<PERSON><PERSON>in ke papan klip", "ok": "<PERSON>e", "cancel": "Batalkan", "send": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "modal_title": "<PERSON><PERSON><PERSON> yang terjadi", "modal_placeholder": "Berbagi detail seperti cara kami dapat mereproduksi bug akan membantu kami memperbaiki masalah tersebut.", "error_message": "<PERSON><PERSON> kes<PERSON>han:", "copy": "<PERSON><PERSON>", "describe": "<PERSON><PERSON><PERSON> yang terjadi", "try_again": "Coba lagi", "contact_support": "Hubungi dukungan"}, "whats_new": {"title": "What's New", "remove_gns_new_ui_update": {"title": "New UI update", "introduction": "We've made updates to improve the app experience.", "descriptions": {"description_1": "Network dropdown moved to your assets", "description_2": "Swap and Bridge in one simple flow", "description_3": "Streamlined Send experience", "description_4": "A fresh account view"}, "more_information": "Now you can focus on your tokens and activity, not the networks behind them.", "got_it": "<PERSON><PERSON><PERSON>"}}, "invalid_network": {"title": "ID chain untuk jaringan khusus \n %{network} \n harus dimasukkan kembali.", "message": "Untuk melindungi Anda dari penyedia jaringan yang berbahaya atau rusak, ID chain kini diperlukan untuk semua jaringan khusus.", "hint": "Anda dapat menemukan ID chain dari jaringan paling populer di", "edit_network_button": "<PERSON>", "cancel": "<PERSON><PERSON>"}, "switch_custom_network": {"title_existing_network": "Situs ini ingin beralih jaringan", "title_new_network": "Jaringan baru ditambahkan", "switch_warning": "Ini akan mengalihkan jaringan yang dipilih dalam MetaMask ke jaringan yang ditambahkan sebelumnya:", "add_network_and_give_dapp_permission_warning": "Anda menambahkan jaringan ini ke MetaMask dan memberikan {{dapp_origin}} izin untuk menggunakannya.", "update_network_and_give_dapp_permission_warning": "Anda memperbarui jaringan ini di MetaMask dan memberikan {{dapp_origin}} izin untuk menggunakannya.", "request_update_network_url": "{{dapp_origin}} meminta untuk memperbarui URL jaringan default. Anda dapat mengedit informasi jaringan default setiap saat.", "available": "kini tersedia di pemilih jaringan.", "cancel": "<PERSON><PERSON>", "switch": "<PERSON><PERSON><PERSON>"}, "add_custom_network": {"title": "Izinkan situs ini untuk menambahkan jaringan?", "warning": "Ini memungkinkan jaringan untuk digunakan dalam MetaMask.", "warning_subtext_1": "MetaMask tidak memverifikasi jaringan khusus atau keamanannya.", "warning_subtext_2": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>ar", "warning_subtext_3": "penipuan dan risiko keamanan jaringan", "display_name": "<PERSON><PERSON> ta<PERSON>", "chain_id": "ID Chain", "network_url": "URL Jaringan", "currency_symbol": "Simbol mata uang", "block_explorer_url": "URL block explorer", "details_title": "<PERSON>ail jaringan", "cancel": "<PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "unrecognized_chain": "<PERSON><PERSON><PERSON> k<PERSON> ini tidak dikenali", "invalid_chain": "%{rpcUrl} untuk ID Chain ini tidak cocok dengan daftar chainid.network", "alert_recommend": "<PERSON><PERSON> agar <PERSON>", "alert_verify": "memverifikasikan ID rantai", "warning_subtext_new": {"1": "MetaMask tidak memverifikasi jaringan k<PERSON>, jadi hanya setujui jaringan yang <PERSON>a percayai.", "2": "Selengkapnya tentang risiko keamanan jaringan dan penipuan."}, "invalid_rpc_url": "URL jaringan ini tidak cocok dengan penyedia yang dikenal untuk ID chain ini.", "invalid_chain_token_decimals": "Sepertinya desimal jaringan ini tidak cocok dengan ID chain-nya.", "unrecognized_chain_name": "Sepertinya nama displai jaringan ini tidak cocok dengan ID chain-nya.", "unrecognized_chain_ticker": "Sepertinya simbol jaringan ini tidak cocok dengan ID chain-nya.", "unrecognized_chain_id": "Kami tidak mengenali jaringan ini. Pastikan ID chain sudah benar sebelum melanjutkan."}, "media_player": {"loading": "Memuat...", "not_found": "Media tidak ditemukan"}, "edit_gas_fee_eip1559": {"advanced_options": "Opsi lanjutan", "gas_limit": "Batas gas", "max_priority_fee": "Biaya prioritas maks", "max_fee": "Biaya maks", "estimate": "Estimasi", "recommended_gas_fee": "Biaya gas yang direkomendasikan", "swaps_warning": "<PERSON><PERSON><PERSON> sangat sensitif terhadap waktu. “Tinggi” akan membantu menghindari potensi kerugian akibat per<PERSON>han kondisi pasar.", "priority_fee_at_least_0_error": "Biaya prioritas minimal 1 GWEI", "learn_more": {"title": "<PERSON>gai<PERSON> saya harus memilih?", "intro": "Memilih biaya gas yang tepat tergantung dari jenis transaksi dan seberapa penting bagi Anda.", "aggressive_label": "Agresif", "aggressive_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> besar, bahkan di pasar yang fluktuaktif. Gunakan Agresif untuk menutup biaya lonjakan lalu lintas jaringan karena hal-hal seperti penurunan NFT yang populer.", "market_label": "Pasar", "market_text": "<PERSON>akan <PERSON> untuk memprediksi harga pasar saat ini.", "low_label": "Rendah", "low_text": "Gunakan Rendah untuk menunggu harga yang lebih murah. Estimasi waktu kurang akurat karena harga sedang tidak dapat diprediksi.", "link": "Pelajari selengkapnya seputar penyesuaian gas."}, "save": "Simpan", "submit": "<PERSON><PERSON>", "max_priority_fee_low": "Biaya Prioritas Maks rendah untuk kondisi jaringan saat ini", "max_priority_fee_high": "Biaya Prioritas Maks lebih tinggi dari yang diperlukan", "max_priority_fee_speed_up_low": "Biaya Prioritas Maks minimal {{speed_up_floor_value}} GWEI (10% lebih tinggi dari transaksi awal)", "max_priority_fee_cancel_low": "Biaya Prioritas Maks minimal {{cancel_value}} GWEI (50% lebih tinggi dari transaksi awal)", "max_fee_low": "Biaya Maks rendah untuk kondisi jaringan saat ini", "max_fee_high": "Biaya Maks lebih tinggi dari yang diperlukan", "max_fee_speed_up_low": "Biaya Maks minimal {{speed_up_floor_value}} GWEI (10% lebih tinggi dari transaksi awal)", "max_fee_cancel_low": "Biaya Maks minimal {{cancel_value}} GWEI (50% lebih tinggi dari transaksi awal)", "learn_more_gas_limit": "Batas gas merupakan unit maksimum gas yang ingin Anda gunakan. Unit gas merupakan pengganda untuk “Biaya prioritas maks” dan “Biaya maks”.", "learn_more_max_priority_fee": "Biaya prioritas maks (alias “tip penambang”) langsung masuk ke penambang dan memberi insentif kepada mereka untuk memprioritaskan transaksi Anda. Pengaturan maks akan menjadi yang paling sering <PERSON>a bayar", "learn_more_max_fee": "Biaya maks merupakan biaya tertinggi yang akan <PERSON> (biaya dasar + biaya prioritas).", "learn_more_new_gas_fee": "<PERSON><PERSON> telah memperbarui biaya gas berdasarkan kondisi jaringan saat ini dan telah meningkatkannya minimal 10% (diperlukan oleh jaringan).", "learn_more_cancel_gas_fee": "<PERSON><PERSON> telah memperbarui biaya gas berdasarkan kondisi jaringan saat ini dan telah meningkatkannya minimal 50% (diperlukan oleh jaringan).", "low": "Rendah", "medium": "Sedang", "high": "Tingg<PERSON>", "market": "Pasar", "aggressive": "Agresif", "low_fee_warning": "Buat catatan tentang waktu pemrosesan Anda. Transaksi yang akan datang akan mendapatkan giliran setelah transaksi ini.", "edit_priority": "Edit prioritas", "speed_up_transaction": "Percepat transaksi", "cancel_transaction": "Batalkan transaksi", "new_gas_fee": "Biaya gas baru", "edit_suggested_gas_fee": "Edit biaya gas yang disarankan", "gas_price": "Harga gas", "learn_more_gas_limit_legacy": "Batas gas merupakan unit maksimum gas yang ingin Anda gunakan. Unit gas merupakan pengganda untuk “Harga gas”.", "learn_more_gas_price": "Jaringan ini memerlukan bidang “Harga gas” saat mengirimkan transaksi. Harga gas merupakan jumlah maksimum yang akan Anda bayarkan per unit gas.", "gas_price_low": "Harga gas rendah untuk kondisi jaringan saat ini", "gas_price_high": "Harga gas lebih tinggi dari yang diperlukan"}, "transaction_review_eip1559": {"estimated_gas_fee": "Estimasi biaya gas", "network_fee": "<PERSON><PERSON><PERSON>", "max_fee": "Biaya maks", "total": "Total", "max_amount": "<PERSON><PERSON><PERSON> ma<PERSON>", "estimated_gas_fee_tooltip": "Apa itu biaya gas?", "estimated_gas_fee_tooltip_text_1": "Biaya gas dibayarkan kepada penambang kripto yang memproses transaksi di", "estimated_gas_fee_tooltip_text_2": "<PERSON><PERSON><PERSON>", "estimated_gas_fee_tooltip_text_3": "Ethereum.", "estimated_gas_fee_tooltip_text_4": "MetaMask tidak mengambil keuntungan dari biaya gas.", "estimated_gas_fee_tooltip_text_5": "Biaya gas diatur oleh jaringan dan berfluktuasi berdasarkan lalu lintas jaringan dan kompleksitas transaksi.", "learn_more": "Pelajari selengkapnya seputar biaya gas", "legacy_gas_suggestion_tooltip": "Usulan biaya gas ini menggunakan estimasi gas lama yang mungkin kurang akurat."}, "times_eip1559": {"unknown": "<PERSON><PERSON><PERSON> pem<PERSON> tidak di<PERSON>i", "maybe": "<PERSON><PERSON><PERSON> <PERSON>", "likely": "<PERSON><PERSON><PERSON><PERSON><PERSON> di <", "likely_in": "<PERSON><PERSON><PERSON><PERSON><PERSON> di", "very_likely": "<PERSON>at mungkin di <", "at_least": "Minimal", "less_than": "<PERSON><PERSON> dari", "warning_very_likely": "Biaya gas ini jauh lebih tinggi dibandingkan opsi lain yang tersedia.", "warning_very_likely_title": "Biaya gas terlalu tinggi", "warning_unknown": "Biaya maks atau biaya prioritas maks Anda mungkin terlalu rendah untuk kondisi pasar saat ini. <PERSON><PERSON> tidak tahu kapan (atau jika) transaksi Anda akan diproses.", "warning_low": "<PERSON>i akan menu<PERSON>an biaya maks Anda tetapi jika lalu lintas jaringan meningkat, transaksi Anda mungkin tertunda atau gagal.", "warning_low_title": "Prioritas rendah", "warning_unknown_title": "<PERSON><PERSON><PERSON> pem<PERSON> tidak di<PERSON>i"}, "review_prompt": {"high_fees": "Mengapa biayanya begitu tinggi?", "missing_tokens": "<PERSON>a kehilangan token...", "swap_issues": "<PERSON>a tidak bisa <PERSON>...", "mobile_sentiment": "Bagaimana pendapat Anda tentang MetaMask seluler?", "sentiment_good_face": "😁", "sentiment_bad_face": "☹️", "sentiment_good": "Suka sekali!", "sentiment_bad": "Tidak bagus...", "help_title": "Oh tidak! Ada yang bisa kami bantu?", "help_description_1": "<PERSON>mi selalu siap membantu! <PERSON><PERSON>um di bawah ini atau", "help_description_2": "hubungi dukungan", "help_description_3": "dan dapatkan bantuan!"}, "nickname": {"add_nickname": "Tambahkan nama panggilan", "edit_nickname": "Edit nama <PERSON>n", "save_nickname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "name_placeholder": "Tambahkan nama panggilan ke alamat ini", "contract": "Kontrak", "nickname": "nama panggilan"}, "network_information": {"things_to_keep_in_mind": "<PERSON><PERSON>hal yang perlu diingat", "testnet_network": "Testnet {{type}}", "first_description": "Token asli di jaringan ini adalah {{ticker}}. Ini merupakan token yang digunakan untuk biaya gas.", "second_description": "Jika Anda mencoba mengirim aset secara langsung dari satu jaringan ke jaringan lain, ini dapat mengakibatkan aset Anda hilang secara permanen. Pastikan untuk menggunakan bridge.", "third_description": "Token Anda mungkin tidak secara otomatis muncul di dompet Anda.", "private_network": "Jaringan ini tidak dikenal dan bisa menggunakan token khusus untuk biaya gas.", "unknown_network": "<PERSON><PERSON><PERSON> tidak di<PERSON>al", "switched_network": "Anda telah beralih ke", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "add_token": "Klik di sini untuk menambahkan token secara manual", "add_token_manually": "Tambahkan secara manual.", "got_it": "<PERSON><PERSON><PERSON>", "error_title": "Ups! <PERSON><PERSON><PERSON><PERSON> k<PERSON>han.", "error_message": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memuat informasi jaringan. <PERSON><PERSON>an coba lagi nanti.", "private_network_third_description": "<PERSON><PERSON>kkan nama untuk jaringan ini agar mudah diidentifikasi.", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "enable_token_detection": "Aktifkan deteksi token otomatis", "token_detection_mainnet_title": "Deteksi token diaktifkan sehingga token-token akan muncul di dompet Anda secara otomatis.", "token_detection_mainnet_link": "Anda juga dapat menambahkan token secara manual.", "or": "atau", "non_evm_first_description": "Aset asli di jaringan ini adalah {{ticker}}. Aset ini digunakan untuk biaya transaksi.", "non_evm_second_description": "<PERSON>a akan kehilangan aset milik Anda jika mencoba mengirimnya dari atau ke jaringan lain."}, "download_files": {"error": "<PERSON><PERSON>.", "unknownError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak dikenal"}, "remember_me": {"enable_remember_me": "Aktifkan Ingatkan saya", "enable_remember_me_description": "Saat Ingatkan saya aktif, siapa pun yang memiliki akses ke ponsel Anda dapat mengakses akun MetaMask Anda."}, "turn_off_remember_me": {"title": "<PERSON><PERSON><PERSON>n kata sandi Anda untuk menonaktifkan Ingatkan saya", "placeholder": "<PERSON>a sandi", "description": "<PERSON><PERSON> Anda menonaktifkan opsi ini, <PERSON><PERSON> me<PERSON>an kata sandi untuk membuka MetaMask mulai sekarang.", "action": "Nonaktifkan Ingatkan saya"}, "dapp_connect": {"warning": "Untuk menggunakan fitur ini, perbarui aplikasi ke versi terbaru"}, "confirmation_modal": {"cancel_cta": "<PERSON><PERSON>", "confirm_cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "automatic_security_checks": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kea<PERSON>n otomatis", "description": "Memeriksa pembaruan secara otomatis dapat menampilkan alamat IP Anda ke server GitHub. Ini hanya menunjukkan bahwa alamat IP Anda sedang menggunakan MetaMask. Tidak ada alamat akun atau informasi lainnya yang ditampilkan."}, "terms_of_use_modal": {"title": "<PERSON><PERSON><PERSON> kami", "terms_of_use_check_description": "<PERSON><PERSON>, yang berlaku untuk penggunaan saya atas MetaMask dan semua fiturnya", "accept_cta": "Terima", "accept_helper_description": "Gulirkan layar untuk membaca semua bagian", "agree_cta": "<PERSON><PERSON><PERSON>"}, "update_needed": {"title": "Dapatkan fitur terbaru", "description": "<PERSON><PERSON> telah membuat dompet <PERSON>a lebih aman, lebih lancar, dan menambahkan beberapa fitur baru. <PERSON><PERSON><PERSON> sekarang untuk tetap terlindungi dan gunakan penyempurnaan terbaru kami.", "primary_action": "<PERSON><PERSON><PERSON> ke versi terkini"}, "enable_automatic_security_check_modal": {"title": "Periksa pembaruan keamanan secara otomatis?", "description": "Memeriksa pembaruan secara otomatis dapat menampilkan alamat IP Anda ke server GitHub. Ini hanya menunjukkan bahwa alamat IP Anda sedang menggunakan MetaMask. Tidak ada alamat akun atau informasi lainnya yang ditampilkan.", "primary_action": "Aktifkan pemeriksaan keamanan otomatis", "secondary_action": "Tidak, terima kasih"}, "contract_allowance": {"custom_spend_cap": {"max": "<PERSON><PERSON>", "edit": "Edit", "title": "<PERSON><PERSON>", "use_site_suggestion": "<PERSON><PERSON><PERSON> saran situs", "from_your_balance": "dari saldo Anda saat ini.", "default_error_message": "<PERSON><PERSON>kkan angka yang menurut Anda dapat digunakan pihak ketiga sekarang atau di masa mendatang. <PERSON>a selalu dapat meningkatkan batas pengunaan nanti.", "this_contract_allows": "Ini memungkinkan pihak ketiga menggunakan", "amount_greater_than_balance": "Ini memungkinkan pihak ketiga menggunakan semua saldo token hingga mencapai batas atau Anda mencabut batas pengeluaran. Jika ini bukan yang dimaksudkan, pertimbangkan untuk menetapkan batas penggunaan yang lebih rendah.", "info_modal_description_default": "<PERSON>nt<PERSON> dapat mempergunakan seluruh saldo token Anda tanpa pemberitahuan atau persetujuan lebih lanjut. Lindungi diri Anda dengan menetapkan batas penggunaan yang lebih rendah.", "set_spend_cap": "Atur batas penggunaan", "be_careful": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error_enter_number": "Kesalahan: <PERSON><PERSON><PERSON><PERSON> angka saja", "enter_number": "<PERSON><PERSON><PERSON>n nomor di sini", "learn_more": "<PERSON><PERSON><PERSON><PERSON>"}, "token_allowance": {"verify_third_party_details": "Verifikasikan detail pihak ketiga", "protect_from_scams": "Untuk melindungi diri Anda dari pen<PERSON>, luangkan waktu sejenak untuk memverifikasi detail pihak ketiga.", "learn_to_verify": "Pelajari cara memverifikasi detail pihak ketiga", "spending_cap": "batas penggunaan", "access": "aks<PERSON>", "nft_contract": "Kontrak NFT", "token_contract": "Kontrak token", "third_party_requesting_text": "<PERSON><PERSON> ketiga meminta {{action}}", "third party": "pihak ketiga", "address": "<PERSON><PERSON><PERSON>"}}, "restore_wallet": {"restore_needed_title": "<PERSON><PERSON><PERSON><PERSON>", "restore_needed_description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, tetapi jangan khawatir! Mari coba pulihkan dompet Anda.", "restore_needed_action": "Pulihkan dompet"}, "wallet_restored": {"wallet_restored_title": "Dompet Anda sudah siap!", "wallet_restored_action": "Lanjutkan ke dompet", "wallet_restored_description_part_one": "<PERSON>a perlu menambahkan beberapa aset, jar<PERSON><PERSON>, dan pengaturan lagi secara manual.", "wallet_restored_description_part_two": "Luangkan waktu sejenak untuk", "wallet_restored_description_link": "mencadangkan Frasa Pemulihan Rahasia", "wallet_restored_description_part_three": "bilamana dompet Anda perlu dipulihkan lagi di masa mendatang."}, "new_wallet_needed": {"new_wallet_needed_title": "Dompet baru diperlukan", "new_wallet_needed_create_new_wallet_action": "Buat dompet baru", "new_wallet_needed_create_try_again_action": "Coba pulihkan dompet", "new_wallet_needed_description_part_one": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han dengan dompet Anda. <PERSON><PERSON><PERSON> yang baru. <PERSON><PERSON> akun <PERSON> berada di blockchain, maka keamana<PERSON>a masih terjaga. <PERSON><PERSON>, <PERSON><PERSON><PERSON> ters<PERSON>, nama akun, dan data terkait yang tersimpan di perangkat yang hilang.", "new_wallet_needed_description_part_two": "<PERSON><PERSON>k mengimpor akun ke dompet baru, <PERSON><PERSON>an <PERSON><PERSON>. Jika tidak memiliki Frasa P<PERSON>, akun <PERSON>a tidak akan bisa diimpor.", "new_wallet_needed_description_part_three": "<PERSON>gar hal ini tidak terjadi lagi, pastikan untuk selalu memperbarui aplikasi dan OS MetaMask ke versi terbaru."}, "srp_security_quiz": {"title": "<PERSON><PERSON> k<PERSON>", "introduction": "Untuk mengungkapkan Frasa <PERSON>, <PERSON><PERSON> perlu menjawab dua pertanyaan dengan benar", "get_started": "<PERSON><PERSON>", "learn_more": "<PERSON><PERSON><PERSON><PERSON>", "try_again": "Coba lagi", "continue": "Lanjutkan", "of": "dari", "question_one": {"question": "<PERSON><PERSON>, MetaMask...", "right_answer": "Tidak dapat membantu Anda", "wrong_answer": "Dapat mengembalikannya untuk Anda", "right_answer_title": "Benar! Tidak ada yang dapat membantu mengembalikan Frasa <PERSON>a", "right_answer_description": "<PERSON>at, ukir pada logam, atau simpan di beberapa tempat rahasia agar Anda tidak pernah kehilangan. <PERSON><PERSON> kehilangan, maka akan hilang selamanya.", "wrong_answer_title": "Salah! Tidak ada yang dapat membantu mengembalikan Frasa <PERSON>a", "wrong_answer_description": "<PERSON><PERSON> Anda kehilangan <PERSON>, maka akan hilang selam<PERSON>. Tidak ada yang dapat membantu <PERSON><PERSON>, apa pun yang mereka katakan."}, "question_two": {"question": "<PERSON><PERSON> ada yang men<PERSON>akan <PERSON>, bahkan agen pendukung,...", "right_answer": "<PERSON><PERSON> <PERSON>", "right_answer_title": "Benar! Membagikan Frasa Pemulihan Rahas<PERSON> bukanlah ide yang baik", "right_answer_description": "Siapa pun yang mengaku membutuhkan <PERSON>, mereka berbohong kepada <PERSON>. <PERSON><PERSON> memba<PERSON>, mereka akan mencuri aset <PERSON>.", "wrong_answer": "<PERSON><PERSON> harus member<PERSON><PERSON>a kepada mereka", "wrong_answer_title": "Tidak! Jangan pernah membagikan Frasa Pemulihan Rahas<PERSON> kepada siapa pun", "wrong_answer_description": "Siapa pun yang mengaku membutuhkan <PERSON>, mereka berbohong kepada <PERSON>. <PERSON><PERSON> memba<PERSON>, mereka akan mencuri aset <PERSON>."}}, "ledger": {"open_settings": "<PERSON><PERSON>", "view_settings": "<PERSON><PERSON>", "bluetooth_off": "Bluetooth nonaktif", "bluetooth_off_message": "Aktifkan Bluetooth untuk perangkat", "bluetooth_access_blocked": "Ledger memer<PERSON>an akses Bluetooth untuk dipasangkan dengan perangkat seluler.", "bluetooth_access_blocked_message": "<PERSON>ka ingin memasangkan perangkat Ledger menggunakan Bluetooth, aktifkan di Pengaturan dan coba lagi.", "location_access_blocked": "MetaMask memerlukan izin akses lokasi untuk dipasangkan dengan Ledger.", "location_access_blocked_message": "Jika ingin memasangkan perangkat Ledger menggunakan Bluetooth, aktifkan akses lokasi di Pengaturan & coba lagi.", "nearbyDevices_access_blocked": "MetaMask memerlukan izin perangkat terdekat untuk dipasangkan dengan Ledger.", "nearbyDevices_access_blocked_message": "<PERSON>ka ingin memasangkan perangkat Ledger menggunakan Bluetooth, <PERSON><PERSON> harus mengaktifkan akses perangkat terdekat di Pengaturan & coba lagi.", "bluetooth_scanning_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat memindai perangkat", "bluetooth_scanning_error_message": "Pastikan perangkat tidak terkunci dan aplikasi Ethereum berjalan", "bluetooth_connection_failed": "<PERSON><PERSON><PERSON><PERSON> Bluetooth gagal", "bluetooth_connection_failed_message": "Pastikan Ledger tidak terkunci dan Bluetooth diaktifkan", "ethereum_app_open": "Ledger memb<PERSON><PERSON><PERSON> konfirmasi", "ethereum_app_open_message": "Konfirmasikan pada perangkat untuk membuka aplikasi Ethereum. <PERSON><PERSON> se<PERSON>.", "ethereum_app_unconfirmed_error": "<PERSON><PERSON> menolak permintaan untuk membuka aplikasi Ethereum.", "failed_to_open_eth_app": "Gagal membuka aplikasi Ethereum.", "ethereum_app_open_error": "Instal/terima aplikasi Ethereum di perangkat Ledger.", "running_app_close": "<PERSON>l menutup aplikasi yang ber<PERSON>.", "running_app_close_error": "Gagal menutup aplikasi yang berjalan di perangkat Ledger.", "ethereum_app_not_installed": "Aplikasi Ethereum belum diinstal.", "ethereum_app_not_installed_error": "Instal aplikasi Ethereum di perangkat Ledger.", "ledger_is_locked": "Ledger terkunci", "unlock_ledger_message": "<PERSON><PERSON>", "cannot_get_account": "Tidak bisa mendapatkan akun", "connect_ledger": "<PERSON><PERSON><PERSON><PERSON> Ledger", "looking_for_device": "<PERSON><PERSON><PERSON>", "ledger_reminder_message": "<PERSON><PERSON><PERSON>:", "ledger_reminder_message_step_one": "1. <PERSON><PERSON>", "ledger_reminder_message_step_two": "2. In<PERSON> dan buka aplikasi Ethereum", "ledger_reminder_message_step_three": "3. Aktifkan Bluetooth", "ledger_reminder_message_step_four": "4. Lokasi diaktifkan dengan menggunakan lokasi yang tepat", "ledger_reminder_message_step_four_Androidv12plus": "4. Perangkat terdekat diaktifkan", "ledger_reminder_message_step_five": "5. <PERSON><PERSON> ganggu harus dinonakt<PERSON>", "blind_signing_message": "6. <PERSON><PERSON><PERSON><PERSON> \"penanda<PERSON><PERSON>an buta\" pada perang<PERSON>a.", "available_devices": "<PERSON><PERSON><PERSON> yang tersedia", "retry": "Coba lagi", "continue": "Lanjutkan", "confirm_transaction_on_ledger": "Konfirmasikan transaksi di Ledger Anda", "bluetooth_enabled_message": "Pastikan Bluetooth diaktifkan", "device_unlocked_message": "Perangkat tidak terkunci", "ledger_disconnected": "Sambungan perangkat terputus", "ledger_disconnected_error": "Koneksi ke perangkat telah terputus. Silakan coba lagi.", "unknown_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak terduga.", "unknown_error_message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak terduga. <PERSON>lakan coba lagi.", "error_occured": "<PERSON><PERSON><PERSON><PERSON>", "how_to_install_eth_app": "Cara menginstal aplikasi Ethereum di perangkat Ledger", "ledger_account_count": "Anda menggunakan 1 akun dari Ledger dengan MetaMask Mobile.", "open_eth_app": "Buka aplikasi Ethereum", "open_eth_app_message_one": "<PERSON><PERSON> telah mendeteksi Anda telah menginstal aplikasi Ethereum tetapi tidak terbuka,", "open_eth_app_message_two": "tekan dua tombol pada perangkat untuk menerima perintah membuka aplikasi Ethereum untuk melanjutkan.", "toast_bluetooth_connection_error_title": "Ups! <PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han :/", "toast_bluetooth_connection_error_subtitle": "Ledger belum terhubung", "try_again": "Coba lagi", "forget_device": "<PERSON><PERSON><PERSON> Ledger", "sign_with_ledger": "Tandatangani dengan Ledger", "ledger_pending_confirmation": "Ledger sedang sibuk", "ledger_pending_confirmation_error": "<PERSON> tindakan tertunda di Ledger. <PERSON><PERSON> tindakan terlebih dahulu lalu coba lagi.", "not_supported": "Operasi tidak didukung", "not_supported_error": "Hanya penandatanganan data yang diketik versi 4 yang didukung.", "error_during_connection": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han tidak dikenal", "error_during_connection_message": "Ada sedikit masalah saat menghubungkan perangkat Ledger, ketuk 'Coba Lagi' di bawah ini untuk mencoba kembali. Terkadang hal ini terjadi karena aplikasi ETH pada perangkat Ledger terbuka di awal proses pemasangan dengan MetaMask Mobile.", "how_to_install_eth_webview_title": "Cara menginstal Aplikasi Ethereum", "nonce_too_low": "<PERSON><PERSON> terlalu rendah", "nonce_too_low_error": "<PERSON>ce yang ditetapkan terlalu rendah", "select_accounts": "<PERSON><PERSON><PERSON> akun", "select_hd_path": "<PERSON><PERSON><PERSON>", "select_hd_path_description": "Jika Anda tidak menemukan akun yang di<PERSON>, coba alihkan jalur HD atau jaringan yang dipilih saat ini.", "ledger_live_path": "Ledger Live", "ledger_legacy_path": "Legacy(MEW/MyCrypto)", "ledger_bip44_path": "BIP44(mis. <PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "ledger_legacy_label": " (legacy)", "blind_sign_error": "<PERSON><PERSON><PERSON> buta", "blind_sign_error_message": "Penandatanganan buta tidak diaktifkan pada perangkat Ledger Anda. Aktifkan di pengaturan.", "user_reject_transaction": "Pengguna menolak transaksi", "user_reject_transaction_message": "Pengguna telah menolak transaksi pada perangkat Ledger.", "multiple_devices_error_message": "Beberapa perangkat belum didukung. Untuk menambahkan perangkat Ledger baru, <PERSON><PERSON> ha<PERSON> menghapus perangkat lama.", "hd_path_error": "<PERSON><PERSON><PERSON> <PERSON> tidak valid: {{path}}", "unspecified_error_during_connect": "Kesalahan tak dikenali saat menghubungkan Perangkat Keras Ledger,", "account_name_existed": "Akun {{accountName}} sudah ada"}, "account_actions": {"edit_name": "Edit nama akun", "add_account_or_hardware_wallet": "Tambahkan akun atau dompet perangkat keras", "connect_hardware_wallet": "Hubungkan akun", "import_wallet_or_account": "I<PERSON>r dompet atau akun", "add_account": "Buat akun baru", "add_multichain_account": "Buat akun {{networkName}} baru", "create_an_account": "Buat akun baru", "add_new_account": "Akun Ethereum", "create_new_wallet": "Buat dompet baru", "import_wallet": "Impor do<PERSON>", "import_srp": "<PERSON><PERSON>", "add_hardware_wallet": "<PERSON><PERSON> perangkat keras", "import_account": "<PERSON><PERSON><PERSON> pribadi", "add_bitcoin_account": "Akun Bitcoin", "add_solana_account": "<PERSON><PERSON><PERSON>", "switch_to_smart_account": "Beralih ke Akun <PERSON>", "rename_account": "Ganti nama akun", "addresses": "<PERSON><PERSON><PERSON>", "headers": {"bitcoin": "Bitcoin", "solana": "Solana"}}, "show_nft": {"show_nft_title": "Tampilkan NFT", "show_nft_content_1": "Kami menggunakan layanan pihak ketiga untuk menampilkan gambar NFT yang disimpan di IPFS, menampilkan informasi terkait alamat ENS yang dimasukkan di bilah alamat browser, serta mengambil ikon untuk token yang berbeda. Alamat IP dapat terekspos oleh layanan ini saat Anda menggunakannya.", "show_nft_content_2": "<PERSON><PERSON><PERSON><PERSON>", "show_nft_content_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "show_nft_content_4": "akan mengaktifkan resolusi IPFS. Anda dapat menonaktifkannya di", "show_nft_content_5": "Pengaturan > <PERSON><PERSON><PERSON> dan privasi", "show_nft_content_6": "kapan saja."}, "show_display_nft_media": {"show_display_nft_media_title": "Tampilkan media NFT", "show_display_nft_media_content_1": "Untuk melihat NFT, aktifkan", "show_display_nft_media_content_2": "Tampilkan media NFT.", "show_display_nft_media_content_3": "Menampilkan media dan data NFT mengekspos alamat IP ke OpenSea atau pihak ketiga lainnya. Autodeteksi NFT mengandalkan fitur ini, dan tidak akan tersedia jika fitur ini dinonaktifkan.", "show_display_nft_media_content_4": "Anda dapat menonaktifkan Tampilkan media NFT di", "show_display_nft_media_content_5": "<PERSON><PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON> dan privasi."}, "ipfs_gateway_banner": {"ipfs_gateway_banner_title": "gateway IPFS", "ipfs_gateway_banner_content1": "Ini adalah situs web IPFS. Untuk melihat situs ini, <PERSON><PERSON> perlu mengaktifkan", "ipfs_gateway_banner_content2": "gateway IPFS", "ipfs_gateway_banner_content3": "di", "ipfs_gateway_banner_content4": "<PERSON><PERSON><PERSON><PERSON>."}, "ipfs_gateway": {"ipfs_gateway_title": "gateway IPFS", "ipfs_gateway_content1": "Kami menggunakan layanan pihak ketiga untuk menampilkan gambar NFT yang disimpan di IPFS, menampilkan informasi terkait alamat ENS yang dimasukkan di bilah alamat browser, serta mengambil ikon untuk token yang berbeda. Alamat IP dapat terekspos oleh layanan ini saat Anda menggunakannya.", "ipfs_gateway_content2": "Anda dapat menonaktifkan resolusi IPFS di", "ipfs_gateway_content3": "Pengaturan > <PERSON><PERSON><PERSON> dan privasi", "ipfs_gateway_content4": "kapan saja"}, "install_snap": {"title": "Permin<PERSON>an kone<PERSON>i", "description": "{{origin}} ingin mengg<PERSON> {{snap}}.", "permissions_request_title": "<PERSON><PERSON><PERSON><PERSON>", "permissions_request_description": "{{origin}} ingin men<PERSON> {{snap}}, yang meminta izin berikut.", "approve_permissions": "<PERSON><PERSON><PERSON><PERSON>", "installed": "<PERSON><PERSON><PERSON>", "install_successful": "{{snap}} berhasil diinstal.", "okay_action": "<PERSON>e", "error_title": "<PERSON><PERSON>", "error_description": "Instalasi {{snap}} gagal."}, "earn": {"empty_state_cta": {"heading": "Pinjamkan {{tokenSymbol}} dan hasilkan", "body": "Pinjamkan {{tokenSymbol}} <PERSON><PERSON> den<PERSON> {{protocol}} dan dapatkan", "annually": "set<PERSON>p tahun.", "learn_more": "Selengkapnya.", "earn": "<PERSON><PERSON><PERSON>"}, "service_interruption_banner": {"maintenance_message": "<PERSON><PERSON>g dalam perbaikan dan akan segera kembali online!"}, "deposit": "<PERSON><PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON><PERSON>", "approval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Batalkan", "transaction_submitted": "Transaksi <PERSON>", "every_minute": "<PERSON><PERSON><PERSON> menit", "immediate": "<PERSON><PERSON><PERSON>", "apr": "APR", "protocol": "Terdesentralisasi", "receive_tooltip": "menerima tooltip", "button": "tombol", "receive": "Terima", "tooltip_content": {"apr": {"part_one": "Peningkatan nilai deposit tahunan yang di<PERSON>, berdasarkan pada nilai reward selama seminggu terakhir.", "part_two": "Catatan: APR berubah seiring waktu."}, "protocol": "Protokol peminjaman adalah kontrak cerdas yang memungkinkan Anda meminjamkan token untuk memperoleh reward. Protokol ini juga memungkinkan pengguna meminjam token dengan menjadikan token lain sebagai agunan, dengan biaya yang dibayarkan kepada pemberi pinjaman.", "reward_frequency": "Frekuensi penghitungan reward <PERSON><PERSON>.", "withdrawal_time": "Waktu yang diperlukan untuk menarik token dari protokol dan mengembalikannya ke dompet Anda", "receive": "Token ini digunakan untuk melacak aset dan reward. <PERSON><PERSON> mentransfer atau memperdagangkannya, atau Anda tidak akan dapat menarik aset.", "health_factor": {"your_health_factor_measures_liquidation_risk": "<PERSON><PERSON><PERSON> men<PERSON>r risi<PERSON> lik<PERSON>", "above_two_dot_zero": "Di atas 2,0", "safe_position": "<PERSON><PERSON><PERSON> aman", "between_one_dot_five_and_2_dot_zero": "Antara 1,5-2,0", "medium_liquidation_risk": "Risiko Li<PERSON>idasi Sedang", "below_one_dot_five": "<PERSON> bawah 1,5", "higher_liquidation_risk": "Risiko lik<PERSON> lebih tinggi"}, "lending_risk_aware_withdrawal_tooltip": {"why_cant_i_withdraw_full_balance": "Mengapa saya tidak dapat menarik seluruh saldo?", "your_withdrawal_amount_may_be_limited_by": "<PERSON><PERSON><PERSON>a mungkin dibatasi oleh", "pool_liquidity": "Likuiditas Pool", "not_enough_funds_available_in_the_lending_pool_right_now": "<PERSON> yang tersedia pada pool pinjaman saat ini tidak cukup.", "existing_borrow_positions": "<PERSON><PERSON><PERSON> yang <PERSON>", "withdrawing_could_put_your_existing_loans_at_risk_of_liquidation": "Penarikan dapat membuat posisi pinjaman saat ini berisiko dilikuidasi."}}, "withdraw": "<PERSON><PERSON>", "deposit_more": "Deposit lagi", "earning": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_time": "<PERSON><PERSON><PERSON> penari<PERSON>", "withdrawing_to": "Penarikan ke", "network": "<PERSON><PERSON><PERSON>", "health_factor": "<PERSON><PERSON><PERSON>", "liquidation_risk": "<PERSON><PERSON><PERSON> l<PERSON>", "insufficient_pool_liquidity": "Likuiditas Pool Tidak Cukup", "available_to_withdraw": "tersedia untuk ditarik", "unknown": "tidak dikenal", "how_it_works": "<PERSON> k<PERSON>", "market_historic_apr_modal": {"earn_rewards_on_your_token": "Dapatkan reward atas {{tokenSymbol}} Anda", "lend_and_earn_daily_rewards": "Pinjamkan {{tokenSymbol}} <PERSON><PERSON> den<PERSON> {{protocol}} dan dapatkan reward harian. <PERSON><PERSON> berta<PERSON>h seiring waktu dan APR bervariasi.", "withdraw_whenever_you_want": "<PERSON><PERSON> tiap saat <PERSON>a mau", "get_asset_back_in_your_wallet_instantly": "Dapatkan {{tokenSymbol}} kembali ke dompet Anda segera."}, "amount_exceeds_safe_withdrawal_limit": "<PERSON><PERSON><PERSON> mele<PERSON>hi batas penarikan yang aman", "view_earnings_history": {"lending": "Lihat riwayat posisi", "staking": "Lihat riwayat penghasilan"}, "earnings_history_list_title": {"lending": "Riwayat posisi", "staking": "Riwayat pembayaran"}, "allowance_reset": "<PERSON><PERSON>"}, "stake": {"stake": "Stake", "earn": "Dapatkan", "stake_eth": "Stake ETH", "unstake_eth": "Batalkan stake ETH", "staked_balance": "Saldo yang di-stake", "staked_ethereum": "Ethereum yang di-stake", "unstake": "Batalkan stake", "stake_more": "Stake lebih banyak", "claim": "<PERSON><PERSON><PERSON>", "your_earnings": "Pendapatan Anda", "annual_rate": "<PERSON><PERSON>", "lifetime_rewards": "<PERSON><PERSON> seumur hidup", "estimated_annual_earnings": "Estimasi pendapatan tahunan", "accessibility_labels": {"stake_annual_rate_tooltip": "<PERSON><PERSON><PERSON> nilai ta<PERSON>an"}, "estimated_annual_rewards": "Estimasi reward ta<PERSON>an", "estimated_annual_reward": "Estimasi reward ta<PERSON>an", "reward_frequency": "Frekuensi reward", "reward_frequency_tooltip": "<PERSON><PERSON> yang di-stake diperbarui setiap {{frequency}} untuk memperhitungkan reward baru.", "withdrawal_time": "<PERSON><PERSON><PERSON> penari<PERSON>", "metamask_pool": "MetaMask Pool", "enter_amount": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "review": "Tinjau", "not_enough_eth": "ETH tidak cukup", "not_enough_token": "Tidak cukup {{ticker}}", "balance": "<PERSON><PERSON>", "stake_eth_and_earn": "Stake ETH dan dapatkan", "how_it_works": "<PERSON> k<PERSON>", "stake_any_amount_of_eth": "Stake sejumlah ETH.", "no_minimum_required": "Tidak ada jumlah minimum.", "earn_eth_rewards": "Dapatkan reward ETH.", "earn_eth_rewards_description": "<PERSON><PERSON><PERSON><PERSON><PERSON> segera setelah Anda melakukan stake. <PERSON><PERSON> akan terkumpul secara otomatis.", "flexible_unstaking": "Pembatalan stake yang fleksibel.", "flexible_unstaking_description": "Batalkan stake setiap saat. Biasanya waktu yang diperlukan kurang dari 3 hari, tetapi butuh waktu hingga 11 hari untuk memprosesnya.", "disclaimer": "Stake tidak menjamin reward, dan melibatkan risiko termasuk hilangnya dana.", "learn_more": "Selengkapnya", "got_it": "<PERSON><PERSON><PERSON>", "your_balance": "<PERSON><PERSON>", "stake_your_eth_cta": {"base": "Stake ETH dengan MetaMask Pool dan dapatkan", "annually": "set<PERSON>p tahun.", "learn_more_with_period": "Selengkapnya."}, "day": {"zero": "", "one": "hari", "other": "hari"}, "hour": {"zero": "", "one": "jam", "other": "jam"}, "minute": {"zero": "", "one": "menit", "other": "menit"}, "banner_text": {"has_claimable_eth": "<PERSON><PERSON> dapat mengekla<PERSON> {{amountEth}} ETH. <PERSON><PERSON><PERSON>, ETH akan kembali ke dompet Anda.", "unstaking_in_progress": {"base": "Pembatalan stake {{amountEth}} ETH sedang berlangsung. Ke<PERSON>li lagi dalam", "and": "dan", "to_claim_it": "untuk mengeklaimnya.", "default": "Pembatalan stake {{amountEth}} ETH sedang berlangsung. Kembalilah dalam beberapa hari untuk mengeklaimnya."}, "geo_blocked": "Anda berada di wilayah tempat pembatalan stake maupun stake tidak diizinkan.", "approximately": "seki<PERSON>"}, "unstake_input_banner_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, diperlukan waktu kurang dari 3 hari agar ETH yang batal di-stake dapat diklaim, tetapi tindakan ini dapat memakan waktu hingga 11 hari.", "max": "<PERSON><PERSON>", "staking_from": "Stake dari", "advanced_details": "Detail lanjutan", "ethereum_mainnet": "Mainnet Ethereum", "interacting_with": "Berinteraks<PERSON> den<PERSON>", "12_hours": "12 jam", "terms_of_service": "<PERSON><PERSON><PERSON><PERSON> layanan", "risk_disclosure": "Pengungkapan risiko", "cancel": "Batalkan", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "continue": "Lanjutkan", "estimated_changes": "<PERSON><PERSON><PERSON><PERSON>", "you_receive": "<PERSON><PERSON>", "up_to_n": "Hingga {{count}}", "unstaking_to": "Batalkan stake ke", "claiming_to": "Mengeklaim ke", "max_modal": {"title": "<PERSON><PERSON>", "eth": {"description": "Maks merupakan jumlah total ETH yang Anda miliki, dikurangi biaya gas yang diperlukan untuk melakukan stake. Sebaiknya simpan sejumlah ETH tambahan di dompet Anda untuk transaksi di masa mendatang."}}, "use_max": "Gunakan maks", "estimated_unstaking_time": "1 sampai 11 hari", "proceed_anyway": "Tetap <PERSON>", "gas_cost_impact": "Dampak biaya gas", "select_a_token_to_deposit": "Pilih token untuk deposit", "select_a_token_to_withdraw": "<PERSON><PERSON><PERSON> token untuk ditarik", "you_could_earn_up_to": "<PERSON>a bisa mengh<PERSON>an hingga", "per_year_on_your_tokens": "per tahun atas token Anda", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "gas_cost_impact_warning": "Peringatan: biaya gas transaksi akan mencapai lebih dari {{percentOverDeposit}}% dari deposit Anda.", "earnings_history_title": "Pendapatan {{ticker}}", "apr": "APR", "interactive_chart": {"timespan_buttons": {"7D": "7H", "1M": "1B", "3M": "3B", "6M": "6B"}}, "today": "<PERSON> ini", "one_week_average": "Rata-rata 1 minggu", "one_month_average": "Rata-rata 1 bulan", "three_month_average": "Rata-rata 3 bulan", "six_month_average": "Rata-rata 6 bulan", "one_year_average": "Rata-rata 1 tahun"}, "default_settings": {"title": "<PERSON><PERSON> Anda sudah siap", "description": "MetaMask menggunakan pengaturan default untuk menyeimbangkan keamanan dan kemudahan penggunaan. Ubah pengaturan ini untuk lebih meningkatkan privasi Anda.", "learn_more_about_privacy": "Selengkapnya seputar praktik terbaik privasi.", "privacy_policy": "<PERSON><PERSON><PERSON><PERSON> privasi", "default_settings": "<PERSON><PERSON><PERSON><PERSON>", "done": "Se<PERSON><PERSON>", "basic_functionality": "Fungsionalitas dasar", "manage_networks": "<PERSON><PERSON><PERSON>", "manage_networks_body": "<PERSON><PERSON> mengg<PERSON>kan Infura sebagai penyedia panggilan prosedur jarak <PERSON>h (RPC) untuk menawarkan akses paling andal dan pribadi ke data Ethereum sebisa mungkin. Anda dapat memilih RPC sendiri, akan tetapi ingatlah bahwa RPC apa pun akan menerima alamat IP dan dompet Ethereum Anda untuk melakukan transaksi. Baca ", "manage_networks_body2": " untuk mempelajari selengkapnya tentang cara Infura menangani data untuk akun EVM, untuk akun Solana ", "manage_networks_body3": "klik di sini.", "functionality_body": "MetaMask menawarkan fitur dasar seperti detail token dan pengaturan gas melalui layanan internet. Alamat IP dibagikan saat menggunakan layanan internet, dalam hal ini kepada MetaMask. Ini sama seperti saat Anda mengunjungi situs web mana pun. MetaMask menggunakan data ini untuk sementara dan tidak akan pernah menjual data Anda. Anda dapat menggunakan VPN atau menonaktifkan layanan ini, akan tetapi hal ini dapat memengaruhi pengalaman dalam menggunakan MetaMask. Baca ", "functionality_body2": " untuk selengkapnya.", "sheet": {"title_off": "Nonaktifkan fungsionalitas dasar", "description_off": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> tidak akan benar-benar mengoptimalkan waktu di MetaMask. <PERSON><PERSON> dasar (seperti detail token, pengaturan gas optimal, dan lainnya) tidak akan tersedia untuk Anda.", "description_off2": "<PERSON><PERSON> menonaktifkan ini, <PERSON><PERSON> juga akan menonaktifkan semua fitur di dalamnya", "description_off2_related_features1": "keamanan dan pri<PERSON>i, pencadangan dan <PERSON>", "description_off2_related_features1_and": "dan", "description_off2_related_features2": "notif<PERSON><PERSON>.", "title_on": "Aktifkan fungsionalitas dasar", "description_on": "Untuk mengoptimalkan waktu di MetaMask, <PERSON>a perlu mengaktifkan fitur ini. Fun<PERSON>i dasar (seperti detail token, pengaturan gas optimal, notifikasi, dan la<PERSON>ya) penting untuk pengalaman web3.", "checkbox_label": "<PERSON>a memahami dan ingin melanju<PERSON>kan", "buttons": {"cancel": "<PERSON><PERSON>", "turn_on": "Aktifkan", "turn_off": "Nonaktifkan", "reset": "Reset"}}, "drawer_general_title": "<PERSON><PERSON>", "drawer_general_title_desc": "Sinkronkan pengaturan di seluruh perang<PERSON>, pilih preferensi jaringan, dan lacak data token", "drawer_assets_title": "<PERSON><PERSON>", "drawer_assets_desc": "Autodeteksi token di dompet Anda, tampilkan NFT, dan dapatkan pembaruan saldo akun secara batch", "drawer_security_title": "<PERSON><PERSON><PERSON>", "drawer_security_desc": "Ku<PERSON><PERSON> kemungkinan Anda bergabung dengan jaringan yang tidak aman dan lindungi akun Anda", "network_details_check_desc": "MetaMask menggunakan layanan pihak ketiga yang disebut chainid.network untuk menampilkan detail jaringan yang akurat dan terstandardisasi. Hal ini dapat mengurangi kemungkinan Anda untuk terhubung ke jaringan berbahaya atau salah. Saat menggunakan fitur ini, alamat IP akan diketahui oleh chainid.network."}, "simulation_details": {"failed": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memuat estimasi Anda.", "fiat_not_available": "Tidak Tersedia", "incoming_heading": "<PERSON><PERSON>", "no_balance_changes": "Tidak ada per<PERSON>han", "outgoing_heading": "<PERSON><PERSON>", "reverted": "Transaksi ini kemungkinan besar akan gagal", "title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip_description": "<PERSON><PERSON><PERSON><PERSON> perubahan merupakan hal yang mungkin terjadi jika Anda melakukan transaksi ini. Ini hanyalah prediksi, bukan jaminan.", "total_fiat": "Total = {{currency}}"}, "spam_filter": {"block_origin_requests_for_1_minute": "Blokir situs ini untuk sementara", "cancel": "Batalkan", "description": "<PERSON><PERSON>i dengan banyak permin<PERSON>, <PERSON><PERSON> dapat memblokir situs tersebut untuk sementara.", "got_it": "<PERSON><PERSON><PERSON>", "site_blocked_description": "Situs akan diblokir selama 1 menit.", "site_blocked_title": "Anda telah memblokir situs ini untuk sementara", "title": "<PERSON><PERSON> telah melihat banyak permintaan"}, "common": {"please_wait": "<PERSON><PERSON> tunggu", "disconnect_you_from": "Ini akan memutus koneksi dari {{dappUrl}}", "disconnect": "<PERSON><PERSON><PERSON> kone<PERSON>i"}, "tooltip_modal": {"reward_rate": {"title": "<PERSON><PERSON> reward", "tooltip": "Peningkatan nilai stake tahunan yang di<PERSON>, berda<PERSON><PERSON> pada nilai reward selama seminggu terakhir."}, "estimated_gas_fee": {"title": "Estimasi biaya gas", "gas_recipient": "Biaya gas dibayarkan kepada penambang kripto yang memproses transaksi di jaringan Ethereum. MetaMask tidak mengambil keuntungan dari biaya gas.", "gas_fluctuation": "Biaya gas diperkirakan dan akan berfluktuasi berdasarkan lalu lintas jaringan dan kompleksitas transaksi.", "gas_learn_more": "Pelajari selengkapnya seputar biaya gas"}, "reward_frequency": {"title": "Frekuensi reward", "tooltip": "<PERSON><PERSON> yang di-stake diperbarui setiap 12 jam untuk memperhitungkan reward baru."}, "unstaking_time": {"title": "Waktu pembatalan stake", "tooltip": "Biasanya diperlukan waktu kurang dari 3 hari untuk membatalkan stake ETH, tetapi butuh waktu hingga 11 hari untuk memprosesnya. <PERSON><PERSON><PERSON> pastinya bergantung pada jumlah stake yang dibatalkan dan aktivitas stake ETH"}}, "confirm": {"cancel": "Batalkan", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "staking_footer": {"part1": "<PERSON><PERSON>, <PERSON><PERSON> ", "terms_of_use": "<PERSON><PERSON><PERSON><PERSON>", "part2": " dan ", "risk_disclosure": "Pengungka<PERSON>", "part3": "."}, "label": {"amount": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "balance": "<PERSON><PERSON>", "interacting_with": "Berinteraks<PERSON> den<PERSON>", "network": "<PERSON><PERSON><PERSON>", "primary_type": "Tipe primer", "request_from": "<PERSON><PERSON><PERSON><PERSON> dari", "signing_in_with": "<PERSON><PERSON><PERSON>", "spender": "<PERSON>pender", "now": "<PERSON><PERSON><PERSON>", "switching_to": "<PERSON><PERSON><PERSON>", "bridge_estimated_time": "Estimasi waktu", "pay_with": "<PERSON><PERSON>", "total": "Total", "transaction_fee": "Transaction fee", "metamask_fee": "Biaya MetaMask", "network_fee": "<PERSON><PERSON><PERSON>", "bridge_fee": "Bridge provider fee"}, "title": {"signature": "<PERSON><PERSON><PERSON><PERSON> tanda tangan", "permit": "Permintaan batas penggunaan", "permit_revoke": "<PERSON><PERSON>", "permit_NFTs": "Permintaan penarikan", "signature_siwe": "<PERSON><PERSON><PERSON><PERSON> masuk", "contract_interaction": "<PERSON><PERSON><PERSON><PERSON>", "contract_deployment": "Terapkan kontrak", "transfer": "Permintaan transfer", "switch_account_type": "Pembaruan akun", "approve": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>", "perps_deposit": "<PERSON><PERSON><PERSON> dana"}, "sub_title": {"permit": "Situs ini meminta izin untuk menggunakan token Anda.", "permit_revoke": "<PERSON><PERSON> men<PERSON> izin seseorang untuk menggunakan token dari akun.", "permit_NFTs": "Situs ini meminta izin untuk menarik NFT Anda.", "permit_revoke_NFTs": "Situs ini ingin mereset batas penarikan untuk NFT Anda", "signature": "Tinjau detail permintaan sebelum mengonfirmasi.", "signature_siwe": "Sebuah situs ingin Anda masuk untuk membuktikan bahwa Anda merupakan pemilik akun ini.", "contract_interaction": "Tinjau detail permintaan sebelum mengonfirmasi.", "switch_to_smart_account": "Anda beralih ke akun cerdas.", "switch_to_standard_account": "<PERSON>a beralih kembali ke akun standar (EOA).", "contract_deployment": "Situs ini ingin Anda menerapkan kontrak", "decrease_allowance": "Situs ini ingin menurunkan batas penggunaan untuk token Anda."}, "tooltip": {"perps_deposit": {"transaction_fee": "We'll swap your tokens for USDC on HyperEVM, the network used by Perps. Swap providers may charge a fee, but MetaMask won't."}, "title": {"transaction_fee": "Biaya"}}, "spending_cap": "<PERSON><PERSON>", "withdraw": "<PERSON><PERSON>", "nfts": "NFT", "permission_from": "<PERSON><PERSON> dari", "spender": "<PERSON>pender", "request_from": "<PERSON><PERSON><PERSON><PERSON> dari", "staking_from": "Stake dari", "signing_in_with": "<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON>", "personal_sign_tooltip": "Situs ini meminta tanda tangan Anda", "transaction_tooltip": "Situs ini meminta transaksi Anda", "details": "Detail", "qr_get_sign": "Dapatkan Tanda Tangan", "qr_scan_text": "Pindai dengan dompet perangkat keras", "sign_with_ledger": "Tandatangani dengan Ledger", "smart_account": "<PERSON><PERSON><PERSON>", "smart_contract": "<PERSON><PERSON><PERSON> c<PERSON>", "standard_account": "<PERSON><PERSON><PERSON>", "siwe_message": {"url": "URL", "network": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON>", "chain_id": "ID Chain", "nonce": "<PERSON><PERSON>", "issued": "Diterbitkan", "requestId": "ID Permintaan", "resources": "Sumber daya"}, "simulation": {"decoded_tooltip_bid_nft": "NFT akan terlihat di dompet Anda saat tawaran diterima.", "decoded_tooltip_list_nft": "Nantikan perubahan hanya jika seseorang membeli NFT Anda.", "edit_value_balance_info": "<PERSON><PERSON>:", "info_permit": "Anda memberi izin kepada spender untuk menggunakan sejumlah token ini dari akun <PERSON>.", "info_revoke": "<PERSON><PERSON> men<PERSON> izin seseorang untuk menggunakan token dari akun.", "label_change_type_bidding": "<PERSON><PERSON>", "label_change_type_listing": "<PERSON><PERSON> men<PERSON>", "label_change_type_nft_listing": "<PERSON>rga yang terdaftar", "label_change_type_permit": "<PERSON><PERSON>", "label_change_type_permit_nft": "<PERSON><PERSON>", "label_change_type_receive": "<PERSON><PERSON>", "label_change_type_revoke": "<PERSON><PERSON><PERSON>", "label_change_type_transfer": "<PERSON><PERSON>", "label_change_type_approve": "<PERSON><PERSON>", "personal_sign_info": "<PERSON>a masuk ke sebuah situs dan tidak ada perubahan yang terprediksi pada akun Anda.", "title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> perubahan merupakan hal yang mungkin terjadi jika Anda melakukan transaksi ini. Ini hanyalah prediksi, bukan jaminan.", "unavailable": "Tidak tersedia"}, "7702_functionality": {"smartAccountLabel": "<PERSON><PERSON><PERSON>", "standardAccountLabel": "<PERSON><PERSON><PERSON>", "switch": "<PERSON><PERSON><PERSON>", "switchBack": "<PERSON><PERSON><PERSON> kem<PERSON>", "splashpage": {"accept": "Ya", "betterTransaction": "Transaksi lebih cepat, biaya lebih rendah", "betterTransactionDescription": "<PERSON><PERSON> waktu dan uang dengan memproses trans<PERSON>i bersama-sama.", "featuresDescription": "Pertahankan alamat akun yang sama dan Anda dapat beralih kembali setiap saat.", "payToken": "<PERSON>ar dengan token apa pun, setiap saat", "payTokenDescription": "Gunakan token yang sudah Anda miliki untuk menutupi biaya jaringan.", "reject": "Tidak", "sameAccount": "<PERSON><PERSON><PERSON> sama, fitur lebih cerdas.", "splashTitle": "<PERSON>akan akun cerdas?"}, "includes_transaction": "Mencakup {{transactionCount}} transaksi", "useSmartAccount": "<PERSON><PERSON><PERSON> akun cerdas", "successful": "Ber<PERSON>il!", "success_message": "<PERSON><PERSON>n Anda akan diperbarui ke akun cerdas pada transaksi berikutnya."}, "edit_spending_cap_modal": {"account_balance": "<PERSON><PERSON>n", "cancel": "Batalkan", "description": "<PERSON><PERSON><PERSON><PERSON> jumlah yang paling sesuai untuk digunakan atas nama Anda.", "invalid_number_error": "Batas penggunaan harus berupa angka", "no_empty_error": "Batas penggunaan tidak boleh kosong", "no_extra_decimals_error": "Batas penggunaan tidak boleh memiliki desimal lebih banyak dari token", "no_zero_error": "Batas penggunaan tidak boleh 0", "no_zero_error_decrease_allowance": "Batas penggunaan 0 tidak berpengaruh pada metode 'decreaseAllowance'", "no_zero_error_increase_allowance": "Batas penggunaan 0 tidak berpengaruh pada metode 'increaseAllowance'", "save": "Simpan", "title": "<PERSON> batas per<PERSON>"}, "unlimited": "Tak terbatas", "all": "<PERSON><PERSON><PERSON>", "none": "Kosong", "advanced_details": "Detail lanjutan", "interacting_with": "Berinteraks<PERSON> den<PERSON>", "data": "Data", "review": "Tinjau", "transferRequest": "Permintaan transfer", "nested_transaction_heading": "Transaksi {{index}}", "transaction": "Transaksi", "available_balance": "Tersedia: ", "edit_amount_done": "Se<PERSON><PERSON>", "deposit_edit_amount_done": "Lanjutkan"}, "change_in_simulation_modal": {"title": "<PERSON><PERSON> te<PERSON> be<PERSON>h", "description": "Estimasi perubahan untuk transaksi ini telah diperbarui. <PERSON><PERSON><PERSON> dengan saksama sebelum melanjutkan.", "proceed": "Lanjutkan", "reject": "<PERSON><PERSON>"}, "snap_account_custom_name_approval": {"title": "Tambahkan akun ke MetaMask", "input_title": "<PERSON><PERSON> akun", "add_account_button": "Tambah akun", "name_taken_message": "Nama akun ini sudah ada"}, "smart_transactions_migration": {"title": "Transaksi kini menjadi lebih pintar", "link": "Tingkat keberhasilan lebih tinggi", "description": " dan per<PERSON><PERSON>an MEV. Kini aktif secara default."}, "bridge": {"continue": "Lanjutkan", "confirm_bridge": "Bridge", "confirm_swap": "<PERSON><PERSON><PERSON>", "terms_and_conditions": "Syarat & Ketentuan", "select_token": "<PERSON><PERSON><PERSON> token", "select_network": "<PERSON><PERSON><PERSON>", "all_networks": "<PERSON><PERSON><PERSON>", "num_networks": "{{numNetworks}} jar<PERSON>n", "one_network": "1 jaringan", "select_all_networks": "<PERSON><PERSON><PERSON> se<PERSON>a", "deselect_all_networks": "<PERSON><PERSON> pilih semua", "see_all": "<PERSON><PERSON>a", "apply": "Terapkan", "slippage": "<PERSON><PERSON>", "slippage_info": "Jika harga berubah antara waktu penempatan dan konfirmasi order Anda, ini disebut “selip”. Pertukaran akan otomatis dibatalkan jika selip melebihi toleransi yang Anda tetapkan di sini.", "network_fee": "<PERSON><PERSON><PERSON>", "included": "Termasuk", "estimated_time": "Estimasi <PERSON>", "quote": "<PERSON><PERSON><PERSON>", "rate": "Ting<PERSON>", "quote_details": "<PERSON><PERSON>", "price_impact": "<PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON><PERSON>", "quote_info_content": "The best rate we found from providers, including provider fees and a 0.875% MetaMask fee.", "quote_info_title": "Ting<PERSON>", "network_fee_info_title": "<PERSON><PERSON><PERSON>", "network_fee_info_content": "Network fees depend on how busy the network is and how complex your transaction is.", "points": "Poin", "points_tooltip": "Poin", "points_tooltip_content": "Estimasi Poin Reward MetaMask yang akan Anda peroleh dari trade ini. Poin mungkin memerlukan waktu hingga 1 jam untuk dikonfirmasi di saldo <PERSON>ward Anda", "unable_to_load": "Unable to load", "points_error": "We can't load points right now", "points_error_content": "You'll still earn any points for this transaction. We'll notify you once they've been added to your account. You can also check your rewards tab in about an hour.", "see_other_quotes": "<PERSON><PERSON> kuotasi la<PERSON>", "receive_at": "Terima di", "error_banner_description": "Rute perdagangan ini tidak tersedia untuk saat ini. <PERSON><PERSON> ubah jumlah, jar<PERSON><PERSON>, atau <PERSON>, dan kami akan mencari opsi terbaik.", "insufficient_funds": "<PERSON> tidak cukup", "insufficient_gas": "Gas tidak cukup", "select_amount": "<PERSON><PERSON><PERSON> j<PERSON>", "bridge_to": "Bridge ke", "swap_to": "<PERSON><PERSON> ke", "title": "Bridge", "submitting_transaction": "Mengirim", "fetching_quote": "Mengambil kuotasi", "fee_disclaimer": "Termasuk biaya MM 0,875%", "hardware_wallet_not_supported": "Dompet perangkat keras belum didukung. Gunakan hot wallet untuk melanjutkan.", "hardware_wallet_not_supported_solana": "Dompet perangkat keras belum didukung untuk Solana. Gunakan hot wallet untuk melanjutkan.", "price_impact_info_title": "<PERSON><PERSON><PERSON>", "price_impact_info_description": "Dampak harga mencerminkan bagaimana order swap Anda memengaruhi harga pasar aset. Hal ini bergantung pada ukuran perdagangan dan likuiditas yang tersedia di pool. MetaMask tidak memengaruhi atau mengontrol dampak harga.", "price_impact_info_gasless_description": "Price impact reflects how your swap order affects the market price of the asset. If you don't hold enough funds for gas, part of your source token is automatically allocated to cover fees, which increases price impact. MetaMask does not influence or control price impact.", "slippage_info_title": "<PERSON><PERSON>", "slippage_info_description": "The % change in price you're willing to allow before your transaction is canceled.", "blockaid_error_title": "Transaksi ini akan dike<PERSON>an", "max": "<PERSON><PERSON>", "approval_needed": "This will approve {{amount}} {{symbol}} for swapping.", "approval_tooltip_title": "Grant exact access", "approval_tooltip_content": "You are allowing access to the specified amount, {{amount}} {{symbol}}. The contract will not access any additional funds.", "minimum_received": "Minimum Received", "minimum_received_tooltip_title": "Minimum Received", "minimum_received_tooltip_content": "The minimum amount you'll receive if the price changes while your transaction is processing, based on your slippage tolerance. This is an estimate from our liquidity providers. Final amounts may differ."}, "quote_expired_modal": {"title": "Kuotasi baru tersedia", "description": "<PERSON>rga diperbarui setiap {{refreshRate}} detik, jadi, ketuk Dapatkan kuotasi baru saat sudah siap.", "get_new_quote": "Dapatkan kuotasi baru"}, "blockaid_modal": {"simulation_title": "<PERSON><PERSON><PERSON> gagal dalam simulasi", "validation_title": "Transaksi gagal di<PERSON>i", "go_back": "Kembali"}, "bridge_transaction_details": {"status": "Status", "date": "Tanggal", "total_gas_fee": "Total biaya gas", "estimated_completion": "<PERSON><PERSON><PERSON>", "bridge_step_action_bridge_complete": "{{destSymbol}} diterima pada {{destChainName}}", "bridge_step_action_bridge_pending": "<PERSON>eri<PERSON> {{destSymbol}} pada {{destChainName}}", "bridge_step_action_swap_complete": "Menukar {{srcSymbol}} dengan {{destSymbol}}", "bridge_step_action_swap_pending": "Menukar {{srcSymbol}} dengan {{destSymbol}}", "view_on_block_explorer": "Lihat di Block Explorer", "block_explorer_description": "Transaksi ini berlangsung di dua jaringan. <PERSON>tan pertama menampilkan sumbernya; tautan kedua menampilkan tujuannya setelah dikonfirmasi.", "transaction_details": "Detail Transaksi", "bridge_to_chain": "Bridge ke {{chainName}}", "recipient": "<PERSON><PERSON><PERSON>"}, "account_status": {"account_already_exists": "Dompet sudah ada", "account_already_exists_description": "Dompet menggunakan \"{{accountName}}\" sudah ada. Mau coba masuk?", "log_in": "<PERSON><PERSON><PERSON>", "account_not_found": "Dompet tidak ditem<PERSON>n", "account_not_found_description": "<PERSON>mi tidak dapat menemukan dompet untuk \"{{accountName}}\". Ingin membuat yang baru dengan login ini?", "create_new_wallet": "Buat dompet baru", "use_different_login_method": "<PERSON>akan metode login yang berbeda"}, "error_sheet": {"still_there_title": "Masih di situ?", "still_there_description": "Waktu login habis karena tidak aktif. Coba lagi saat Anda siap.", "unable_to_login_title": "Tidak dapat terhubung", "unable_to_login_description": "Koneksi internet Anda tidak stabil. Periksa koneksi dan coba lagi.", "something_went_wrong_title": "<PERSON><PERSON><PERSON><PERSON>", "something_went_wrong_description": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat masuk. Coba lagi dan jika masalah be<PERSON>, hubungi", "support_button": "Dukungan MetaMask.", "error_button": "Coba lagi", "user_cancelled_title": "<PERSON><PERSON>", "user_cancelled_description": "<PERSON><PERSON> me<PERSON> proses login.\nCoba lagi saat Anda siap.", "user_cancelled_button": "Coba lagi", "google_login_no_credential_title": "Login <PERSON> gagal", "google_login_no_credential_description": "<PERSON>mi tidak dapat menemukan akun Google yang terkait dengan login ini. Coba lagi dengan metode login yang berbeda.", "google_login_no_credential_button": "Coba lagi", "oauth_error_title": "<PERSON>gin gagal", "oauth_error_description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat masuk.\nCoba lagi dan jika masalah be<PERSON>, hub<PERSON>i Du<PERSON>n MetaMask.", "oauth_error_button": "Coba lagi", "no_internet_connection_title": "Tidak dapat terhubung", "no_internet_connection_description": "Koneksi internet Anda tidak stabil. Periksa koneksi dan coba lagi.", "no_internet_connection_button": "Coba lagi"}, "password_hint": {"title": "Petunjuk kata sandi", "description": "Berikan petunjuk untuk membantu Anda mengingat kata sandi. Petunjuk ini disimpan di perangkat dan tidak akan dibagikan.", "description2": "Ingat: <PERSON><PERSON> kehilangan kata sandi, <PERSON><PERSON> tidak dapat menggunakan dompet.", "button": "<PERSON><PERSON><PERSON>", "placeholder": "mis. rumah ibu", "saved": "Simpan", "saved_toast": "Petunjuk kata sandi diperbarui", "error_matches_password": "Anda tidak dapat menggunakan kata sandi sebagai petunjuk"}, "protect_your_wallet": {"title": "<PERSON><PERSON><PERSON><PERSON>", "login_with_social": "<PERSON><PERSON>k dengan Akun <PERSON>", "setup": "Atur", "secret_recovery_phrase": "Frasa pemulihan rahasia {{num}}", "back_up": "Cadangkan", "reveal": "<PERSON><PERSON><PERSON><PERSON>", "social_recovery_title": "PEMULIHAN {{authConnection}}", "social_recovery_enable": "Diaktifkan", "social_login_description": "<PERSON><PERSON><PERSON> login {{authConnection}} dan kata sandi MetaMask untuk memulihkan akun dan frasa pemulihan rahasia.", "srps_title": "FRASA PEMULIHAN RAHASIA", "srps_description": "<PERSON><PERSON> Anda akan sangat terlindungi jika kedua metode pemulihan diaktifkan. <PERSON><PERSON> salah satu gagal, metode lainnya akan membantu Anda memulihkan dompet."}, "backupAndSync": {"title": "Pencadangan dan <PERSON>", "description": "Cadangkan akun Anda dan sinkronkan pengaturan.", "enabling": "Mengaktifkan pencadangan dan <PERSON>", "disabling": "Menonaktifkan pen<PERSON>dangan dan <PERSON>", "enable": {"title": "Aktifkan pen<PERSON>dangan dan <PERSON>", "confirmation": "Saat mengaktifkan pencadangan dan <PERSON>, <PERSON>a juga mengaktifkan fungsionalitas dasar. Lanjutkan?", "description": "Pencadangan dan sinkronisasi memungkinkan kami menyimpan data terenkripsi untuk pengaturan dan fitur khusus Anda. Ini menjaga pengalaman MetaMask Anda tetap sama di semua perangkat serta memulihkan pengaturan dan fiturnya jika Anda perlu menginstal ulang MetaMask. Ini tidak mencadangkan Frasa Pemulihan Rahasia.", "updatePreferences": "<PERSON>a dapat memperbarui preferensi setiap saat di", "settingsPath": "Pengaturan > Pencadangan dan sink<PERSON>."}, "privacyLink": "<PERSON><PERSON><PERSON>i cara kami melindungi privasi Anda", "features": {"accounts": "<PERSON><PERSON><PERSON>", "contacts": "Kontak"}, "manageWhatYouSync": {"title": "<PERSON><PERSON><PERSON> yang <PERSON>", "description": "Aktifkan yang disinkronkan antara perangkat Anda."}}, "snap_ui": {"asset_selector": {"title": "<PERSON><PERSON><PERSON> aset"}, "account_selector": {"title": "<PERSON><PERSON><PERSON> akun"}, "dropdown": {"title": "<PERSON><PERSON>h opsi"}, "hideSentitiveInfo": {"message": "Sembunyikan informasi sensitif"}, "doNotShare": {"message": "<PERSON><PERSON> bagikan ini dengan siapa pun"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON><PERSON> konten sensitif"}, "show_more": "la<PERSON><PERSON>", "show_less": "ciutkan"}, "multichain_accounts": {"intro": {"title": "Introducing multichain accounts", "section_1_title": "What are multichain accounts?", "section_1_description": "One account, addresses on multiple networks MetaMask supports. So now you can use Ethereum, Solana, and more without switching accounts.", "section_2_title": "Same address, more networks", "section_2_description": "We’ve merged your accounts. You can keep using MetaMask the same way as before. Your funds are safe and unchanged.", "view_accounts_button": "View accounts", "learn_more_button": "Learn more"}, "learn_more": {"title": "Learn more", "description": "Multichain accounts are now the default. To opt out, turn off basic functionality.", "checkbox_label": "Turn off basic functionality", "confirm_button": "Confirm"}, "add_wallet": "Tambahkan dompet", "add_hardware_wallet": "Tambahkan dompet perangkat keras", "account_details": {"header_title": "Detail Akun", "account_name": "<PERSON><PERSON>", "networks": "<PERSON><PERSON><PERSON>", "account_address": "<PERSON><PERSON><PERSON>", "wallet": "Dompet", "private_key": "<PERSON><PERSON><PERSON> pribadi", "private_keys": "<PERSON><PERSON><PERSON> pribadi", "unlock_to_reveal": "Buka untuk menampilkan", "smart_account": "<PERSON><PERSON><PERSON> cerdas", "set_up": "Atur", "secret_recovery_phrase": "<PERSON><PERSON>", "back_up": "Cadangkan", "remove_account": "<PERSON><PERSON> akun"}, "address_list": {"addresses": "<PERSON><PERSON><PERSON>", "receiving_address": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON> disalin"}, "private_key_list": {"list_title": "<PERSON><PERSON><PERSON> pribadi", "warning_title": "<PERSON><PERSON> bagikan kunci pribadi Anda", "warning_description": "Kunci ini memberikan kontrol penuh atas akun Anda untuk chain yang terkait.", "learn_more": "Selengkapnya", "enter_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "password_placeholder": "<PERSON>a sandi", "wrong_password": "Kata sandi salah", "copied": "<PERSON>nci pribadi disalin", "continue": "Lanjutkan", "cancel": "<PERSON><PERSON>"}, "accounts_list": {"details": "Detail"}, "wallet_details": {"wallet_name": "<PERSON><PERSON>", "balance": "<PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON> akun", "creating_account": "Membuat akun...", "back_up": "Cadangkan", "reveal_recovery_phrase_with_index": "<PERSON><PERSON><PERSON><PERSON> {{index}}"}, "smart_account": {"title": "Aktifkan Akun <PERSON>", "description": "<PERSON>a dapat mengaktifkan fitur akun cerdas pada jaringan yang didukung.", "learn_more": "<PERSON><PERSON><PERSON><PERSON>"}, "edit_account_name": {"title": "<PERSON> <PERSON>", "account_name": "<PERSON><PERSON> akun", "confirm_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "save_button": "Simpan", "error": "<PERSON><PERSON> mengedit nama akun", "error_duplicate_name": "This name is already in use.", "error_empty_name": "<PERSON>a akun tidak boleh kosong"}, "delete_account": {"title": "Hapus Akun", "warning_title": "<PERSON>kun ini akan di<PERSON>pus dari <PERSON>.", "warning_description": "Pastikan Anda memiliki Frasa Pemulihan Rahas<PERSON> atau kunci pribadi untuk akun ini sebelum menghapusnya.", "remove_button": "Hapus", "cancel_button": "<PERSON><PERSON>", "error": "<PERSON><PERSON> men<PERSON><PERSON> akun"}, "share_address": {"title": "Bagikan Alamat", "copy_address": "<PERSON><PERSON>", "view_on_explorer_button": "<PERSON><PERSON> di {{explorer}}", "view_on_block_explorer": "Lihat di Block Explorer"}, "share_address_qr": {"title": "Alamat {{networkName}}", "copy_address": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> alamat ini untuk menerima token dan koleksi di", "description_prefix": "<PERSON><PERSON><PERSON> alamat ini untuk menerima token dan koleksi di"}, "export_credentials": {"export_private_key": "<PERSON><PERSON><PERSON> pribadi", "private_key_warning_title": "<PERSON>an pernah mengungkapkan kunci ini.", "private_key_warning_description": "Siapa pun yang mengetahui kunci pribadi Anda dapat mencuri segala aset yang tersimpan di akun.", "credential_as_text": "Teks", "credential_as_qr": "Kode QR", "export_mnemonic": "Ekspor mnemonik", "backup": "Cadangan"}, "reveal_private_key": {"title": "<PERSON><PERSON><PERSON><PERSON> kunci pribadi", "banner_title": "<PERSON>an pernah mengungkapkan kunci ini.", "banner_description": "Siapa pun yang mengetahui kunci pribadi Anda dapat mencuri segala aset yang tersimpan dalam akun.", "enter_password": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "password_placeholder": "<PERSON>a sandi", "next": "Berikutnya", "copy": "<PERSON><PERSON>"}, "reveal_srp": {"header": "<PERSON><PERSON> k<PERSON>", "description": "Untuk mengungkapkan Frasa <PERSON>, <PERSON><PERSON> perlu menjawab dua pertanyaan dengan benar", "get_started": "<PERSON><PERSON>", "learn_more": "Selengkapnya"}, "address_rows_list": {"search_placeholder": "<PERSON><PERSON> jar<PERSON>n", "no_networks_found": "<PERSON><PERSON><PERSON> tidak <PERSON>", "no_networks_available": "<PERSON><PERSON><PERSON> tidak tersedia"}}, "deep_link_modal": {"private_link": {"title": "Mengalihkan ke MetaMask", "description": "<PERSON>a akan membuka {{pageTitle}} jika melanjutkan.", "checkbox_label": "<PERSON>an ingatkan lagi"}, "public_link": {"title": "Lanju<PERSON><PERSON> dengan hati-hati", "description": "Anda di<PERSON> ke sini oleh pihak ketiga, bukan <PERSON>. Anda akan membuka {{pageTitle}} jika melanjutkan."}, "invalid": {"title": "Halaman ini tidak ada", "description": "<PERSON>mi tidak dapat menemukan halaman yang Anda cari.", "update_to_store_link": "Update to the latest version of MetaMask", "well_take_you_to_right_place": " and we'll take you to the right place."}, "go_to_home_button": "Go to the home page", "back_button": "Kembali", "continue_button": "Lanjutkan"}, "card": {"card": "Kartu MetaMask", "add_funds_bottomsheet": {"deposit": "<PERSON><PERSON> dengan uang tunai", "deposit_description": "Kartu berbiaya rendah atau transfer bank", "swap": "<PERSON><PERSON> den<PERSON> k<PERSON>to", "swap_description": "<PERSON><PERSON> token menjadi {{symbol}} di Linea", "select_method": "<PERSON><PERSON><PERSON> metode"}, "card_home": {"error_title": "Tidak dapat mengambil data", "error_description": "Tampaknya ada masalah yang mencegah Anda melihat konten di halaman ini. Periksa koneksi Anda atau coba segarkan halaman.", "try_again": "Coba lagi", "limited_spending_warning": "Kemamp<PERSON> pemakaian Anda mungkin terbatas. Untuk menyesuaikan limit, buka {{manageCard}}", "add_funds": "<PERSON><PERSON><PERSON> dana", "manage_card_options": {"manage_card": "Kelola kartu", "advanced_card_management_description": "<PERSON><PERSON>, beku<PERSON> kartu, dan la<PERSON>ya"}}}, "onboarding_error_fallback": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kirimkan laporan kesalahan kepada kami untuk membantu memperbaiki masalah dan meningkatkan MetaMask. Laporan ini akan bersifat rahasia dan anonim.", "recovery_warning": "<PERSON>ka Anda terus mengalami k<PERSON>han ini, simpan <PERSON> dan instal ulang aplikasi. Ingat: tanpa <PERSON>, <PERSON><PERSON> tidak dapat memuli<PERSON>kan dompet.", "error_message_report": "<PERSON><PERSON><PERSON> kes<PERSON>han:", "copy": "<PERSON><PERSON>", "send_report": "<PERSON><PERSON>", "try_again": "Coba lagi", "report_submitted": "<PERSON><PERSON><PERSON> kes<PERSON>han telah di<PERSON>."}, "pay_with_modal": {"title": "<PERSON><PERSON><PERSON> metode pembayaran"}, "connection_removed_modal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "Beberapa koneksi (seperti dompet perangkat keras dan snap) telah dihapus karena tidak aktif di perangkat ini. Anda dapat menambahkannya kembali setiap saat di Pengaturan.", "tryAgain": "Coba lagi", "close": "<PERSON><PERSON><PERSON>"}, "rewards": {"auth_fail_title": "Unknown Error.", "auth_fail_description": "An unknown error occurred while authenticating this account with the rewards program. Please try again later.", "failed_to_authenticate": "Failed to authenticate with rewards program", "not_implemented": "<PERSON><PERSON><PERSON> hadir", "not_implemented_season_summary": "<PERSON><PERSON><PERSON>", "referral_rewards_title": "Rujukan", "points": "Poin", "point": "Poin", "level": "Level", "to_level_up": "Untuk naik level", "season_ends": "<PERSON><PERSON><PERSON>", "season_ended": "<PERSON><PERSON><PERSON>", "main_title": "<PERSON><PERSON>", "referral_title": "Rujukan", "tab_overview_title": "<PERSON><PERSON><PERSON><PERSON>", "tab_activity_title": "Aktivitas", "tab_levels_title": "Level", "referral_stats_earned_from_referrals": "<PERSON><PERSON><PERSON><PERSON> dari rujukan", "referral_stats_referrals": "Rujukan", "loading_activity": "Loading activity...", "error_loading_activity": "Error loading activity", "activity_empty_title": "No recent activity.", "activity_empty_description": "Use MetaMask to earn points, level up, and unlock rewards.", "activity_empty_link": "See ways to earn", "toast_dismiss": "<PERSON><PERSON><PERSON>", "events": {"type": {"swap": "<PERSON><PERSON><PERSON>", "perps": "Per<PERSON>", "referral": "Referral", "referral_action": "Referral action", "sign_up_bonus": "Sign up bonus", "loyalty_bonus": "Loyalty bonus", "one_time_bonus": "One-time bonus", "open_position": "Opened position", "close_position": "Closed position", "take_profit": "Take profit", "stop_loss": "Stop loss", "uncategorized_event": "Uncategorized event"}}, "onboarding": {"not_supported_region_title": "Wilayah tidak didukung", "not_supported_region_description": "Rewards are not supported in your region yet. We are working on expanding access, so check back later.", "not_supported_account_needed_title": "Ethereum account needed", "not_supported_account_needed_description": "MetaMask Rewards aren't available for Solana accounts yet. Switch to an Ethereum account to claim your points.", "not_supported_confirm": "<PERSON><PERSON><PERSON>", "intro_title_1": "Season 1", "intro_title_2": "is Live", "intro_description": "Earn points for your your activity. \nAdvance through levels to unlock rewards.", "intro_confirm": "<PERSON><PERSON><PERSON> 250 points", "intro_confirm_geo_loading": "Checking region...", "checking_opt_in": "Checking opt-in for accounts...", "redirecting_to_dashboard": "Redirecting to dashboard...", "intro_skip": "Tidak se<PERSON>", "step_confirm": "Berikutnya", "step1_title": "Earn points on every trade", "step1_description": "Every swap and perps trade you make in MetaMask gets you closer to rewards. Link your accounts and watch your points add up.", "step2_title": "Level up for bigger perks", "step2_description": "Hit points milestones to get perks like 50% off perps fees, exclusive tokens, and a free MetaMask Metal Card.", "step3_title": "Exclusive seasonal rewards", "step3_description": "Each season brings new perks. Join in, compete, and claim what you can before time runs out.", "step4_title": "You'll earn 250 points when you sign up!", "step4_title_referral_bonus": "You'll earn 500 points when you sign up with a code!", "step4_title_referral_validating": "Validating referral code...", "step4_referral_bonus_description": "Use a referral code to earn 250 points", "step4_referral_input_placeholder": "Referral code (optional)", "step4_confirm": "<PERSON>laim points", "step4_confirm_loading": "Claiming points...", "step4_linking_accounts": "Linking accounts... ({{current}}/{{total}})", "step4_linking_accounts_loading": "Linking additional accounts...", "step4_success_description": "You have successfully signed up for MetaMask Rewards!", "step4_legal_disclaimer": "Joining means we'll track your on-chain activity to reward you automatically.", "step4_legal_disclaimer_learn_more": "Selengkapnya."}, "settings": {"title": "<PERSON><PERSON>s", "subtitle": "Connect multiple accounts to combine your points and unlock rewards faster.", "error_title": "Unable to Load Linked Accounts", "error_description": "We couldn't detect your linked accounts. Please check your internet connection and try again.", "error_retry": "Retry", "tab_linked_accounts": "Linked Accounts ({{count}})", "tab_unlinked_accounts": "Unlinked Accounts ({{count}})", "no_linked_accounts": "No linked accounts", "all_accounts_linked_title": "All accounts are linked", "all_accounts_linked_description": "You have linked all your accounts to the rewards program.", "link_account_success_title": "Account {{accountName}} successfully linked", "link_account_error_title": "Failed to link account", "link_account_button": "Link"}, "optout": {"title": "Opt out of Rewards", "description": "This will erase your points and progress. You won't be able to undo this.", "confirm": "Opt out", "modal": {"confirmation_title": "Are you sure?", "confirmation_description": "This will remove all your progress, and can't be reversed. If you rejoin the Rewards program later, you'll restart at 0.", "cancel": "Cancel", "confirm": "Confirm", "error_message": "Failed to opt out of rewards program. Please try again.", "processing": "Processing..."}}, "unlinked_accounts_info": {"title": "There are unlinked accounts", "description": "If you want to earn points for the activity of these accounts you can link them in the settings page.", "go_to_settings": "Go to settings"}, "unlinked_account_info": {"title": "Account not linked", "description": "This account's activity is not being tracked for this season."}, "link_account": "Link account", "linking_account": "Linking...", "ways_to_earn": {"title": "Ways to earn", "supported_networks": "Supported Networks", "swap": {"title": "<PERSON><PERSON><PERSON>", "sheet_title": "Swap tokens", "sheet_description": "Swap tokens on supported networks to earn points for every dollar you trade.", "points": "80 points per $100", "cta_label": "Start a swap"}, "perps": {"title": "Perps", "sheet_title": "Trade perps", "sheet_description": "Earn points on every trade, including opens and closes, stop loss and take profit orders, and margin adjustments.", "points": "10 points per $100", "cta_label": "Start a trade"}, "referrals": {"title": "Referrals", "points": "Get 20% from friends you refer"}}, "referral": {"actions": {"share_referral_link": "Rekomendasikan ke teman", "share_referral_subject": "Gabung dengan MetaMask Rewards"}, "info": {"title": "Bagikan kode Anda untuk mendapatkan lebih banyak", "description": "<PERSON><PERSON> <PERSON>a memperoleh poin bonus dan Anda memperoleh 20% dari reward mereka."}}, "active_boosts_title": "Active Boosts", "season_1": "Season 1"}, "time": {"minutes_format": "{{count}} menit", "minutes_format_plural": "{{count}} menit"}, "transaction_details": {"title": {"perps_deposit": "Akun perp yang didanai", "default": "Detail transaksi"}, "label": {"bridge_fee": "Biaya bridge", "network_fee": "<PERSON><PERSON><PERSON>", "paid_with": "<PERSON><PERSON><PERSON>", "total": "Total"}, "summary_title": {"bridge": "Bridge dari {{sourceSymbol}} ke {{targetSymbol}}", "bridge_approval": "Approve {{approveSymbol}}", "default": "Transaksi", "perps_deposit": "<PERSON><PERSON><PERSON> dana", "swap": "<PERSON><PERSON> token", "swap_approval": "Approve tokens"}}}