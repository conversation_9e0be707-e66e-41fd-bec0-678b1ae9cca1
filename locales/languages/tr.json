{"alert_system": {"alert_modal": {"title": "Uyarı", "checkbox_label": "Riski biliyor ve yine de ilerlemek istiyorum", "got_it_btn": "<PERSON><PERSON><PERSON><PERSON>", "alert_details": "Uyarı Bilgileri"}, "confirm_modal": {"title": "<PERSON><PERSON><PERSON><PERSON>p", "checkbox_label": "Uyarıyı biliyor ve yine de ilerlemek istiyorum", "review_alerts": "Tüm uyarıları incele", "message": "Bu talebi reddetmenizi öneririz. <PERSON><PERSON> ederseniz varlıklarınızı riske atarsınız.", "title_blockaid": "Varlıklarınız risk altında olabilir", "blockaid": {"message": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz. Bu talebi iptal etmenizi öneririz.", "message1": "Bu talebi onaylarsanız bir dolandırıcının varlıklarınızı çekmesine ve harcamasına izin verirsiniz. Varlıklarınızı geri alamazsınız.", "message2": "Varlıklarınızı bir dolandırıcıya gönderiyorsunuz. Devam ederseniz bu varlıkları kaybedeceksiniz.", "message3": "Devam ederseniz OpenSea'de yer alan tüm varlıklarınız riske girebilir.", "message4": "<PERSON><PERSON> ederseniz Blur'de yer alan tüm varlıklarınız riske girebilir.", "message5": "Kötü niyetli bir siteyle etkileşimde bulunuyorsunuz. Devam ederseniz varlıklarınızı kaybedeceksiniz."}}, "inline_alert_label": "Uyarı", "review_alerts": "Uyarıları incele", "review_alert": "Uyarıyı incele", "upgrade_account": {"title": "Hesabınız güncelleniyor", "message": "Hesabınızı akıllı hesap olarak güncelliyorsunuz. Aynı hesap adresini korurken daha hızlı işlemlerin ve daha düşük ağ ücretlerinin kilidini açacaksınız.", "learn_more": "<PERSON>ha fazla bilgi edin"}, "domain_mismatch": {"title": "Şü<PERSON><PERSON> g<PERSON>", "message": "Talepte bulunan site giriş yaptığınız site değil. Bu, oturum açma bilgilerinizi çalma teşebbüsü olabilir."}, "insufficient_balance": {"title": "Para yetersiz", "message": "Hesabınızda ağ ücretlerini ödemek için yeterli %{nativeCurrency} yok.", "buy_action": "%{nativeCurrency} satın al"}, "signed_or_submitted": {"title": "Transaction in progress", "message": "Halen önceki bir işlem imzalanıyor veya gönderiliyor."}, "signed_or_submitted_perps_deposit": {"title": "Deposit in progress", "message": "You already have a deposit in progress. You'll need to wait for it to go through before depositing again."}, "pending_transaction": {"title": "Bekleyen işlem", "message": "<PERSON><PERSON> <PERSON><PERSON>, önceki işlem tamamlanana kadar gerçekleşmeyecek.", "learn_more": "Bir işlemin nasıl iptal edileceğini veya hızlandırılacağını öğrenin."}, "batched_unused_approvals": {"title": "Gereksiz izin", "message": "Bu işlem için gerekli olmamasına rağmen bir başkasına token'lerinizi çekme izni veriyorsunuz."}, "perps_deposit_minimum": {"message": "Minimum 10$"}, "insufficient_pay_token_balance": {"message": "<PERSON><PERSON><PERSON>"}, "insufficient_pay_token_balance_fees": {"message": "Insufficient {{symbol}} to cover fees. Add less funds or select a different token to continue.", "title": "Insufficient funds"}, "insufficient_pay_token_native": {"message": "Insufficient {{ticker}} to cover fees. Select a token on a different network to continue."}, "no_pay_token_quotes": {"message": "Bu ödeme rotası şu anda kullanılamıyor. <PERSON><PERSON><PERSON>, ağı veya token'ı değiştirmeyi deneyin ve sizin için en iyi seçeneği bulalım."}, "perps_hardware_account": {"title": "<PERSON><PERSON> not supported", "message": "Perps doesn't support hardware wallets.\nSwitch accounts to continue funding."}}, "blockaid_banner": {"approval_farming_description": "Bu talebi onaylarsanız dolandırıcılıkla bilinen üçüncü bir taraf tüm varlıklarınızı çalabilir.", "blur_farming_description": "Bu talebi onaylarsanız birisi Blur üzerinde yer alan varlıklarınızı çalabilir.", "deceptive_request_title": "Bu, aldatıcı bir taleptir", "failed_title": "<PERSON>p gü<PERSON> o<PERSON>", "failed_description": "Bu talep bir hatadan dolayı güvenlik sağlayıcısı tarafından doğrulanmadı. Dikkatli bir <PERSON><PERSON><PERSON>.", "malicious_domain_description": "Kötü niyetli bir alanla etkileşimde bulunuyorsunuz. Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz.", "other_description": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz.", "raw_signature_farming_description": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz.", "seaport_farming_description": "Bu talebi onaylarsanız birisi OpenSea üzerinde yer alan varlıklarınızı çalabilir.", "see_details": "Ayrıntıları gör", "does_not_look_right": "Bir şeyler doğru görünmüyor mu?", "report_an_issue": "<PERSON>ir sorun bildir", "suspicious_request_title": "<PERSON><PERSON>, <PERSON><PERSON>heli bir taleptir", "trade_order_farming_description": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz.", "transfer_farming_description": "Bu talebi onaylarsanız dolandırıcılıkla bilinen üçüncü bir taraf tüm varlıklarınızı çalar.", "before_you_proceed": "<PERSON><PERSON> et<PERSON>", "enable_blockaid_alerts": "Bu özelliği etkinleştirmek için bazı güvenlik uyarılarının kurulumunun yapılması gerekiyor. Kurulum yapılırken bu sayfanın açık kalması gerekecek. Bu işlemin tamamlanması bir dakikadan az sürecektir.", "enable_blockaid_alerts_description": "Güvenlik uyarılarının kurulumu yapılırken bu sayfanın açık kalması gerekir. Bu sürecin tamamlanması bir dakikadan kısa sürecektir.", "setting_up_alerts": "Güvenlik uyarılarının kurulumu yapılıyor", "setting_up_alerts_description": "Güvenlik uyarlarının kurulumunu yapmak için elimizden geldiğince hızlı ilerliyoruz. Pes etmeyin, bitmek üzere.", "setup_complete": "<PERSON><PERSON><PERSON> ta<PERSON>", "setup_failed": "Güvenlik uyarılarının ayarlanmasını tamamlayamadık. İnternet bağlantınızı kontrol edip tekrar deneyin.", "setup_multiple_failures": "İnternet bağlantınız şu anda stabil olmadığından güvenlik uyarılarını ayarlayamadık. Lütfen bağlantınız stabil hale gelince tekrar deneyin.", "setup_complete_description": "Güvenlik uyarıları hazır. Bu sayfayı kapatıp cüzdanınızı keşfetmeye devam edebilirsiniz", "continue": "<PERSON><PERSON> et", "cancel": "İptal", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "try_again": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON> ters gitti"}, "date": {"months": {"0": "Oca", "1": "<PERSON><PERSON>", "2": "Mar", "3": "<PERSON><PERSON>", "4": "May", "5": "Haz", "6": "Tem", "7": "<PERSON><PERSON><PERSON>", "8": "<PERSON><PERSON>", "9": "<PERSON><PERSON>", "10": "<PERSON><PERSON>", "11": "Ara"}, "connector": "saat"}, "autocomplete": {"placeholder": "Token'a, siteye veya adrese göre ara", "recents": "Yakın Zamanda Gerçekleşenler", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "sites": "<PERSON><PERSON>", "tokens": "<PERSON><PERSON><PERSON>"}, "navigation": {"back": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "cancel": "İptal", "info": "<PERSON><PERSON><PERSON>", "ok": "<PERSON><PERSON>"}, "onboarding": {"title": "Başlayalım!", "sync_desc": "Zaten MetaMask uzantısı veya başka bir cüzdan aldıysanız mevcut varlıklarınızı yönetmek için onu senkronize edin veya içe aktarın.", "create_desc": "İlk cüzdan kurulumunuzu yapın ve merkeziyetsiz uygulamaları keşfetmeye başlayın.", "import": "Mevcut bir cüzdanı içe aktarın veya yeni bir tane oluşturun.", "import_wallet_button": "Cüzdanı senkronize et veya içe aktar", "new_to_crypto": "<PERSON><PERSON><PERSON>da yeni misiniz?", "start_exploring_now": "<PERSON>ni bir cüzdan oluştur", "unlock": "<PERSON><PERSON><PERSON>", "new_to_metamask": "MetaMask'te yeni misiniz?", "already_have_wallet": "Zaten bir cüzdanınız var mı?", "optin_back_title": "Dikkat!", "optin_back_desc": "Lütfen veri analitiklerinin kullanımını kabul edin veya reddedin. Bu seçeneği ayarlar kısmında da güncelleyebilirsiniz.", "warning_title": "Uyarı", "warning_text_1": "İlerlediğiniz ta<PERSON>dirde", "warning_text_2": "geçerli cüzdan ve hesaplarınız", "warning_text_3": "kaldırılacak.", "warning_text_4": "Onları sadece cüzdanınızın Gizli Kurtarma İfadesi ile kurtarabilirsiniz. MetaMask onu kurtarmanıza yardımcı olamaz.", "warning_proceed": "Cüzdanı kaldır ve ilerle", "warning_cancel": "İptal", "step1": "Cüzdan kurulumu", "step2": "<PERSON><PERSON><PERSON>", "step3": "Cüzdanı güvenli hale getir", "already_have": "Zaten bir cüzdanınız var mı?", "sync_existing": "Tarayıcı uzantısından mevcut MetaMask cüzdanınızı senkronize edin veya manuel olarak içe aktarın.", "scan_title": "MetaMask uzantısı ile senkronize etme adımları", "scan": "Tara", "scan_step_1": "Uzantıyı masaüstünde aç", "scan_step_2": "Ayarlar > G<PERSON>şmiş kısmına gidin", "scan_step_3": "“Mobil ile Senkronize Et” düğmesine tıklayın", "scan_step_4": "Senkronize etmeye başlamak için QR kodunu tarayın", "success": "Başarılı", "continue_with_google": "Google ile Devam Et", "continue_with_apple": "Apple ile Devam Et", "or": "veya", "import_existing_wallet": "Mevcut cüzdanı içe aktar", "bottom_sheet_title": "<PERSON><PERSON> etmek için bir seçenek seçin", "continue_with_srp": "<PERSON><PERSON><PERSON> Kurtarma İfadesi kullanın", "import_srp": "<PERSON><PERSON><PERSON> Kurtarma İfadesini kullanarak içe aktar", "sign_in_with_google": "Google ile Giriş <PERSON>", "sign_in_with_apple": "Apple ile Giriş <PERSON>", "your_wallet": "Cüzdanınızı başarılı bir şekilde sıfırladınız!", "delete_current": "Geçerli cüzdanı sıfırla", "have_existing_wallet": "Mevcut bir cüzdanım var", "import_using_srp": "<PERSON><PERSON><PERSON> Kurtarma İfadesini kullanarak içe aktar", "import_using_srp_social_login": "Mevcut bir cüzdanım var", "by_continuing": "By continuing, you agree to MetaMask's", "terms_of_use": "Kullanım şartları", "privacy_notice": "Privacy notice", "and": "ve"}, "onboarding_success": {"title": "Gizli Kurtarma İfadenizi güvenli bir şekilde saklayın!", "description": "Bu <PERSON><PERSON><PERSON>, şifrenizi unutmanız veya oturumunuza erişimi kaybetmeniz durumunda yeniden erişim kazanmanıza yardımcı olabilir.", "description_bold": "Ayarlar > Güvenlik ve Gizlilik.", "description_continued": "paranıza eri<PERSON><PERSON><PERSON> asla kaybetmemak için bu ifadeyi güvenli bir şekilde saklayabilirsiniz.", "learn_more": "<PERSON>ha fazla bilgi edin", "learn_how": "Na<PERSON>ıl olduğunu öğren", "leave_hint": "<PERSON><PERSON> bir i<PERSON>cu bırak?", "default_settings": "Varsayılan ayarlar", "manage_default_settings": "Varsayılan ayarları yönet", "default_settings_footer": "<PERSON><PERSON><PERSON>, kull<PERSON><PERSON><PERSON> kolaylığı ve güvenlik için optimize edildi.\nBu ayarları dilediğiniz zaman değiştirin.", "no_srp_title": "Hatırlatıcı ayarlandı!", "no_srp_description": "Uygulamaya giriş yapamazsanız veya yeni bir cihaz edinirseniz paranızı kaybedersiniz. Gizli Kurtarma İfadenizi şurada yedeklediğinizden emin olun:", "import_title": "Cüzdanın hazır!", "import_description": "Gizli Kurtarma İfadenizi kaybederseniz cüzdanınızı kullanamayacaksınız.", "import_description2": "paranıza eri<PERSON><PERSON><PERSON> asla kaybetmemak için bu ifadeyi güvenli bir şekilde saklayabilirsiniz.", "import_description_social_login": "{{authConnection}} hesabınızı ve şifrenizi kullanarak dilediğiniz zaman cüzdanınızda oturum açabilirsiniz.", "import_description_social_login_2": "Şifrenizi unutursanız cüzdanınıza eri<PERSON>im <PERSON>ğlayamayacaksınız.", "done": "<PERSON><PERSON>", "create_hint": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "hint_title": "<PERSON><PERSON><PERSON>", "hint_description": "Kendinize şifrenizi hatırlamanıza yardımcı olacak bir ipucu bırakın. Bu ipucu cihazınıza kaydedilecek ve paylaşılmayacaktır.", "hint_description2": "Unutmayın: Şifrenizi kaybederseniz cüzdanınızı kullanamayacaksınız.", "hint_button": "İpucu o<PERSON>ştur", "hint_placeholder": "ör. annemin evi", "hint_saved": "<PERSON><PERSON>", "hint_saved_toast": "<PERSON><PERSON><PERSON>", "remind_later": "Size daha sonra hatırlatacağız", "remind_later_description": "Gizli Kurtarma İfadenizi yedeklemezseniz uygulamanın dışında kalmanız veya yeni bir cihaz edinmeniz durumunda paranıza erişimi ka<PERSON>bed<PERSON>iniz.", "remind_later_description2": "Şurada cüzdanlarınızı yedekleyebilir veya Gizli Kurtarma İfadenizi görebilirsiniz:", "setting_security_privacy": "Ayarlar > Güvenlik ve Gizlilik"}, "onboarding_carousel": {"title1": "MetaMask'e Hoş Geldiniz", "title2": "Dijital varlıklarını yönet", "title3": "web3 ağ geçidiniz", "subtitle1": "Milyonların güvendiği MetaMask web3 dünyasını herkes için erişilebilir kılan güvenli bir cüzdandır.", "subtitle2": "Token, Ethereum, eşsiz koleksiyonlar gibi dijital varlıkları saklayın, harcayın ve gönderin.", "subtitle3": "MetaMask ile oturum açın ve yatırım yapmak, ka<PERSON><PERSON><PERSON>, oyun oynamak ve satmak ve daha fazlası için işlem yapın!", "get_started": "Başla"}, "onboarding_wizard": {"skip_tutorial": "Öğreticiyi Geç", "coachmark": {"action_back": "Hayır, teşekkürler", "action_next": "<PERSON><PERSON>", "progress_back": "<PERSON><PERSON>", "progress_next": "<PERSON><PERSON><PERSON><PERSON>!"}, "step1": {"title": "Yeni cüzdanınıza hoş geldiniz!", "content1": "Blokzincirini kullanmak için bir cüzdana ihtiyacınız var! Bazı eylemlerin maliyeti Ether (ETH) olacaktır.", "content2": "Nasıl ETH satın alabileceğinizi göstereceğiz veya bir arkadaşınızdan biraz isteyebilirsiniz."}, "step2": {"title": "Hesaplarınız", "content1": "<PERSON><PERSON> sizin ilk he<PERSON>b<PERSON><PERSON><PERSON><PERSON>, top<PERSON> değer ve onun eşsiz genel adresi (0x...).", "content2": "Profil sim<PERSON><PERSON> tı<PERSON> bu cüzdan dahilinde birden fazla hesap oluşturabilirsiniz."}, "step3": {"title": "Hesap <PERSON>ı<PERSON>üzenle", "content1": "Hesabınıza hatırlanabilir ve farklı bir isim vermeye ne dersiniz?", "content2": "Ş<PERSON>di", "content3": "hesap adını düzenlemek için uzun dokunun."}, "step4": {"title": "<PERSON>", "content1": "Bu menüden İşlem geçmişi, Ayarlar ve Destek kısımlarına erişim sağlayabilirsiniz.", "content2": "Hesaplarınızla daha fazla işlem yapabilir ve MetaMask ayarlarına erişim sağlayabilirsiniz."}, "step5": {"title": "Tarayıcıyı Keşfet", "content1": "Tarayıcıyı kullanarak web3'ü keşfedebilirsiniz"}, "step6": {"title": "Ara", "content": "Siteleri arayabilir veya nereye yönlendirildiğinizi biliyorsanız bir URL adresi girebilirsiniz."}}, "onboarding_wizard_new": {"coachmark": {"action_back": "Hayır, istemiyorum", "action_next": "<PERSON><PERSON>", "progress_next": "<PERSON><PERSON><PERSON><PERSON>"}, "step1": {"title": "Cüzdanınıza hoş geldiniz!", "content1": "Blokzincirinde bir cüzdana ihtiyacınız vardır (ve belki biraz ETH'ye). <PERSON><PERSON><PERSON><PERSON>, MetaMask cüzdanınızı nasıl kullanacağınıza bakalım."}, "step2": {"title": "Hesaplarınız", "content1": "Bu sizin hesabınız ve eşsiz genel adresinizdir (0x...). Ok simgesine dokunarak daha fazla hesap oluşturun."}, "step3": {"title": "Hesabınızın yö<PERSON>i", "content1": "Bu simgeye dokunarak Etherscan üzerinde hesap adınız<PERSON> dü<PERSON>in, i<PERSON><PERSON><PERSON><PERSON>, genel anahtarınızı paylaşın ve özel anahtarınızı görün"}, "step4": {"title": "Cüzdanınızın kullanımı", "content1": "Bu simgeye dokunarak varlık satın alın, <PERSON><PERSON><PERSON><PERSON>, swap işlemi gerçekleştirin ve varlık alın"}, "step5": {"title": "web3 keşfi", "content1": "Bu simgeye dokunarak tarayıcıyı açın"}, "step6": {"title": "Tarayıcının kullanımı", "content1": "Anahtar sözcükle siteleri arayın veya bir URL adresi girin. İyi eğlenceler! "}, "step7": {"title": "Tarayıcının kullanımı", "content1": "<PERSON><PERSON><PERSON> kelime kull<PERSON> siteleri arayın veya bir URL adresi girin. İyi eğlenceler! "}}, "create_wallet": {"title": "Cüzdanınız oluşturuluyor...", "subtitle": "Bu işlem uzun sürmeyecektir"}, "import_wallet": {"title": "Zaten MetaMask kullanıcısı mısınız?", "sub_title": "Uzantı ile senkronize et", "sync_help": "Uzantı ile cüzdanını senkronize et", "sync_help_step_one": "1. Uzantıyı aç", "sync_help_step_two": "2. <PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON><PERSON><PERSON> kı<PERSON>ına gidin", "sync_help_step_three": "3. “Mobil ile Senkronize Et” dü<PERSON><PERSON>ine tıklayın", "sync_help_step_four": "4. Senkronize etmeye başlamak için QR kodunu tarayın", "sync_from_browser_extension_button": "MetaMask uzantısı ile senkronize et", "or": "VEYA", "import_from_seed_button": "<PERSON><PERSON><PERSON> Kurtarma İfadesini kullanarak içe aktar"}, "login": {"title": "Tekrar Hoş Geldiniz!", "password": "Şifre", "password_placeholder": "MetaMask şifresini girin", "unlock_button": "<PERSON><PERSON><PERSON>", "go_back": "Cüzdanın kilidi açılmıyor mu? Geçerli cüzdanınızı silip yeni bir tanesinin kurulumunu yapabilirsiniz", "forgot_password": "<PERSON><PERSON><PERSON>i mi unuttunuz?", "cant_proceed": "‘Sil’ sözcüğünü yazana kadar ilerleyemezsiniz. Bu eylemle geçerli cüzdanınızı silmeyi seçiyorsunuz.", "invalid_password": "<PERSON><PERSON><PERSON>, te<PERSON><PERSON> den<PERSON>.", "type_delete": "<PERSON><PERSON><PERSON><PERSON> için ‘%{erase}’ yazın", "erase_my": "<PERSON><PERSON>, cüzdanı sıfırla", "cancel": "İptal et", "are_you_sure": "Emin misiniz?", "your_current_wallet": "Cüzdanın<PERSON>z, hesapların<PERSON>z ve varlıklarınız", "removed_from": " bu uygulamadan kalıcı olarak kaldırılacak", "this_action": "<PERSON><PERSON> eylem geri alı<PERSON>.", "you_can_only": "Bu cüzdanı yalnızca Gizli Kurtarma İfadeniz, Google hesabınız veya Apple hesabınız gibi cüzdanınızı oluştururken kullanılan bilgilerle geri alabilirsiniz. MetaMask bu bilgilere sahip değildir.", "recovery_phrase": "G<PERSON><PERSON> Kurtarma İfadenizle kurtarabilirsiniz", "metamask_does_not": "<PERSON><PERSON><PERSON> Kurtarma İfadeniz MetaMask'in içerisinde bulunmaz.", "i_understand": "<PERSON><PERSON><PERSON><PERSON><PERSON>, devam et", "passcode_not_set_error": "Hata: <PERSON><PERSON><PERSON>.", "wrong_password_error": "Hata: <PERSON><PERSON><PERSON> başarısız <PERSON>u", "wrong_password_error_android": "Hata: error:1e000065:Şifreleme fonksiyonları:OPENSSL_internal:BAD_DECRYPT", "vault_error": "Hata: Önceki bir vault o<PERSON><PERSON> k<PERSON>.", "clean_vault_error": "<PERSON><PERSON><PERSON><PERSON>, depolama limitine ulaşılmasından dolayı bir hata ile karşılaştı. <PERSON>rel veriler bozuldu. Lütfen MetaMask'ı tekrar yükleyin ve Gizli Kurtarma İfadenizi kullanarak geri yükleyin.", "seedless_password_outdated": "Şifreniz yakın zamanda değiştirildi.", "seedless_password_outdated_modal_title": "<PERSON><PERSON><PERSON><PERSON>", "seedless_password_outdated_modal_content": "Şifreniz ya<PERSON>ın z<PERSON>, bu nedenle MetaMask oturumunuzun açık kalması için yeni şifrenizi girmeniz gerekiyor.", "seedless_password_outdated_modal_confirm": "<PERSON><PERSON> et", "no_internet_connection": "Seedless account recovery requires internet connection.", "seedless_controller_error_prompt_title": "<PERSON><PERSON> ters gitti", "seedless_controller_error_prompt_description": "An issue occurred while unlocking. Re-login with Google and your MetaMask password.", "seedless_controller_error_prompt_primary_button_label": "Oturum aç", "security_alert_title": "Güvenlik Uyarısı", "security_alert_desc": "İlerlemek için Parolayı veya cihazınızda desteklenen herhangi bir biyometri kimlik doğrulama yöntemini (FaceID, TouchID veya Parmak İzi) açmanız gerekir", "too_many_attempts": "Çok sayıda deneme yapıldı. Lütfen {{remainingTime}} sonra tekrar deneyin", "apple_button": "Apple ile Giriş <PERSON>", "google_button": "Google ile Giriş <PERSON>", "other_methods": "Farklı bir oturum açma yöntemi kullanın", "forgot_password_desc": "Şifrenizi mi unuttunuz?", "forgot_password_desc_2": "MetaMask sizin için şifrenizi kurtaramaz.", "forgot_password_point_1": "Bir cihazda MetaMask oturumunuzu", "forgot_password_point_1_bold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Face ID gibi) ise", "forgot_password_point_1_1": "şifrenizi orada sıfırlayabilirsiniz.", "forgot_password_point_2": "<PERSON><PERSON><PERSON>arma İfadeniz", "forgot_password_point_2_bold": "<PERSON><PERSON><PERSON>,", "forgot_password_point_2_1": "geçerli cüzdanınızı sıfırlayabilir ve Gizli Kurtarma İfadesi kullanarak yeniden içe aktarabilirsiniz.", "reset_wallet": "Cüzdanı sıfırla", "reset_wallet_desc": "Cüzdan verileriniz", "reset_wallet_desc_bold": "bu cihazda", "reset_wallet_desc_2": "MetaMask'ten kalıcı olarak silinecek. Bu işlem geri alınamaz.", "reset_wallet_desc_login": "Cüzdanınızı geri yüklemek için Gizli Kurtarma İfadenizi veya Google ya da Apple hesap şifrenizi kullanabilirsiniz. MetaMask bu bilgilere sahip değildir.", "reset_wallet_desc_srp": "Cüzdanınızı geri yüklemek için Gizli Kurtarma İfadenizin hazır bulunduğundan emin olun. MetaMask bu bilgiye sahip değildir."}, "connect_hardware": {"title_select_hardware": "Bir donanım cüzdanı bağlayın", "select_hardware": "Kullanmak istediğiniz donanım cüzdanını seçin"}, "enter_password": {"title": "Şifrenizi girin", "desc": "Devam etmek için lütfen şifrenizi girin", "password": "Şifre", "confirm_button": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>"}, "choose_password": {"title": "MetaMask şifresi", "description": "Yalnızca bu cihazda MetaMask'i açar.", "description_social_login": "Tüm cihazlarda cüzdanı kurtarmak için bunu kullanın.", "description_social_login_update": "If you lose this password, you’ll lose access to your wallet on all devices. Store it somewhere safe,", "description_social_login_update_bold": "MetaMask can't reset it.", "subtitle": "Bu ş<PERSON><PERSON> sadece bu cihazda MetaMask cüzdanınızın kilidini açar.", "password": "Create password", "confirm_password": "<PERSON><PERSON><PERSON><PERSON>", "create_button": "<PERSON><PERSON><PERSON>", "import_with_seed_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadesi ile içe aktar", "password_length_error": "Şifre en az 8 karakter uzunluğunda olmalıdır", "password_dont_match": "Şifreler uyumlu değil", "password_strength": "Şifre güvenlik seviyesi:", "strength_weak": "<PERSON><PERSON><PERSON><PERSON>", "strength_good": "İyi", "strength_strong": "Güçlü", "show": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON>", "seed_phrase": "<PERSON><PERSON><PERSON>", "must_be_at_least": "En az {{number}} karakter olmalıdır", "remember_me": "<PERSON><PERSON>", "security_alert_title": "Güvenlik Uyarısı", "security_alert_message": "İlerlemek için Parolayı veya cihazınızda desteklenen herhangi bir biyometri kimlik doğrulama yöntemini (FaceID, TouchID veya Parmak İzi) açmanız gerekir", "i_understand": "MetaMask'ın benim için bu şifreyi kurtaramadığını anlıyorum.", "learn_more": "Daha fazlasını öğren.", "secure": "Cüzdanı güvenli hale getir", "confirm": "<PERSON><PERSON><PERSON>fadesini onayla", "disable_biometric_error": "Uygulama için biyometriyi devre dışı bıraktınız. Beni Hatırla ayarlarını güncelleyip tekrar deneyin.", "create_password_cta": "<PERSON><PERSON><PERSON>", "steps": "Adım {{currentStep}}/{{totalSteps}}", "password_error": "Passwords don’t match.", "marketing_opt_in_description": "Get product updates, tips, and news including by email. We may use your interactions to improve what we share.", "loose_password_description": "If I lose this password, MetaMask can’t reset it."}, "reset_password": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Bu ş<PERSON><PERSON> sadece bu cihazda MetaMask cüzdanınızın kilidini açar.", "password": "<PERSON><PERSON>", "confirm_password": "<PERSON><PERSON><PERSON><PERSON>", "reset_button": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "import_with_seed_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadesi ile içe aktar", "password_length_error": "Şifre en az 8 karakter uzunluğunda olmalıdır", "password_dont_match": "Şifreler uyumlu değil", "password_strength": "Şifre güvenlik seviyesi:", "strength_weak": "<PERSON><PERSON><PERSON><PERSON>", "strength_good": "İyi", "strength_strong": "Güçlü", "show": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON>", "seed_phrase": "<PERSON><PERSON><PERSON>", "must_be_at_least": "En az {{number}} karakter olmalıdır", "remember_me": "<PERSON><PERSON>", "security_alert_title": "Güvenlik Uyarısı", "security_alert_message": "İlerlemek için Parolayı veya cihazınızda desteklenen herhangi bir biyometri kimlik doğrulama yöntemini (FaceID, TouchID veya Parmak İzi) açmanız gerekir", "i_understand": "MetaMask'ın benim için bu şifreyi kurtaramadığını anlıyorum.", "learn_more": "Daha fazlasını öğren.", "secure": "Cüzdanı güvenli hale getir", "confirm": "<PERSON><PERSON><PERSON>fadesini onayla", "password_updated": "<PERSON><PERSON>", "successfully_changed": "Şifreniz başarılı bir şekilde <PERSON>ti<PERSON>", "new_password_placeholder": "En az 8 karakter kullanın", "confirm_password_placeholder": "Şifrenizi tekrar girin", "confirm_btn": "<PERSON><PERSON>", "warning_password_change_title": "Emin misiniz?", "warning_password_change_description": "Buradan şifrenizi değiştirdiğinizde kullandığınız diğer cihazlarda MetaMask kilitlenecek. Yeni şifrenizle tekrar oturum açmanız gerekecek", "warning_password_change_button": "<PERSON><PERSON><PERSON>", "warning_password_cancel_button": "İptal et", "changing_password": "<PERSON><PERSON><PERSON>tiriliyor...", "changing_password_subtitle": "Bu işlem uzun sürmeyecektir", "checkbox_forgot_password": "Bu şifreyi unutursam cüzdanıma erişimi kalıcı olarak kaybedeceğim. MetaMask bunu benim için sı<PERSON>ırlayamaz.", "seedless_change_password_error_modal_title": "Change password failed", "seedless_change_password_error_modal_content": "We were unable to change your password. Please try again.", "seedless_change_password_error_modal_confirm": "<PERSON><PERSON><PERSON> dene"}, "import_from_seed": {"title": "Bir cüzdanı içe aktar", "seed_phrase_placeholder": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi girin", "create_new_password": "Create password", "confirm_password": "<PERSON><PERSON><PERSON><PERSON>", "import_button": "İÇE AKTAR", "cancel_button": "İptal", "password_length_error": "Şifre en az 8 karakter uzunluğunda olmalıdır", "password_dont_match": "Şifreler uyumlu değil", "seed_phrase_requirements": "<PERSON><PERSON><PERSON> Kurtarma İfadeleri 12, 15, 18, 21 veya 24 sözcük içerir", "invalid_seed_phrase": "G<PERSON><PERSON> Kurtarma İfadesi bulunamadı.", "error": "<PERSON><PERSON>", "invalid_qr_code_title": "QR Kodu geçersiz", "invalid_qr_code_message": "Bu QR kodu geçerli bir Gizli Kurtarma İfadesini temsil etmiyor", "enter_your_secret_recovery_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi girin", "metamask_password": "MetaMask şifresi", "metamask_password_description": "Yalnızca bu cihazda MetaMask'i açar.", "seed_phrase_required": "<PERSON><PERSON><PERSON> Kurtarma İfadesi gereklidir", "re_enter_password": "Şifrenizi tekrar girin", "use_at_least_8_characters": "En az 8 karakter kullanın", "unlock_with_face_id": "Face ID ile kilidi a<PERSON>?", "learn_more": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> unutursam MetaMask bunu benim i<PERSON> k<PERSON>.", "learn_more_social_login": "Bu şifreyi unutursam cüzdanıma erişimi kalıcı olarak kaybedeceğim. MetaMask bunu benim için sı<PERSON>ırlayamaz.", "seed_phrase_length_error": "G<PERSON>li Kurtarma İfadesi 12, 15, 18, 21 veya 24 sözcükten oluşmalıdır", "continue": "<PERSON><PERSON> et", "clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "show_all": "Tümünü <PERSON>ö<PERSON>", "paste": "Yapıştır", "hide_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> gizle", "srp": "<PERSON><PERSON><PERSON>", "srp_placeholder": "Her bir s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n arasına bir boş<PERSON> ekleyin ve hiç kimsenin izlemediğinden emin olun.", "learn_more_link": "<PERSON>ha fazla bilgi edin", "import_create_password_cta": "Create password", "pass_flow": "Akış ile devam et", "steps": "Adım {{currentStep}}/{{totalSteps}}", "password_error": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor. <PERSON><PERSON><PERSON>.", "spellcheck_error": "Yalnızca küçük harf kullanın, yazımınızı kontrol edin ve sözcükleri asıl sıralamasında girin.", "enter_strong_password": "Güçlü bir ş<PERSON>re girin"}, "bottom_tab_bar": {"dapps": "Merkeziyetsiz uygulamalar", "wallet": "Cüzdan", "transfer": "Transfer Et"}, "bottom_nav": {"home": "<PERSON>", "browser": "Tarayıcı", "activity": "Aktivite", "trade": "Trade", "settings": "<PERSON><PERSON><PERSON>", "rewards": "<PERSON><PERSON><PERSON><PERSON>"}, "drawer": {"send_button": "<PERSON><PERSON><PERSON>", "receive_button": "Para ekle", "coming_soon": "Çok yakında...", "wallet": "Cüzdan", "transaction_activity": "Faaliyet", "request_feature": "Bir Özellik Talep Et", "submit_feedback_message": "Gönderilecek geri bildirim tür<PERSON><PERSON>.", "submit_bug": "<PERSON><PERSON>", "submit_general_feedback": "<PERSON><PERSON>", "share_address": "<PERSON><PERSON> Adresim<PERSON>", "view_in_etherscan": "Etherscan üzerinde görüntüle", "view_in": "Şurada görüntüle:", "browser": "Tarayıcı", "settings": "<PERSON><PERSON><PERSON>", "settings_warning": "Cüzdan korunmasız", "settings_warning_short": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "help": "Destek", "lock": "<PERSON><PERSON><PERSON>", "lock_title": "Cüzdanınızı gerçekten kilitlemek istiyor musunuz?", "lock_ok": "EVET", "lock_cancel": "HAYIR", "feedback": "<PERSON><PERSON>", "metamask_support": "MetaMask Destek", "public_address": "<PERSON><PERSON>"}, "send": {"available": "kullanılabilir", "invalid_value": "Geç<PERSON><PERSON>", "insufficient_funds": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON>", "unit": "birim", "units": "birim", "next": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "deeplink_failure": "Bir şeyler ters gitti. Lütfen tekrar deneyin", "warn_network_change": "<PERSON><PERSON>ril<PERSON>:", "send_to": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "paste": "Yapıştır", "no_assets_available": "Gönderilebilecek varlık yok", "clear": "<PERSON><PERSON><PERSON>", "to": "Alıcı", "enter_address_to_send_to": "Gönderilecek adresi girin", "accounts": "<PERSON><PERSON><PERSON><PERSON>", "contacts": "<PERSON><PERSON><PERSON>", "no_contacts_found": "<PERSON><PERSON><PERSON> bulunamadı", "review": "<PERSON><PERSON><PERSON>", "all_networks": "Tümü", "no_tokens_match_filters": "Filtrelerinizle eşleşen token yok", "clear_filters": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "no_tokens_available": "Token yok", "sign": "İmzala", "network_not_found_title": "<PERSON><PERSON> bulunamadı", "network_not_found_description": "<PERSON><PERSON><PERSON>r k<PERSON> {{chain_id}} olan ağ, cüzdanınızda bulunamadı. Lütfen önce ağı ekleyin.", "network_missing_id": "Zincir kimliği eksik.", "search_tokens_and_nfts": "Token ve NFT ara", "tokens": "To<PERSON>'lar", "nfts": "NFT'ler", "show_more_nfts": "Daha fazla NFT göster", "token_contract_warning": "Bu adres bir token sözleşmesi adresi. Bu adrese token gönderirseniz bunları kaybedersiniz.", "invisible_character_error": "ENS adında görünmez bir karakter algıladık. Olası bir dolandırıcılığı önlemek için ENS adını kontrol edin.", "could_not_resolve_name": "Couldn't resolve name"}, "deposit": {"title": "Para Yatır", "selectRegion": "<PERSON><PERSON><PERSON>", "chooseYourRegionToContinue": "<PERSON><PERSON> etmek i<PERSON><PERSON> bö<PERSON> seçin", "buildQuote": {"payWith": "Ödeme <PERSON>", "unexpectedError": "Beklenmedik bir hata <PERSON>.", "quoteFetchError": "<PERSON><PERSON><PERSON><PERSON>.", "kycFormsFetchError": "KYC formları alınamadı.", "title": "Para Yatır"}, "token_modal": {"select_a_token": "Bir Token seçin", "select_token": "Token seç", "search_by_name_or_address": "İsme veya adrese göre token ara", "no_tokens_found": "\"{{searchString}}\" ile eşleşen token yok"}, "networks_filter_bar": {"all_networks": "<PERSON><PERSON><PERSON>"}, "networks_filter_selector": {"select_network": "<PERSON><PERSON>", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "deselect_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> kaldır", "apply": "<PERSON><PERSON><PERSON><PERSON>"}, "configuration_modal": {"title": "Seçenekler", "view_order_history": "<PERSON><PERSON> g<PERSON>ç<PERSON>şini gö<PERSON><PERSON>", "contact_support": "Destek ile iletişime geçin", "log_out": "<PERSON><PERSON><PERSON><PERSON> kapat", "logged_out_success": "Oturum başarılı bir şekilde ka<PERSON>ı<PERSON>ı", "error_sdk_not_initialized": "SDK başlatılmadı", "logged_out_error": "<PERSON><PERSON><PERSON> kapatılırken hata"}, "region_modal": {"select_a_region": "<PERSON><PERSON> bö<PERSON> se<PERSON>", "search_by_country": "Ülkeye göre ara", "no_regions_found": "\"{{searchString}}\" ile <PERSON><PERSON><PERSON><PERSON><PERSON> bölge yok"}, "state_modal": {"select_a_state": "Bir eyalet seçin", "search_by_state": "Eyalete göre ara", "no_state_results": "\"{{searchString}}\" ile eşleşen eyalet yok"}, "payment_modal": {"select_a_payment_method": "Bir Ödeme Yöntemi <PERSON>"}, "payment_duration": {"instant": "Anında", "1_to_2_days": "1 ila 2 gün"}, "unsupported_region_modal": {"title": "<PERSON><PERSON><PERSON>", "location_prefix": "Görünüşe göre bulunduğunuz konum:", "description": "Kapsamı bölgenize genişletmek için çok çalışıyoruz. Bu arada kripto edinmenizin başka yolları olabilir.", "change_region": "<PERSON><PERSON><PERSON><PERSON>", "buy_crypto": "Kripto Satın Al"}, "unsupported_state_modal": {"title": "<PERSON><PERSON><PERSON>", "location_prefix": "<PERSON><PERSON><PERSON>iz:", "description": "Kapsamı bölgenize genişletmek için çok çalışıyoruz. Bu arada kripto edinmenizin başka yolları var.", "change_state": "<PERSON><PERSON><PERSON><PERSON>", "try_another_option": "Başka bir seçenek deneyin"}, "incompatible_token_acount_modal": {"title": "Hesabınızı değiştirin", "description": "Bulunduğunuz {{networkName}} hesabı seçili token'ı desteklemiyor. Devam etmek için hesabınızı veya token'ı değiştirin.", "cta": "Anlıyorum"}, "ssn_info_modal": {"title": "<PERSON><PERSON>?", "description": "ABD yasaları uyarınca doları kriptoya çevirmeden önce Transak'in kimliğinizi doğrulaması gerekir. Yani ABD'deki müşterilerin bir Sosyal Güvenlik numarası sunması gerekir."}, "get_started": {"navbar_title": "Para Yatır", "title": "USDC ile başlamak kolaydır", "bullet_1_title": "Değeri Amerikan doları desteklidir", "bullet_1_description": "USDC, Amerikan doları destekli dijital bir dolardır, yani her zaman değerini bilirsiniz.", "bullet_2_title": "Düşük volatilite", "bullet_2_description": "USDC, stabil olmak için ta<PERSON>lanmıştır ve planlamayı, biriktirmeyi ve harcamayı kolay hale getirir.", "bullet_3_title": "Bankalardan da<PERSON> hı<PERSON>ı<PERSON>", "bullet_3_description": "Dakikalar içinde gönderin ve alın; banka saatlerini veya uluslararası gecikmeleri beklemenize gerek yok.", "button": "Başlarken"}, "enter_email": {"navbar_title": "Kimliğinizi doğrulayın", "title": "E-posta adresinizi girin", "description": "Transak ile güvenli bir şekilde oturum açmanız ve hesap oluşturmanız için size e-posta yoluyla altı haneli bir doğrulama kodu göndereceğiz.", "input_placeholder": "<EMAIL>", "submit_button": "E-posta g<PERSON>nder", "loading": "E-posta gönderiliyor...", "validation_error": "Lütfen geçerli bir e-posta adresi girin", "error": "E-posta gönderilirken bir hata o<PERSON>"}, "otp_code": {"navbar_title": "Kimliğinizi doğrulayın", "title": "<PERSON><PERSON> haneli kodu girin", "description": "{{email}} adresinize gönderdiğimiz kodu girin. Kodu görmüyorsanız istenmeyen ileti klasörünüzü kontrol edin.", "submit_button": "<PERSON><PERSON><PERSON>", "loading": "Kod doğrulanıyor...", "invalid_code_error": "Kod geç<PERSON>iz", "validation_error": "Lütfen geçerli bir kod girin", "need_help": "Yardıma mı ihtiyacınız var?", "resend_code_description": "Kodu almadınız mı?", "resend_code_error": "Kod tekrar gönderili<PERSON>en hata.", "resend_code_button": "<PERSON><PERSON><PERSON>", "resend_cooldown": "Kodu {{seconds}} saniye içinde tekrar gönder", "contact_support": "Destek ile iletişime geçin", "error": "Kod doğrulanırken bir hata oluştu"}, "verify_identity": {"title": "Kimliğinizi doğrulayın", "navbar_title": "Para Yatır", "description_1": "İlk kez para yatırmak için kimliğiniz doğrulanmalıdır.", "description_2_transak": "Transak", "description_2_rest": " para yatırma işleminizi kolaylaştıracak ve verileriniz doğrudan Transak'e gönderilecektir. Paylaştığınız verileri işlemeyiz.", "description_3_part1": "Daha fazla bilgi almak için ", "description_3_privacy_policy": "gizlilik politikası", "description_3_part2": " daha fazla bilgi edinin.", "agreement_text_part1": "Aşağıdaki düğmeye tıklayarak ", "agreement_text_transak_terms": "Transak'in Kullanım Şartları", "agreement_text_and": " ve ", "agreement_text_privacy_policy": "Gizlilik Politikası", "agreement_text_part2": "bölümlerini kabul edersiniz.", "button": "Kabul et ve devam et"}, "additional_verification": {"title": "Ek Doğrulama", "paragraph_1": "Daha büyük para yatırma işlemleri için geçerli bir kimlik belgesine (sürücü belgesi gibi) ve gerçek zamanlı bir selfie'ye ihtiyacınız olacak.", "paragraph_2": "Doğrulamanızı tamamlamak için kameranıza erişimi etkinleştirmeniz gerekecek.", "button": "<PERSON><PERSON> et"}, "basic_info": {"navbar_title": "Kimliğinizi doğrulayın", "title": "Temel bilgilerinizi girin", "continue": "<PERSON><PERSON> et", "subtitle": "Şimdi sizden bazı temel bilgileri isteyeceğiz.", "first_name": "Ad<PERSON>", "last_name": "Soyadı", "phone_number": "Telefon numarası", "date_of_birth": "<PERSON><PERSON><PERSON> tarihi", "social_security_number": "Sosyal güvenlik numarası (SSN)", "enter_phone_number": "Telefon numarası girin", "select_region": "<PERSON><PERSON><PERSON>", "first_name_required": "Ad gereklidir", "first_name_invalid": "Lütfen geçerli bir ad girin", "last_name_required": "Soyadı gereklidir", "last_name_invalid": "Lütfen geçerli bir soyadı girin", "mobile_number_required": "Telefon numarası gereklidir", "mobile_number_invalid": "Lütfen geçerli bir telefon numarası girin", "dob_required": "<PERSON><PERSON><PERSON> tari<PERSON> g<PERSON>", "dob_invalid": "Lütfen geçerli bir doğum tarihi girin", "ssn_required": "Sosyal güvenlik numarası gereklidir", "unexpected_error": "Beklenmedik bir hata oluştu. Lütfen tekrar deneyin."}, "enter_address": {"navbar_title": "Kimliğinizi doğrulayın", "title": "Adresini<PERSON> girin", "subtitle": "En güncel daimi adresinizi kullanın.", "continue": "<PERSON><PERSON> et", "address_line_1": "Adres satırı 1", "address_line_2": "Adres satırı 2 (is<PERSON>ğe bağlı)", "state": "Eyalet/Bölge", "city": "Şehir", "postal_code": "Posta Kodu", "country": "<PERSON><PERSON><PERSON>", "select_state": "Eyalet seçin", "address_line_1_required": "Adres satırı 1 gereklidir", "address_line_1_invalid": "Lütfen geçerli bir adres girin", "address_line_2_invalid": "Lütfen geçerli bir adres girin", "city_required": "<PERSON><PERSON>ir g<PERSON>lid<PERSON>", "city_invalid": "Lütfen geçerli bir şehir girin", "state_required": "Eyalet/Bölge g<PERSON>ir", "state_invalid": "Lütfen geçerli bir eyalet girin", "postal_code_required": "Posta Kodu/Zip <PERSON>du g<PERSON>klidir", "postal_code_invalid": "Lütfen geçerli bir posta kodu girin", "unexpected_error": "Beklenmedik hata."}, "privacy_section": {"transak": "Transak bu bilgileri şifreler ve saklar.", "metamask": "MetaMask verilerinizi asla almaz veya kullanmaz."}, "kyc_processing": {"navbar_title": "Kimliğinizi doğrulayın", "heading": "<PERSON>ık<PERSON> durun...", "description": "Bu işlem yalnızca yaklaşık 2 dakika sürecektir.", "error_heading": "Kimliğinizi doğrulayamadık.", "error_description": "Belgelerinizi tekrar yüklemeyi ve doğrulama için tekrar göndermeyi deneyin.", "error_button": "Tekrar doğrulamayı deneyin", "success_heading": "Doğrulandınız", "success_description": "Şimdi para yatırma işleminizi tamamlayabilirsiniz.", "success_button": "<PERSON><PERSON><PERSON>"}, "provider_webview": {"title": "Para Yatır"}, "kyc_webview": {"title": "Kimliğiniz doğrulanıyor", "webview_received_error": "WebView şu hata durum kodunu aldı: {{code}}"}, "order_processing": {"title": "Para yatırma işleniyor", "button": "Ka<PERSON><PERSON>", "description": "Kart ile satın alma işlemleri genellikle birkaç dakika sürer", "bank_transfer_description": "Banka transferleri genellikle birkaç iş günü sürer", "success_title": "Para yatırma işlemi tamamlandı", "success_description": "{{amount}} {{currency}} para yatırma işleminiz başarılı oldu!", "error_title": "Para yatırma işlemi başarısız oldu", "error_description": "Para yatırma işleminizi tamamlayamadık", "cancel_order_description": "Para yatırma işleminizin iptalini talep ettiniz.", "error_button": "<PERSON><PERSON><PERSON> dene", "contact_support_button": "Destek ile iletişime geçin", "account": "<PERSON><PERSON><PERSON>", "network": "Ağ", "order_id": "<PERSON><PERSON>", "fees": "<PERSON><PERSON><PERSON>", "total": "Toplam", "view_order_details_in_transak": "Emir bilgilerini Transak'ta gö<PERSON><PERSON><PERSON><PERSON>", "no_order_found": "<PERSON><PERSON>", "back_to_wallet": "Cüzdana Geri Dön", "cancel_order_button": "<PERSON><PERSON> et"}, "order_details": {"title": "Para Yatırma <PERSON>", "error_title": "Para yatırma emrinizle ilgili bir hata oluş<PERSON>", "error_message": "Beklenmedik bir hata <PERSON>."}, "bank_details": {"navbar_title": "{{paymentMethod}} banka transferi", "button": "<PERSON><PERSON> on<PERSON>la", "button_cancel": "<PERSON><PERSON> et", "main_title": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON><PERSON>", "main_content_1": "Aşağıdaki bilgileri kullanarak tercih ettiğiniz bankadan bir transfer başlatın.", "main_content_2": "Bittiğinde geri gelin ve onaylamak için “Transferi onayla” seçeneğine tıklayın.", "show_bank_info": "Banka bilgilerini göster", "hide_bank_info": "Banka bilgilerini gizle", "transfer_amount": "Transfer miktarı", "account_holder_name": "<PERSON><PERSON><PERSON> adı", "beneficiary_name": "<PERSON><PERSON><PERSON> adı", "routing_number": "Yönlendirme numarası", "account_number": "<PERSON><PERSON><PERSON>", "iban": "IBAN", "bic": "BIC", "account_type": "<PERSON><PERSON><PERSON>", "bank_name": "Banka adı", "beneficiary_address": "<PERSON><PERSON><PERSON>", "bank_address": "Banka adresi", "info_banner_text": "<PERSON><PERSON>p sahibi o<PERSON>ak <PERSON>unu görmelisiniz: '{{accountHolderName}}'. Bu, bankanızın transferi gerçekleştirmesini sağlar.", "error_message": "Banka transferinizi onaylayamadık. Lütfen tekrar deneyin.", "cancel_order_error": "Emrinizi iptal edemedik. Lütfen tekrar deneyin."}, "webview_modal": {"error": "Webview hata aldı: %{code}"}, "notifications": {"deposit_failed_title": "{{currency}} para yatırma işlemi başarısız oldu! Lütfen tekrar deneyin, verdiğimiz rahatsızlıktan dolayı özür dileriz!", "deposit_failed_description": "Ödeme yönteminizi ve kart desteğini doğrulayın", "deposit_cancelled_title": "Para yatırma işleminiz iptal edildi", "deposit_cancelled_description": "Para yatırma işleminizin iptalini talep ettiniz.", "deposit_completed_title": "{{amount}} {{currency}} para yatırma işleminiz başarılı oldu!", "deposit_completed_description": "{{currency}} şu anda cüzdanınızda mevcut", "deposit_pending_title": "{{currency}} para yatırma işleminiz gerçekleşiyor", "deposit_pending_description": "Bu işlem sadece birkaç dakika sürecektir..."}, "error_view": {"title": "<PERSON>ir hata o<PERSON>", "description": "Para yatırma işleminiz gerçekleştirilirken bir hata oluştu. Sorun devam ederse lütfen destek bölümüyle iletişime geçin.", "try_again": "<PERSON><PERSON><PERSON> dene"}}, "perps": {"title": "Sürekli Vadeli İşlem Sözleşmeleri", "perps_trading": "Sürekli Vadeli İşlem Sözleşmeleri ile İşlem", "perp_account_balance": "Sürekli vadeli işlem sözleşmesi hesap bakiyesi", "manage_balance": "Bakiyeyi Yönet", "total_balance": "Toplam Bakiye", "available_balance": "Kullanılabil<PERSON> b<PERSON>", "margin_used": "Kullanılan Marj", "gtm_content": {"title": "PERPS ARE HERE", "title_description": "Long or short tokens with up to 40x leverage. Fund your account with any EVM token in one click.", "not_now": "<PERSON><PERSON><PERSON>", "try_now": "Get started"}, "unrealized_pnl": "Gerçekleşmemiş Kazanç/Zarar", "withdraw": "Çek", "refresh_balance": "Bakiyeyi Yenile", "add_funds": "Para ekle", "your_positions": "Pozisyonlarınız", "loading_positions": "Pozisyonlar yükleniyor...", "refreshing_positions": "Pozisyonlar yenileniyor...", "no_open_orders": "Açık emir yok", "deposit": {"title": "Yatırılacak miktar", "get_usdc_hyperliquid": "USDC • Hyperliquid al", "insufficient_funds": "Para yetersiz", "enter_amount": "Tutar gir", "fetching_quote": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "İşlem gönderiliyor", "get_usdc": "USDC al", "network_fee": "<PERSON><PERSON>", "estimated_time": "<PERSON><PERSON><PERSON>", "rate": "<PERSON><PERSON>", "slippage": "<PERSON><PERSON>", "slippage_info": "Emrinizin verildiği zaman ile onaylandığı zaman arasında fiyat değişirse buna “kayma” denir. Kay<PERSON> burada belirlediğiniz toleransı aşarsa işleminiz otomatik olarak iptal edilir.", "slippage_auto": "Otomatik", "apply": "<PERSON><PERSON><PERSON><PERSON>", "max_button": "<PERSON><PERSON><PERSON><PERSON>", "done_button": "<PERSON><PERSON>", "metamask_fee": "MetaMask ücreti", "metamask_fee_tooltip": "<PERSON><PERSON><PERSON><PERSON>, Hyperliquid'e yapılan para yatırma işlemlerinden herhangi bir ücret almaz", "minimum_deposit_error": "Yatırılması gereken minimum miktar: {{amount}} USDC", "processing_title": "Para yatırma işleniyor", "preview": {"title": "Para Yatırma İşlemi Ön İzlemesi"}, "processing": {"title": "Para Yatırma İşlemi Gerçekleştiriliyor"}, "deposit_completed": "Para yatırma işlemi başarılı bir şekilde ta<PERSON>landı!", "deposit_failed": "Para Yatırma İşlemi Başarısız Oldu", "retry_deposit": "Para Yatırma İşlemini Tekrar Dene", "go_back": "<PERSON><PERSON>", "view_balance": "Bakiyeyi Görüntüle", "calculating_fee": "Hesaplanıyor...", "quote_expired_modal": {"title": "<PERSON><PERSON><PERSON>in Süresi Doldu", "description": "Para yatırma işleminiz için yapılan fiyat teklifinin {{refreshRate}} saniye sonra süresi doldu. Devam etmek için yeni bir teklif alın.", "get_new_quote": "<PERSON><PERSON>"}, "steps": {"preparing": "Para yatırma işlemi hazırlanıyor...", "swapping": "{{token}} - USDC takas işlemi yapılıyor", "bridging": "Hyperliquid ile Köprüleniyor", "depositing": "Sürekli vadeli işlem sözleşmeleri hesabına para yatırılıyor", "depositing_direct": "Doğrudan HyperLiquid hesabınıza USDC transfer ediliyor..."}, "step_descriptions": {"preparing": "Para yatırma işleminiz hazırlanıyor...", "swapping": "Para yatırma işlemi için token'larınız USDC'ye dönüştürülüyor...", "bridging": "USDC, Arbitrum ağına taşınıyor...", "depositing": "HyperLiquid hesabınıza USDC transfer ediliyor...", "success": "HyperLiquid hesabınıza başarılı bir şekilde {{amount}} USDC yatırıldı", "error": "Para yatırma işlemi sırasında bir şeyler ters gitti. Lütfen tekrar deneyin."}, "quote_fetch_error": "Para yatırma fiyat teklifi alınamadı. Lütfen tekrar deneyin.", "bridge_quote_timeout": "Köprü teklifi alınamadı. Lütfen tekrar deneyin veya farklı bir token seçin.", "no_quotes_available": "<PERSON>u takas için rota mevcut değil. Farklı bir token veya miktar deneyin.", "success": {"title": "Para Yatırma İşlemi Başarılı!", "description": "USDC'niz başarılı bir şekilde HyperLiquid işlem hesabınıza yatırıldı", "amount": "<PERSON><PERSON>", "processing_time": "İşlem Süresi", "status": "Durum", "completed": "Tamamlandı", "view_balance": "Bakiyeyi Görüntüle", "view_transaction": "İşlemi Görüntüle"}, "success_toast": "Your Perps account was funded", "success_message": "{{amount}} available to trade", "funds_are_ready_to_trade": "Funds are ready to trade", "error_toast": "Transaction failed", "error_generic": "Funds have been returned to you", "in_progress": "Adding funds to Perps", "estimated_processing_time": "Est. {{time}}", "funds_available_momentarily": "Funds will be available momentarily", "your_funds_are_available_to_trade": "Your funds are available to trade", "track": "Track"}, "withdrawal": {"title": "Çek", "insufficient_funds": "Para yetersiz", "enter_amount": "Tutar gir", "review": "Para çekme işlemini incele", "withdraw": "Çek", "withdraw_usdc": "USDC çek", "minimum_amount_error": "Çekilebilir minimum tutar: {{amount}}$", "amount_too_low": "Ücretlerin karşılanabilmesi için tutar {{minAmount}}$ üzerinde olmalıdır", "confirm": "Para çekme işlemini onayla", "processing_title": "Para çekme işlemi başlatıldı", "eta_will_be_shared_shortly": "ETA will be shared shortly", "success_title": "Para çekme işlemi başarılı", "success_description": "{{amount}} USDC Hyperliquid'den çekildi", "network_fee": "<PERSON><PERSON>", "metamask_fee": "MetaMask ücreti", "total_fees": "Toplam ücretler", "receiving_amount": "Elinize geçecek miktar:", "estimated_time": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "initiated": "Para çekme işlemi başlatıldı", "wait_time_message": "Paranız 5 dakika içinde ulaşacaktır", "submitting": "Gönderiliyor...", "error": "Para çekme işlemi başarısız", "success_toast": "<PERSON><PERSON><PERSON> confirmed", "success_toast_description": "You'll receive {{amount}} {{symbol}} on {{networkName}} within 5 minutes", "bridge_info": "HyperLiquid doğrulayıcıları para çekme işleminizi gerçekleştiriyor", "error_generic": "Para çekme işlemi sırasında bir hata oluştu", "invalid_amount": "Lütfen geçerli bir tutar girin", "max": "<PERSON><PERSON><PERSON><PERSON>", "percentage_10": "%10", "percentage_25": "%25", "available_balance": "Kullanılabilir sürekli vadeli işlem sözleşmeleri bakiyesi: {{amount}}", "receive": "Al", "provider_fee": "Sağlayıcı ücreti", "you_will_receive": "Elinize geçecek miktar:", "continue": "<PERSON><PERSON>", "funds_received": "{{amount}} {{symbol}} alındı"}, "quote": {"network_fee": "<PERSON><PERSON>", "estimated_time": "<PERSON><PERSON><PERSON>", "rate": "<PERSON><PERSON>", "metamask_fee": "MetaMask ücreti", "metamask_fee_tooltip": "<PERSON><PERSON><PERSON><PERSON>, Hyperliquid işlemleri için herhangi bir ücret almaz", "metamask_fee_tooltip_deposit": "<PERSON><PERSON><PERSON><PERSON>, Hyperliquid'e yapılan para yatırma işlemlerinden herhangi bir ücret almaz", "metamask_fee_tooltip_withdrawal": "<PERSON><PERSON><PERSON><PERSON>, Hyperliquid'den yapılan para çekme işlemlerinden herhangi bir ücret almaz"}, "order": {"title": "<PERSON><PERSON>", "leverage": "Kaldıraç", "limit_price": "Limit fiyatı", "enter_price": "Fiyatı gir", "trigger_price": "Tetikleme fiyatı", "liquidation_price": "Likidasyon fiyatı", "fees": "<PERSON><PERSON><PERSON>", "market": "<PERSON><PERSON><PERSON>", "limit": "Limit", "open_orders": "<PERSON><PERSON><PERSON>", "max": "maks.", "cancel_order": "<PERSON><PERSON> et", "filled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reduce_only": "Yalnızca azalt", "yes": "<PERSON><PERSON>", "status": {"open": "Açık", "filled": "Gerçek<PERSON>ş<PERSON>", "canceled": "İptal Edildi", "rejected": "Reddedildi"}, "validation": {"failed": "Em<PERSON>ğ<PERSON> başarısız oldu", "amount_required": "Emir tutarı 0'dan büyük olmalıdır", "minimum_amount": "Minimum emir b<PERSON><PERSON><PERSON><PERSON> {{amount}}$", "insufficient_funds": "Insufficient funds", "insufficient_balance": "Bakiye yetersiz. Gereken: {{required}}$, Kullanılabilir: {{available}}$", "invalid_leverage": "Kald<PERSON>raç {{min}} katı ile {{max}} katı arasında olmalıdır", "high_leverage_warning": "Yüksek kaldıraç likidasyon riskini artırır", "invalid_take_profit": "<PERSON><PERSON><PERSON> al, {{positionType}} pozisyonlar için {{direction}} güncel fiyat olmalıdır", "invalid_stop_loss": "<PERSON><PERSON><PERSON> dur, {{positionType}} pozisyonlar için {{direction}} gü<PERSON><PERSON> fiyat olmalıdır", "liquidation_warning": "Pozisyon likidasyon fiyatına yakın", "limit_price_required": "Limit emirleri için lütfen bir limit fiyatı belirleyin", "please_set_a_limit_price": "Please set a limit price", "limit_price_must_be_set_before_configuring_tpsl": "Limit price must be set before configuring TP/SL", "only_hyperliquid_usdc": "HyperLiquid’de ödeme için şu anda yalnızca USDC desteklenmektedir", "limit_price_far_warning": "Limit price is far from current market price"}, "error": {"placement_failed": "Emir verme işlemi başarısız oldu", "network_error": "<PERSON><PERSON>ı", "unknown": "Bilinmeyen bir hata o<PERSON>", "dismiss": "<PERSON><PERSON> say", "invalid_asset": "Geçersiz varlık", "go_back": "<PERSON><PERSON>", "asset_not_tradable": "{{asset}} işlem yapılabilir bir varlık değildir"}, "off": "<PERSON><PERSON><PERSON>", "estimated_execution_time": "<PERSON><PERSON><PERSON> g<PERSON>şme zamanı", "one_to_three_seconds": "1 ila 3 saniye", "margin": "<PERSON><PERSON>", "take_profit": "Kazancı al", "stop_loss": "<PERSON><PERSON><PERSON> du<PERSON>", "tp_sl": "KA/ZD", "button": {"long": "Uzun {{asset}}", "short": "Kısa {{asset}}"}, "tpsl_modal": {"title": "Kazancı al ve Zararda durdur", "save": "<PERSON><PERSON>", "current_price": "Mevcut fiyat: {{price}}", "on": "AÇIK", "off": "KAPALI", "take_profit_helper": "Fiyat bu seviyeye ulaştığında pozisyonu kapat", "stop_loss_helper": "Pozisyonu bu fiyatta kapatarak zararı sınırla"}, "limit_price_modal": {"title": "Limit fiyatı belirle", "set": "<PERSON><PERSON><PERSON>", "market_price": "Piyasa fiyatı: {{price}}", "market": "<PERSON><PERSON><PERSON>", "current_price": "Mev<PERSON> fiyat", "ask_price": "Satış fiyatı", "bid_price": "Alış fiyatı", "difference_from_market": "Piyasadan farkı:", "limit_price_above": "Limit price is above current price", "limit_price_below": "Limit price is below current price"}, "leverage_modal": {"title": "Kaldıraç", "confirm": "<PERSON><PERSON><PERSON>", "entry_price": "<PERSON><PERSON><PERSON>ı", "current_price": "Mev<PERSON> fiyat", "liquidation_price": "Likidasyon Fiyatı", "liquidation_distance": "Likidasyon Mesafesi"}, "type": {"title": "<PERSON><PERSON>", "market": {"title": "<PERSON><PERSON><PERSON>", "description": "Mevcut piyasa fiyatında derhal gerçekleştir"}, "limit": {"title": "Limit", "description": "Yalnızca belirlediğiniz fiyatta veya daha iyi fiyatta gerçekleştir"}}, "success": {"title": "Emir Başarılı Bir Şekilde Verildi", "subtitle": "{{asset}} için {{direction}} pozisyonunuz oluşturuldu", "asset": "Varlık", "direction": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "orderId": "<PERSON><PERSON>", "viewPositions": "Pozisyonları Görüntüle", "placeAnother": "Başka Bir Emir Ver", "backToPerps": "Sürekli Vadeli İşlem Sözleşmelerine Geri Dön"}, "submitted": "<PERSON><PERSON>", "confirmed": "<PERSON><PERSON>", "cancelling_order": "Cancelling order", "cancelling_order_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_cancelled": "<PERSON><PERSON> i<PERSON> edil<PERSON>", "failed_to_cancel_order": "Failed to cancel order", "funds_have_been_returned_to_you": "Funds have been returned to you", "funds_are_available_to_trade": "Funds are available to trade", "close_order_still_active": "Close order still active", "order_submitted": "Order submitted", "order_filled": "Order filled", "order_placed": "Order placed", "order_placement_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "order_failed": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oldu", "your_funds_have_been_returned_to_you": "Your funds have been returned to you"}, "close_position": {"title": "Pozisyonu kapat", "button": "Pozisyonu kapat", "closing": "Pozisyon kapatılıyor...", "position_close_order_placed": "Placed order to close position", "partially_closing_position": "Partially closing position", "partial_close_submitted": "Partial close submitted", "position_partially_closed": "Position partially closed", "closing_position": "<PERSON><PERSON><PERSON>on ka<PERSON>tılıyor", "limit_close_order_cancelled": "Limit close order cancelled", "closing_position_subtitle": "{{direction}} {{amount}} {{assetSymbol}}", "your_funds_will_be_available_momentarily": "Your funds will be available momentarily", "cancel": "İptal", "margin": "<PERSON><PERSON>", "includes_pnl": "includes P&L", "pnl": "Kazanç/Zarar", "estimated_pnl": "<PERSON><PERSON><PERSON>/Zarar", "fees": "<PERSON><PERSON><PERSON>", "receive": "Elinize geçecek miktar:", "you_receive": "Elinize geçecek miktar:", "select_amount": "Kapatmak için miktar seç", "error_unknown": "Pozisyon kapatılamadı", "success_title": "Pozisyon Başarılı Bir Şekilde <PERSON>ıldı", "position_closed": "Position closed", "funds_are_available_to_trade": "Funds are available to trade", "error_title": "Pozisyon Kapatma İşlemi Başarısız Oldu", "fox_points_earned": "{{points}} Fox <PERSON> kazanıldı!", "minimum_remaining_warning": "<PERSON>lan pozisyon en az {{minimum}}$ olmalıdır. Mevcut: {{remaining}}$", "minimum_remaining_error": "Kısmen kapatılamıyor: kalan pozisyon ({{remaining}}$), minimum emir b<PERSON><PERSON><PERSON> ({{minimum}}$) altında olacaktır. Lütfen bunun yerine %100 kapatın.", "must_close_full_below_minimum": "Pozisyon değeri 10$ altında. Pozisyonun %100'ünü kapatmalısınız.", "negative_receive_amount": "Ücretler, pozisyonunuzun değerini aşıyor", "no_amount_selected": "Lütfen kapatmak için bir miktar seçin", "order_type_reverted_to_market_order": "Changed to market order", "you_need_set_price_limit_order": "You need to set a price for a limit order."}, "tpsl": {"title": "Kazancı al ve zararda durdur", "description": "Bir kazanç veya kayıp yüzdesi ya da pozisyonunuzun otomatik olarak kapanacağı özel bir tetikleme fiyatı girin.", "set": "<PERSON><PERSON><PERSON>", "updating": "Güncelleniyor...", "off": "<PERSON><PERSON><PERSON>", "current_price": "Mev<PERSON> fiyat", "leverage": "Kaldıraç", "margin": "<PERSON><PERSON>", "liquidation_price": "Likidasyon fiyatı", "take_profit_long": "Kazancı al", "take_profit_short": "Kazancı al", "stop_loss_long": "<PERSON><PERSON><PERSON> du<PERSON>", "stop_loss_short": "<PERSON><PERSON><PERSON> du<PERSON>", "trigger_price_placeholder": "Tetikleme fiyatı", "profit_percent_placeholder": "Kazanç", "loss_percent_placeholder": "Zara<PERSON>", "profit_roe_placeholder": "% Kazanç", "loss_roe_placeholder": "% Zarar", "usd_label": "($)", "take_profit_invalid_price": "Take profit must be {{direction}} {{priceType}} price", "stop_loss_invalid_price": "Stop loss must be {{direction}} {{priceType}} price", "stop_loss_beyond_liquidation_error": "Stop loss must be {{direction}} liquidation price", "stop_loss_order_view_warning": "Stop loss is {{direction}} liquidation price", "above": "above", "below": "below"}, "token_selector": {"no_tokens": "Token yok"}, "errors": {"minimumDeposit": "Yatırılması gereken minimum miktar: {{amount}} USDC", "tokenNotSupported": "Para yatırma işlemleri için {{token}} token'ı desteklenmiyor", "unknownError": "Bilinmeyen bir hata o<PERSON>", "clientNotInitialized": "HyperLiquid SDK istemcileri düzgün şekilde başlatılmadı", "exchangeClientNotAvailable": "ExchangeClient başlatıldıktan sonra kullanılamıyor", "infoClientNotAvailable": "InfoClient başlatıldıktan sonra kullanılamıyor", "subscriptionClientNotInitialized": "SubscriptionClient başlatılmadı", "failedToSubscribePosition": "Pozisyon güncellemelerine abone olunamadı", "failedToUnsubscribePosition": "Pozisyon güncellemeleri aboneliği sonlandırılamadı", "failedToSubscribeOrderFill": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON> güncellemelerine abone olunamadı", "failedToUnsubscribeOrderFill": "<PERSON><PERSON> ger<PERSON>ek<PERSON>şme güncellemeleri aboneliği sonlandırılamadı", "failedToEstablishAllMids": "Kürsel allMids aboneliği başarısız oldu", "failedToEstablishMarketData": "{{symbol}} i<PERSON><PERSON> piyasa verileri aboneliği kurulamadı", "failed_to_toggle_network": "<PERSON><PERSON>rilemedi", "noAccountSelected": "<PERSON><PERSON><PERSON>", "unsupportedMethod": "Desteklenmeyen yöntem: {{method}}", "invalidAddressFormat": "Geçersiz adres biçimi: {{address}}", "depositValidation": {"assetIdRequired": "Para yatırma işlemi doğrulaması için AssetId gereklidir", "amountRequired": "Miktar gereklidir ve 0'dan büyük olmalıdır", "amountPositive": "<PERSON><PERSON><PERSON> poziti<PERSON> sayı olmalıdır"}, "withdrawValidation": {"missingParams": "Para çekme işlemi için gerekli parametreler eksik", "assetIdRequired": "para çekme işlemleri için assetId gereklidir", "amountRequired": "para çekme işlemleri için miktar gereklidir", "amountPositive": "<PERSON><PERSON><PERSON> pozitif bir sayı olmalıdır", "invalidDestination": "Geçersiz hedef adres biçimi: {{address}}", "insufficientBalance": "<PERSON><PERSON><PERSON>. Kullanılabilir: {{available}}, Talep edilen: {{requested}}", "assetNotSupported": "{{assetId}} varlığı para çekme işlemleri için desteklenmiyor. Desteklenen varlıklar: {{supportedAssets}}"}, "orderValidation": {"coinRequired": "Emirler için coin gereklidir", "sizePositive": "<PERSON>ut pozitif bir sayı olmalıdır", "pricePositive": "<PERSON><PERSON><PERSON> fiyat pozitif bir sayı olmalıdır", "unknownCoin": "Bilinmeyen coin: {{coin}}"}, "networkToggleFailed": "<PERSON><PERSON>tirilemedi: {{error}}", "accountBalanceFailed": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>: {{error}}", "positionsFailed": "Pozisyonlar alınamadı", "accountStateFailed": "<PERSON><PERSON><PERSON> durumu al<PERSON>", "marketsFailed": "Piyasalar alınamadı", "bridgeContractNotFound": "HyperLiquid köprü sözleşme adresi alınamadı", "providerNotAvailable": "Sağlayıcı {{providerId}} mevcut değil", "withdrawFailed": "Çekilemedi", "connectionRequired": "usePrepsConnection bir PrepsConnectionProvider içinde kullanılmalıdır", "assetMappingFailed": "Varlık eşleştirmesi oluşturulamadı", "depositFailed": "Para yatırma işlemi başarısız oldu", "orderLeverageReductionFailed": "You cannot reduce your leverage", "connectionFailed": {"title": "Sürekli vadeli geçici olarak çevrimdışı", "description": "En kısa sürede çevrimiçi olması için çalışıyoruz.", "retry": "<PERSON><PERSON><PERSON>", "go_back": "<PERSON><PERSON>"}, "networkError": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> bağlanırken bir sorun oluştu. Lütfen tekrar deneyin.", "retry": "<PERSON><PERSON><PERSON>"}, "unknown": {"title": "Bir <PERSON>ler Ters Gitti", "description": "Beklenmedik bir hata o<PERSON>ş<PERSON>. Lütfen daha sonra tekrar deneyin.", "retry": "<PERSON><PERSON><PERSON>"}}, "position": {"title": "Pozisyonlar", "card": {"entry_price": "<PERSON><PERSON><PERSON> fi<PERSON>tı", "funding_cost": "Fonlama", "liquidation_price": "Lik. Fiyatı", "take_profit": "Kazancı al", "stop_loss": "<PERSON><PERSON><PERSON> du<PERSON>", "margin": "<PERSON><PERSON>", "not_set": "Ayarlanmadı", "edit_tpsl": "KA/ZD düzenle", "close_position": "Pozisyonu kapat", "tpsl_count_multiple": "{{count}} orders", "tpsl_count_single": "{{count}} order"}, "list": {"loading": "Pozisyonlar yükleniyor...", "error_title": "Pozisyonlar Yüklenirken Hata", "empty_title": "Açık Pozisyon Yok", "empty_description": "Henüz açık pozisyonunuz yok.\nPozisyonlarınızı burada görmek için işlem yapmaya başlayın.", "first_time_title": "Sürekli Vadeli İşlem Sözleşmeleri", "first_time_description": "40 kata varan kaldıraçla fiyat hareketlerini tahmin edin.", "start_trading": "İşlem yapmaya başlayın", "start_new_trade": "<PERSON>ni bir iş<PERSON> ba<PERSON>", "open_positions": "Açık Pozi<PERSON>lar", "position_count": "{{count}} pozisyon", "position_count_plural": "{{count}} pozisyon"}, "details": {"error_message": "Pozisyon verileri bulunamadı. Lütfen geri dönüp tekrar deneyin.", "section_title": "Pozisyon"}, "account": {"summary_title": "<PERSON><PERSON><PERSON>", "total_balance": "Toplam Bakiye", "available_balance": "Kullanılabilir Bakiye", "margin_used": "Kullanılan Marj", "total_unrealized_pnl": "Toplam Gerçekleşmemiş K ve Z", "unrealized_pnl": "Gerçekleşmemiş K&Z"}, "tpsl": {"update_success": "KA/ZD başarılı bir şekilde güncellendi", "update_failed": "Failed to update TP/SL"}}, "markets": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "market": {"details": {"title": "<PERSON><PERSON><PERSON>", "error_message": "Piyasa verileri bulunamadı. Lütfen geri dönüp tekrar deneyin."}, "statistics": "Overview", "24hr_high": "Son 24 saatin en yüksek <PERSON>ğeri", "24hr_low": "Son 24 saatin en düşük değeri", "24h_volume": "Son 24 saatin hacmi", "open_interest": "Açık faiz", "funding_rate": "Fonlama oranı", "countdown": "<PERSON><PERSON>", "long": "Uzun", "short": "K<PERSON>sa", "add_funds": "Add funds", "add_funds_to_start_trading_perps": "Sürekli vadeli işlem sözleşmeleri ile işlem yapmaya başlamak için fon ekleyin", "position": "Pozisyon", "orders": "<PERSON><PERSON><PERSON>"}, "buttons": {"get_account_balance": "<PERSON>sa<PERSON>", "deposit_funds": "Fon <PERSON>", "switch_to_mainnet": "Ana Ağa Geç", "switch_to_testnet": "Test Ağına Geç", "view_markets": "Piyasaları Görüntüle", "positions": "Pozisyonlar"}, "tooltips": {"leverage": {"title": "Kaldıraç", "content": "Kaldıraç sayesinde yatırdığınızdan daha fazlasıyla işlem yapabilirsiniz. Kazancınızı artırabileceği gibi zararınızı da artırabilir. Kaldıraç ne kadar yüksek olursa işlem o kadar riskli olur."}, "liquidation_price": {"title": "Likidasyon fiyatı", "content": "Fiyat bu seviyeye ulaşırsa pozisyonunuz likidasyona uğrar ve marjınızı kaybedersiniz. Daha yüksek kaldıraç bunu daha olası hale getirir."}, "margin": {"title": "<PERSON><PERSON>", "content": "<PERSON><PERSON>, bir işlemi açmak için yatırdığınız paradır. Teminat görevi görür ve o işlemde kaybedebileceğiniz en yüksek tutardır."}, "fees": {"title": "<PERSON><PERSON><PERSON>", "content": "Bir pozisyon açtığınızda veya kapattığınızda işlem ücretleri tahsil edilir.", "metamask_fee": "MetaMask ücreti", "provider_fee": "Sağlayıcı ücreti", "total": "Toplam ücretler"}, "closing_fees": {"title": "Kapatma <PERSON>", "metamask_fee": "MetaMask ücreti", "provider_fee": "Sağlayıcı ücreti"}, "estimated_pnl": {"title": "Tahmini K&Z", "content": "Bu pozisyonu mevcut piyasa fiyatında kapattığınızda tahmini kazancınız veya zararınız. Bu fiyat, giriş fiyatınıza göre hesaplanır ve pozisyonun kapatmakta olduğunuz kısmını içerir."}, "limit_price": {"title": "<PERSON>it <PERSON>", "content": "Limit emrinizin gerçekleşeceği belirli fiyat. Uzun pozisyonları kapatmak için fiyat bu seviyeye yükseldiğinde emir gerçekleşir. Kısa pozisyonları kapatmak için fiyat bu seviyenin altına düştüğünde gerçekleşir."}, "open_interest": {"title": "Açık faiz", "content": "Bu sürekli vadeli işlem sözleşmesi için açık pozisyonların birleşik değeri."}, "funding_rate": {"title": "Fonlama oranı", "content": "Fiyatları piyasa ile uyumlu tutmak için yatırımcılar arasında saatlik bir işlem ücreti ödenir. Oran pozitifse uzunlar kısalara ödeme yapar. Negatifse kısalar uzunlara ödeme yapar."}, "geo_block": {"title": "Sürekli vadeli bölgenizde kullanılamıyor", "content": "<PERSON><PERSON> k<PERSON>lar veya yaptırımlar nedeniyle sürekli vadeli işlem sözleşmeleri ile işlem konumunuzda kullanılmamaktadır."}, "receive": {"title": "Al", "content": "The amount of USDC you'll receive in your wallet after withdrawal. You'll receive USDC on the Arbitrum network."}, "withdrawal_fees": {"title": "Sağlayıcı Ücreti", "content": "A flat fee charged by the provider on each withdrawal."}, "tp_sl": {"title": "Kazancı Al ve Zararda Durdur", "content": "Kazancı Al (KA), he<PERSON>flediğ<PERSON>z kazanca ulaştığınızda pozisyonunuzu otomatik olarak kapatır. <PERSON><PERSON><PERSON> (ZD), fiyat sizin aleyhinize hareket ettiğinde pozisyonunuzu kapatarak zararınızı sınırlar."}, "close_position_you_receive": {"title": "Receive amount", "content": "Your receive amount is estimated and may change slightly due to slippage."}, "notifications": {"title": "Bildirimleri aç", "description": "Emir<PERSON>in ger<PERSON><PERSON><PERSON>, likidasyonlar ve piyasa güncellemeleri gibi önemli sürekli vadeli işlem sözleşmeleri ile işlem olayları hakkında bildirim alın.", "turn_on_button": "Aç"}, "got_it_button": "<PERSON><PERSON><PERSON><PERSON>", "tpsl_count_warning": {"title": "Multiple TP/SL orders are active", "content": "To set TP/SL for the whole position, cancel your existing TP/SL orders first.", "view_orders_button": "View orders", "got_it_button": "Got it"}}, "connection": {"failed": "Bağlantı Başarısız Oldu", "error_message": "Sürekli Vadeli İşlemler hizmetine bağlanılamıyor.", "retry_connection": "Bağlantıyı Tekrar <PERSON>", "retrying_connection": "Bağlanıyor...", "connecting_to_perps": "Sürekli Vadeli İşlem Sözleşmelerine bağlanılıyor", "timeout_title": "Connection taking longer than expected"}, "chart": {"no_data": "<PERSON>ik verileri mevcut de<PERSON>", "candle_intervals": "Mum aralıkları", "candle_period_selector": {"show_more": "<PERSON><PERSON> fazla"}}, "perps_markets": "Sürekli vadeli işlem sözleşmesi piyasaları", "volume": "Hacim", "price_24h_change": "Fiyat / 24 sa. <PERSON><PERSON>", "failed_to_load_market_data": "<PERSON><PERSON><PERSON>", "tap_to_retry": "<PERSON><PERSON><PERSON> denemek i<PERSON>n", "search_by_token_symbol": "Token sembolüne göre ara", "testnet": "Test Ağı", "mainnet": "<PERSON>", "developer_options": {"hyperliquid_network_toggle": "Hyperliquid Ağ Değişimi", "simulate_connection_error": "Bağlantı Hatasını Simüle Et"}, "transactions": {"title": "Sürekli Vadeli İşlem Sözleşmeleri", "tabs": {"trades": "İşlemler", "orders": "<PERSON><PERSON><PERSON>", "funding": "Fonlama", "funding_description": "Funding history is a summary of the fees you have paid or received, as determined by the funding rates on your open positions."}, "not_found": "İşlem bulunamadı", "position": {"date": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "entry_price": "<PERSON><PERSON><PERSON> fi<PERSON>tı", "close_price": "Close price", "points": "<PERSON><PERSON>", "fees": "Toplam ücretler", "pnl": "Net K&Z"}, "order": {"date": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "limit_price": "Limit fiyatı", "filled": "Gerçek<PERSON>ş<PERSON>", "metamask_fee": "MetaMask ücreti", "hyperliquid_fee": "Hyperliquid ücreti", "total_fee": "Toplam ücret"}, "funding": {"date": "<PERSON><PERSON><PERSON>", "fee": "Ücret", "rate": "<PERSON><PERSON>"}, "view_on_explorer": "Blok gezgininde görüntüle", "empty_state": {"no_transactions": "Henüz {{type}} işlemi yok", "history_will_appear": "İşlem geçmişiniz burada görüntülenecektir"}}, "risk_disclaimer": "Perps trading is risky, and you could suddenly and without notice lose your entire margin. You trade entirely at your own risk. Market data provided by Hyperliquid. Price chart powered by", "tutorial": {"continue": "<PERSON><PERSON>", "skip": "Atla", "add_funds": "Para ekle", "what_are_perps": {"title": "Sürekli vadeli işlem sözleşmeleri nedir?", "description": "MetaMask artık bir token'ı satın almadan fiyat hareketinde işlem yapmanıza olanak sağlayan sürekli vadeli sözleşmeler piyasasını destekliyor.", "subtitle": "İşte çalışma şekli."}, "go_long_or_short": {"title": "Bir token üzerinde uzun veya kısa pozisyon açın", "description": "Pick a token to long or short, then set your order size.", "subtitle": "Fiyat yukarı giderse kazanç için uzun pozisyon açın. Fiyat aşağı giderse kazanç için kısa pozisyon açın."}, "choose_leverage": {"title": "Kaldıracınızı seçin", "description": "Kaldıraç hem kazançları hem de zararları artırır. 10 kat kaldıraçla %1 oranındaki fiyat hareketi = marjınızda %10 kazanç veya zarar."}, "watch_liquidation": {"title": "Likidasyona dikkat edin", "description": "Token likidasyon fiyatınıza ulaşırsa tüm marjınızı kaybedersiniz. Daha yüksek kaldıraç likidasyon için daha az alan kalması anlamına gelir."}, "close_anytime": {"title": "Dilediğinizde kapatın", "description": "Dilediğiniz zaman çıkış yapın. <PERSON>j<PERSON>n<PERSON>z<PERSON>, artı kazançlarınızı veya eksi zararınızı geri alırsınız."}, "ready_to_trade": {"title": "İşlem yapmaya hazır mısınız?", "fund_text_helper": "MetaMask will swap your funds to USDC on Arbitrum, and deposit them onto HyperEVM for no added fee.", "description": "Sürekli vadeli işlem sözleşmeleri hesabınıza herhangi bir token ile fonlama gerçekleştirin ve saniyeler içinde ilk işleminizi yapın."}, "got_it": "<PERSON><PERSON><PERSON><PERSON>"}}, "receive": {"title": "Al"}, "experience_enhancer_modal": {"title": "Deneyiminizi iyileştirmemize yardımcı olun", "paragraph1a": "Biz ", "paragraph1b": "dış<PERSON><PERSON> verileri (çerezlerden alınan bilgiler gibi) pazarlama iletişimlerimizle nasıl etkileşimde bulunduğunuzu öğrenmek amacıyla kullanırız.", "link": "MetaMetrics", "paragraph2": "Sizinle örneğin şunlar gibi neleri paylaşacağımızı kişiselleştirebilmemize yardımcı olur:", "bullet1": "En yeni gel<PERSON>er", "bullet2": "<PERSON><PERSON><PERSON>n ö<PERSON>", "bullet3": "İlgili diğer promosyon materyalleri", "footer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunduğunuz verileri hiçbir zaman satmayız ve dilediğiniz zaman tercihinizi değiştirebilirsiniz.", "accept": "Kabul ediyorum", "cancel": "Hayır, istemiyorum"}, "multi_rpc_migration_modal": {"description": "Artık tek bir ağ için birden fazla RPC destekliyoruz. Bilgilerin çakışmasını çözmek amacıyla en son RPC'niz varsayılan olarak seçilmiştir", "accept": "Kabul Edin"}, "qr_tab_switcher": {"scanner_tab": "QR kodunu tara", "receive_tab": "QR kodunuz"}, "banner": {"bridge": {"title": "Kö<PERSON>r<PERSON> için hazır mısınız?", "subtitle": "Tamamı cüzdanınızın içinde 9 zincir arasında geçiş yapın"}, "card": {"title": "MetaMask Kartı", "subtitle": "<PERSON><PERSON><PERSON> bölgelerde mevcuttur"}, "fund": {"title": "Cüzdanınıza para ekleyin", "subtitle": "Başlamak için token ekleyin veya transfer edin"}, "cashout": {"title": "MetaMask ile nakit <PERSON>in", "subtitle": "Nakit ka<PERSON>şılığında kriptonuzu satın"}, "aggregated": {"title": "Bakiyeniz birleştirildi", "subtitle": "<PERSON><PERSON><PERSON>a bakiye görünümünüzü kontrol edin"}, "multisrp": {"title": "Birden fazla Giz<PERSON>arma İfadesi ekleyin", "subtitle": "MetaMask'te cüzdanları içe aktarın veya kullanın"}, "solana": {"title": "Solana artık destekleniyor", "subtitle": "Başlamak için bir Solana hesabı oluşturun"}, "smartAccount": {"title": "Akıllı hesapları kullanmaya başlayın", "subtitle": "<PERSON><PERSON><PERSON>, daha akıllı özellikler"}, "backupAndSync": {"title": "Yedekleme ve senkronizasyon hizmetinizde", "subtitle": "Hesaplarınızı yedekleyin ve ayarları senkronize edin."}}, "wallet": {"title": "Cüzdan", "tokens": "To<PERSON>'lar", "collectible": "Koleksiyon", "collectibles": "NFT'ler", "defi": "<PERSON><PERSON><PERSON>", "perps": "Sürekli Vadeli İşlem Sözleşmeleri", "transactions": "İŞLEMLER", "no_collectibles": "NFT'nizi görmüyor musunuz?", "no_available_tokens": "Tokeninizi görmüyor musunuz?", "add_tokens": "Tokenleri içe aktar", "are_you_sure_exit": "Are you sure you want to exit?", "search_information_not_saved": "Your search information will not be saved.", "import_token": "Would you like to import this token?", "tokens_detected_in_account": "Bu hesapta {{tokenCount}} yeni {{tokensLabel}} bulundu", "token_toast": {"tokens_imported_title": "İçe Aktarılan Tokenler", "tokens_imported_desc": "Başarılı bir şekilde içe aktarıldı: {{tokenSymbols}}", "token_imported_title": "İçe Aktarılan Token", "token_imported_desc": "Başarılı bir şekilde içe aktarıldı: {{tokenSymbol}}", "token_imported_desc_1": "You’ve imported a token.", "tokens_import_success_multiple": "You’ve imported {{tokensNumber}} tokens.", "tokens_hidden_title": "Tokenler Gizleniyor", "tokens_hidden_desc": "Algılanan tokenler cüzdanınızdan gizleniyor", "token_hidden_title": "Token Gizleniyor", "token_hidden_desc": "{{tokenSymbol}} Gizleniyor"}, "hide_token": {"title": "<PERSON><PERSON><PERSON> Gizle?", "desc": "Gelecekte bu tokeni “Tokeni içe aktar”' öğesine gidip tokeni arayarak tekrar ekleyebilirsiniz.", "cancel_cta": "İptal", "confirm_cta": "<PERSON><PERSON><PERSON>"}, "import": "İçe aktar", "sort_by": "Şuna göre sırala", "filter_by": "Farklı filtrele", "networks": "<PERSON><PERSON><PERSON>", "popular": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON>", "current_network": "Geçerli ağ", "popular_networks": "<PERSON><PERSON><PERSON>", "all_networks": "<PERSON><PERSON><PERSON>", "declining_balance": "<PERSON><PERSON><PERSON> b<PERSON> ({{currency}} yüksek-düşük)", "alphabetically": "Alfabetik olarak (A-Z)", "add_to_get_started": "Başlamak için k<PERSON>", "token_is_needed_to_continue": "<PERSON>am etmek için {{tokenSymbol}} gerekli", "fund_your_wallet_to_get_started": "Web3'te başlamak için cüzdanınıza fon ekleyin", "add_funds": "<PERSON>on e<PERSON>", "next": "<PERSON><PERSON><PERSON>", "buy_asset": "{{asset}} sat<PERSON>n alın", "no_tokens": "Tokeniniz yok!", "show_tokens_without_balance": "Bakiyesi <PERSON>'<PERSON><PERSON>", "no_nfts_yet": "Henüz NFT yok", "nfts_autodetection_title": "NFT algılama", "nfts_autodetection_desc": "MetaMask'in otomatik olarak cüzdanınızdaki NFT'leri algı<PERSON>ıp göstermesine izin verin.", "network_details_check": "Ağ bilgileri kontrolü", "network_with_chain_id": "<PERSON><PERSON><PERSON>r kimliği olan ağ", "chain_list_returned_different_ticker_symbol": "Bu token sembolü, girilen ağ adı veya zincir kimliği ile eşleşmiyor. Pek çok popüler token benzer sembolleri kullanır ve dolandırıcılar, karşılığında daha değerli bir token gönderecekleri yönünde sizi kandırmak için bunları kullanabilirler. Devam etmeden önce her şeyi doğrulayın.", "suggested_token_symbol": "Önerilen ticker sembolü:", "potential_scam": "Bu potansiyel bir dolandırıcılıktır", "network_not_matching": "<PERSON>u ağ, ilişkili zincir kimliği veya isim ile uyumlu değil. Bu ismin pek çok popüler token tarafından kullanılması", "target_scam_network": "dolandırıcılıkların hedefi haline gelmesine neden olmaktadır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kar<PERSON><PERSON><PERSON><PERSON>ğında daha değerli bir para birimi göndereceğini belirterek sizi aldatabilir. Devam etmeden önce her şeyi doğrulayın.", "use_the_currency_symbol": "para birimi sembolü kullanır", "use_correct_symbol": "Devam etmeden önce doğru sembolü kullandığınızdan emin olun", "chain_id_currently_used": "<PERSON><PERSON> Zincir <PERSON>ği şu anda şunun tarafından kullanılıyor:", "incorrect_network_name_warning": "Kayıtlarımıza göre ağ adı bu zincir kimliği ile doğru şekilde eşleşmiyor olabilir.", "suggested_name": "Önerilen ad:", "network_check_validation_desc": "kötü amaçlı veya yanlış bir ağa bağlanma ihtimalinizi azaltır.", "cant_verify_custom_network_warning": "Özel ağları doğrulayamıyoruz. Kötü amaçlı sağlayıcıların ağ aktivitenizi kaydetmesini önlemek amacıyla sadece güvendiğiniz ağları ekleyin.", "nfts_autodetection_cta": "Ayarlarda NFT algılamayı açın", "learn_more": "Daha fazlasını öğren", "add_collectibles": "NFT'leri içe aktar", "no_transactions": "Hiç işleminiz yok!", "switch_network_to_view_transactions": "İşlemleri görüntülemek için lütfen ağ değiştirin", "send_button": "<PERSON><PERSON><PERSON>", "deposit_button": "Para Yatır", "copy_address": "<PERSON><PERSON>", "collectible_action_title": "Koleksiyon Seçenekleri", "remove_token_title": "Bu tokeni gizlemek istiyor musunuz?", "remove_collectible_title": "Bu koleksiyonu kaldırmak istiyor musunuz?", "refresh_metadata": "Meta Verileri <PERSON>", "token_removal_issue_title": "Token kaldırılırken sorun oldu.", "token_removal_issue_desc": "Token kaldırılırken bir problem oldu. <PERSON><PERSON><PERSON> den<PERSON>.", "collectible_removed_title": "Koleksiyon kaldırıldı!", "collectible_removed_desc": "Fikrinizi değiştirirseniz \"NFT'leri İçe Aktar\" düğmesine dokunarak geri ekleyebilirsiniz", "remove": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "yes": "<PERSON><PERSON>", "private_key_detected": "Özel anahtar algılandı", "do_you_want_to_import_this_account": "Bu hesabı içe aktarmak istiyor musunuz?", "error": "<PERSON><PERSON>", "logout_to_import_seed": "Bir Gizli Kurtarma İfadesini içe aktarmak için önce oturumu kapatmanız gerekiyor.", "ready_to_explore": "Blokzinciri uygulamalarını keşfetmeye başlamak için hazır mısınız?", "unable_to_load": "Bakiye yüklenmedi", "unable_to_find_conversion_rate": "dönüştürme oranı yok", "display_nft_media_desc": "Bir NFT'yi içe aktarmak için Ayarlar > Güvenlik ve gizlilik kısmında NFT medyasını göster seçeneğini açın.", "display_nft_media_cta": "NFT medyasını göster seçeneğini açın", "display_media_nft_warning": "NFT medyasını ve verilerini göstermek IP adresinizi merkezi sunuculara ifşa edebilir. Sadece mevcut riskleri anlıyorsanız NFT'yi içe aktarın.", "nfts_autodetect_title": "NFT otomatik algılama", "nfts_autodetect_cta": "NFT otomatik algılamayı etkinleştir", "turn_on_network_check_cta": "Ağ bilgileri kontrolünü aç", "display_nft_media_cta_new_1": "Bir NFT'yi görmek için NFT medyasını göster seçeneğini açın", "display_nft_media_cta_new_2": "Ayarlar > Güvenlik ve Gizlilik.", "banner": {"title": "Temel işlevsellik kapalı", "link": "Temel işlevselliği aç"}, "carousel": {"empty_state": "You're all caught up!"}}, "asset_details": {"token": "Token", "amount": "Token Miktarı", "address": "<PERSON><PERSON> s<PERSON>şme ad<PERSON>i", "decimal": "Token decimal", "network": "Ağ", "network_fee": "<PERSON><PERSON>", "lists": "<PERSON><PERSON>", "hide_cta": "<PERSON><PERSON><PERSON> gizle", "options": {"view_on_portfolio": "Portfolio'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view_on_block": "Blok gezgininde görüntüle", "token_details": "Token bilgileri", "remove_token": "Token'i kaldır"}}, "nft_details": {"bought_for": "Satın alma amacı", "highest_floor_price": "En yüksek taban fiyat", "data_unavailable": "veri mevcut de<PERSON>", "price_unavailable": "fiyat mevcut de<PERSON>", "rank": "Sıralama", "contract_address": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>i", "token_id": "<PERSON><PERSON>", "token_symbol": "Token symbol", "token_standard": "Token standardı", "date_created": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "unique_token_holders": "<PERSON><PERSON><PERSON><PERSON> <PERSON> sa<PERSON>leri", "tokens_in_collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token'ler", "creator_address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "last_sold": "<PERSON>", "highest_current_bid": "Mevcut en yüksek teklif", "options": {"view_on_os": "OpenSea'de g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remove_nft": "NFT'yi kaldır"}, "attributes": "<PERSON><PERSON><PERSON><PERSON>", "disclaimer": "Sorumluluğun Reddi: MetaMask medya dosyasını kaynak URL adresinden çeker. Bu URL adresi bazen NFT'nin mint edildiği pazar yeri tarafından değiştirilir."}, "activity_view": {"title": "Faaliyet"}, "transactions_view": {"title": "İşlemler"}, "add_asset": {"title": "Tokenleri içe aktar", "title_nft": "NFT'yi içe aktar", "search_token": "Search", "custom_token": "Custom token", "tokens": {"cancel_add_token": "İPTAL", "add_token": "İÇE AKTAR"}, "collectibles": {"cancel_add_collectible": "İPTAL", "add_collectible": "İÇE AKTAR"}, "banners": {"search_desc": "Gelişmiş token algılama şu anda {{network}} ağında kullanılabilir. ", "search_link": "<PERSON><PERSON><PERSON><PERSON>.", "custom_warning_desc": "Mevcut tokenlerin sahte sürümleri dahil olmak üzere herkes token oluşturabilir. \nDolandırıcılık ve güvenlik riskleri ", "custom_warning_link": "hakkında daha fazla bilgi edinin.", "custom_info_desc": "Token algılama henüz bu ağda mevcut değil. Lütfen tokeni manuel olarak içe aktarın ve ona güvendiğinden emin olun. Token dolandırıcılığı ve güvenlik riskleri ", "custom_info_link": "hakkında daha fazla bilgi edinin.", "custom_security_tips": "Güvenlik İpuçları"}}, "defi_positions": {"loading_positions": "DeFi pozisyonları yükleniyor...", "no_visible_positions": "Aradığınızı bulamadınız mı?", "not_supported": "Protokolünüzü henüz desteklemiyor olabiliriz.", "error_cannot_load_page": "Bu sayfayı yükleyemedik.", "error_visit_again": "<PERSON><PERSON> sonra tekrar ziyaret et<PERSON>yi den<PERSON>.", "single_token": "Yalnızca {{symbol}}", "two_tokens": "{{symbol}} +1 di<PERSON>er", "multiple_tokens": "{{symbol}} +{{count}} di<PERSON><PERSON>", "supply": "<PERSON><PERSON><PERSON><PERSON>", "stake": "Stake edildi", "borrow": "Ödünç <PERSON>", "reward": "<PERSON><PERSON><PERSON><PERSON>"}, "terms_and_conditions": {"title": "<PERSON><PERSON>", "description": "İlerleyerek şu bölümleri kabul edersiniz: ", "terms": "<PERSON><PERSON>"}, "privacy_policy": {"title": "Gizlilik Politikası", "fine_print_1": "Bu verileri başka amaçlar için kullanmaya karar verirsek sizi bilgilendireceğiz. Daha fazla bilgi için", "fine_print_2": "bölümümüzü inceleyebilirsiniz. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON> kısmına giderek dilediğiniz zaman vazgeçebilirsiniz.", "privacy_policy_button": "Gizlilik Politikası", "agree": "Kabul Ediyorum", "decline": "Hayır, teşekkürler", "description_title": "MetaMask'ı iyileştirmemize yardımcı olun", "description_content_1": "MetaMask'i iyileştirmek için temel kullanım verilerini toplamak istiyoruz. Burada sunduğunuz verileri asla satmadığımızı bilmenizi isteriz.", "description_content_2": "Topladı<PERSON><PERSON><PERSON><PERSON><PERSON> her zaman şu şekilde o<PERSON>ktır...", "description_content_3": "Profiliniz için kullanım verilerini toplarken gizliliğinizi nasıl koruduğumuzu öğrenin.", "checkbox": "<PERSON><PERSON> veril<PERSON>, pazarlama iletişimlerimizle nasıl etkileşimde bulunduğunuzu öğrenmek için kullanacağız. İlgili haberleri (ürün özellikleri gibi) paylaşabiliriz.", "action_description_1_prefix": "Özel:", "action_description_2_prefix": "Genel:", "action_description_3_prefix": "İsteğe bağlı:", "action_description_1_description": "Uygulama üzerindeki tıklamalar ve görüntülemeler depolanır ancak diğer bilgiler (genel adresiniz gibi) saklanmaz.", "action_description_2_description": "Genel bir konumu (ülkeniz veya bölgeniz gibi) algılamak için geçici olarak IP adresinizi kullanırız ancak bu bilgi asla saklanmaz.", "action_description_3_description": "Kullanım verilerinizi paylaşmak veya silmek isteyip istemediğinize dilediğiniz zaman ayarlar kısmından siz karar verirsiniz.", "cta_no_thanks": "Hayır, istemiyorum", "cta_i_agree": "Kabul ediyorum", "fine_print_1_legacy": "Bu veriler birleştirilmiştir ve bu nedenle 2016/679 sayılı Genel Veri Koruma Tüzüğü (AB) kapsamında isimsizdir.", "fine_print_2a_legacy": "*MetaMask'te varsayılan RPC sağlayıcınız olarak Infura'yı kullanırsanız siz bir işlem gönderdiğinizde Infura, IP adresinizi ve Ethereum cüzdan adresinizi toplar. Bu bilgileri sistemlerimizin bu iki veri parçasını ilişkilendirmesine izin verecek şekilde saklamayız. İlerlemeden önce RPC sağlayıcınızı", "fine_print_2b_legacy": "yapılandırabilirsiniz. \nMetaMask ve Infura'nın veri toplama açısından nasıl etkileşimde bulunduğu hakkında daha fazla bilgi için güncellememize bakın", "fine_print_2c_legacy": ". Genel olarak gizlilik uygulamalarımız hakkında daha fazla bilgi için Gizlilik Politikası bölümümüze bakın", "here_legacy": "bakın:", "description_content_1_legacy": "MetaMask olarak kullanıcılarımızın mobil uygulama ile nasıl etkileşimde bulunduğunu daha iyi anlamak amacıyla temel kullanım verileri toplamak isteriz. Bu veriler ürünümüze ilişkin kullanılabilirliği ve kullanıcı deneyimini devamlı iyileştirmek amacıyla kullanılacaktır.", "description_content_2_legacy": "MetaMask...", "action_description_1_legacy": "Her zaman Ayarlar kısmından vazgeçebilmenize izin verir", "action_description_2_legacy": "İsimsiz tıklama ve sayfa görüntüleme etkinlikleri gönderir", "action_description_3_legacy": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (belirli bir konum değil) verileri gönderir", "action_description_4_legacy": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, b<PERSON><PERSON>, hash veya kişisel bilgi <PERSON>az", "action_description_5_legacy": "Hiçbir zaman IP adresinizi almaz", "action_description_never_legacy": "<PERSON><PERSON><PERSON>", "toast_message": "Gizlilik politikamızı güncelledik", "toast_action_button": "Ka<PERSON><PERSON>", "toast_read_more": "Daha fazlasını oku"}, "template_confirmation": {"ok": "<PERSON><PERSON>", "cancel": "İptal"}, "approval_result": {"ok": "<PERSON><PERSON>", "success": "Başarılı", "error": "<PERSON><PERSON>", "resultPageSuccessDefaultMessage": "İşlem başarılı bir şekilde <PERSON>landı.", "resultPageErrorDefaultMessage": "İşlem başarısız oldu."}, "token": {"token_symbol": "Token symbol", "token_address": "<PERSON><PERSON>", "token_decimal": "Token decimal", "search_tokens_placeholder": "Token <PERSON>", "address_cant_be_empty": "Token adresi bo<PERSON> o<PERSON>az.", "address_must_be_valid": "Token adresi geçerli bir adres olmalıdır.", "symbol_cant_be_empty": "Token sembolü bo<PERSON> o<PERSON>az.", "symbol_length": "Symbol must be 11 characters or fewer", "decimals_cant_be_empty": "Token ondalıkları boş olamaz.", "decimals_is_required": "Decimal is required. Find it on:", "no_tokens_found": "<PERSON>u isimde <PERSON> bula<PERSON><PERSON>.", "select_token": "Token Seç", "address_must_be_smart_contract": "<PERSON><PERSON><PERSON><PERSON> adres algılandı. Token sözleşme adresini girin.", "billion_abbreviation": "MR", "trillion_abbreviation": "T", "million_abbreviation": "MN", "token_details": "Token bilgileri", "contract_address": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>i", "token_list": "Token listesi", "market_details": "<PERSON><PERSON><PERSON>i", "market_cap": "<PERSON><PERSON><PERSON>", "total_volume": "<PERSON>lam Hacim (24 sa)", "volume_to_marketcap": "<PERSON>cim / <PERSON><PERSON><PERSON>", "circulating_supply": "Dolaşım<PERSON>i arz", "all_time_high": "<PERSON>ü<PERSON> z<PERSON>ın en yükseği", "all_time_low": "<PERSON>ü<PERSON> z<PERSON>ın en düşüğü", "fully_diluted": "Tamamen seyreltilmiş", "unknown": "Bilinmeyen"}, "collectible": {"collectible_address": "<PERSON><PERSON>", "collectible_type": "<PERSON><PERSON><PERSON>", "collectible_token_id": "<PERSON><PERSON>", "collectible_description": "<PERSON><PERSON>ı<PERSON><PERSON>", "address_must_be_valid": "Koleksiyon adresi geçerli bir adres olmalıdır.", "address_must_be_smart_contract": "Kiş<PERSON>l adres algılandı. Koleksiyon sözleşme adresini girin.", "address_cant_be_empty": "Koleksiyon adresi boş o<PERSON>az.", "token_id_cant_be_empty": "Koleksiyon tanımlayıcı boş olamaz.", "not_owner_error_title": "<PERSON>ir <PERSON><PERSON> oldu.", "not_owner_error": "<PERSON>u koleks<PERSON><PERSON><PERSON>, bu yüzden onu e<PERSON>em<PERSON>iz.", "ownership_verification_error_title": "NFT eklenemiyor", "ownership_verification_error": "Sahipliği doğrulayamadık. Bunun nedeni standardın desteklenmemesi veya varlığın seçtiğiniz ağda mevcut olmaması olabilir.", "powered_by_opensea": "<PERSON><PERSON><PERSON><PERSON>", "id_placeholder": "Koleksiyon kimliğini girin", "collectible_token_standard": "Token Standardı", "collectible_last_sold": "<PERSON>", "collectible_last_price_sold": "<PERSON> satıldı<PERSON><PERSON> fiyat", "collectible_source": "<PERSON><PERSON><PERSON>", "collectible_link": "Bağlantı", "collectible_asset_contract": "Varlık sözleşmesi", "share_check_out_nft": "NFT'mi incele!", "share_via": "Şununla paylaşıldı:", "untitled_collection": "Adsız <PERSON>iyon", "collection": "Koleksiyon"}, "transfer": {"title": "Transfer Et", "send": "GÖNDER", "receive": "AL"}, "accounts": {"srp_index": "SRP #{{index}}", "snap_account_tag": "Snaps (Beta)", "create_new_account": "<PERSON><PERSON> bir he<PERSON><PERSON>", "new_account": "<PERSON><PERSON>", "import_account": "Bir hesabı içe aktar", "connect_hardware": "Donanım cüzdanı bağla", "imported": "İçe aktarıldı", "qr_hardware": "QR donanımı", "remove_account_title": "<PERSON><PERSON><PERSON> ka<PERSON>", "remove_account_message": "Bu hesabı gerçekten kaldırmak istiyor musunuz?", "no": "Hay<PERSON><PERSON>", "yes_remove_it": "Evet, kaldır", "remove_hardware_account": "Don<PERSON>ım hesabını kaldır", "remove_hw_account_alert_description": "Bu donanım cüzdanı hesabını kaldırmak istediğinizden emin misiniz? MetaMask Mobil ile bu hesabı tekrar kullanmak istiyorsanız donanım cüzdanınızı tekrar senkronize etmeniz gerekecek.", "remove_snap_account": "Snap hesabını kaldır", "remove_snap_account_alert_description": "Bu hesap cüzdanınızdan kaldırılacak. Devam etmeden önce lütfen orijinal Gizli Kurtarma İfadenizin veya içe aktarılan bu hesabın özel anahtarının sizde bulunduğundan emin olun. Hesap açılır menüsünden hesapları içe aktarabilir veya tekrar hesap oluşturabilirsiniz.", "remove_account_alert_remove_btn": "Kaldır", "remove_account_alert_cancel_btn": "Fark etmez", "accounts_title": "<PERSON><PERSON><PERSON><PERSON>", "connect_account_title": "<PERSON><PERSON>bı bağla", "connect_accounts_title": "Hesapları bağla", "edit_accounts_title": "Hesapları düzenle", "connected_accounts_title": "Bağlı hesaplar", "connect_description": "<PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>, faaliyetinizi <PERSON>ın ve sitenin işlem başlatmasına izin verin.", "select_accounts_description": "Bu sitede kullanılacak hesap veya hesapları seçin:", "connect_multiple_accounts": "Birden fazla hesabı bağla", "connect_more_accounts": "<PERSON><PERSON> fazla hesap ba<PERSON>la", "add": "<PERSON><PERSON>", "cancel": "İptal", "connect": "Bağlan", "connect_with_count": "{{count<PERSON><PERSON><PERSON>}} Bağla", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "unselect_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> kaldır", "permissions": "<PERSON><PERSON><PERSON>", "revoke": "İptal et", "revoke_all": "Tümünü iptal et", "ledger": "Ledger", "site_permission_to": "Bu sitenin şunları yapmaya izni vardır:", "address_balance_activity_permission": "<PERSON><PERSON>, he<PERSON><PERSON> b<PERSON><PERSON>i ve aktiviteyi görme", "suggest_transactions": "Onaylanacak işlemleri önerme", "accounts_connected": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>", "account_connected": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>", "accounts_disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON> bağlantısı kesildi.", "account_disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON> bağlantısı kesildi.", "disconnect": "Bağlantıyı kes", "disconnect_all": "Tümünün bağlantısını kes", "reconnect_notice": "{{dappUrl}} bağlantınızı keserseniz bu siteyi tekrar kullanabilmek için hesaplarınızı ve ağlarınızı tekrar bağlamanız gerekecek.", "disconnect_all_accounts": "<PERSON>ü<PERSON> hesap<PERSON><PERSON>n bağlantısını kes", "deceptive_site_ahead": "Aldatıcı siteye gidiliyor", "deceptive_site_desc": "Ziyaret etmeye çalıştığınız sayfa güvenli değil. Saldırganlar tehlikeli bir şey yapmanız konusunda sizi aldatabilir.", "learn_more": "<PERSON>ha fazla bilgi edin", "advisory_by": "Ethereum Kimlik Avı Algılayıcı ve PhishFort tarafından sunulan uyarı", "potential_threat": "Potansiyel tehditler şunları içerir", "fake_metamask": "Sahte MetaMask sürümleri", "srp_theft": "Gizli kurtarma ifadesi veya şifre hırsızlığı", "malicious_transactions": "Varlıkların ç<PERSON>ınmasına neden olan kötü amaçlı işlemler", "secret_recovery_phrase": "<PERSON><PERSON><PERSON>", "account_name": "<PERSON><PERSON><PERSON> adı", "select_secret_recovery_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadesi seçin", "reveal_secret_recovery_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadesini göster", "add_new_hd_account_helper_text": "Yeni hesabınızın oluşturulacağı Gizli Kurtarma İfadesi", "accounts": "hesap", "show_accounts": "<PERSON><PERSON><PERSON>", "hide_accounts": "<PERSON><PERSON><PERSON>", "labels": {"bitcoin_testnet_account_name": "Bitcoin Test Ağı Hesabı", "bitcoin_account_name": "Bitcoin Hesabı", "bitcoin_signet_account_name": "Bitcoin Signet Hesabı", "bitcoin_regtest_account_name": "Bitcoin Regtest Hesabı", "solana_devnet_account_name": "Solana Devnet He<PERSON>bı", "solana_testnet_account_name": "Solana Test Ağı Hesabı", "solana_account_name": "<PERSON><PERSON>"}, "error_messages": {"failed_to_create_account": "{{clientType}} hesabı oluşturulamadı"}, "account_connect_create_initial_account": {"description": "Bu siteye bağlanmak için bir Solana hesabı gereklidir.", "button": "Solana hesabı oluştur"}, "no_accounts_found": "<PERSON><PERSON><PERSON> b<PERSON>", "no_accounts_found_for_search": "Aramanızla eşleşen bir hesap bulunamadı", "search_your_accounts": "Hesaplarınızı arayın"}, "toast": {"connected_and_active": "bağlandı ve aktif.", "now_active": "şimdi aktif.", "network_added": "başarılı bir <PERSON><PERSON><PERSON>", "network_removed": "başarılı bir <PERSON><PERSON><PERSON> kaldırıldı", "network_deleted": "başarılı bir <PERSON><PERSON><PERSON>", "network_permissions_updated": "<PERSON><PERSON>i güncellendi", "revoked": "iptal edildi.", "revoked_all": "Tüm hesaplar iptal edildi.", "accounts_connected": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>.", "account_connected": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>.", "accounts_permissions_updated": "<PERSON><PERSON><PERSON>", "accounts_disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON> bağlantısı kesildi.", "account_disconnected": "<PERSON><PERSON><PERSON><PERSON><PERSON> bağlantısı kesildi.", "disconnected": "bağlantısı kesildi.", "disconnected_all": "<PERSON><PERSON><PERSON> hesa<PERSON><PERSON>n bağlantısı kesildi.", "disconnected_from": "{{dappHostName}} ile bağ<PERSON><PERSON> kesildi", "permissions_updated": "<PERSON><PERSON><PERSON> g<PERSON>", "nft_detection_enabled": "NFT otomatik algılama etkinleştirildi"}, "connect_qr_hardware": {"title": "QR tabanlı bir donanım cüzdanı bağlayın", "description1": "QR kodları ile iletişim kuran kapalı bir donanım cüzdanı bağlayın.", "description2": "Nasıl çalışır?", "description3": "Re<PERSON>i olarak desteklenen kapalı donanım cüzdanları şunları içerir:", "keystone": "<PERSON>", "ngravezero": "<PERSON><PERSON>", "learnMore": "<PERSON>ha fazla bilgi edin", "buyNow": "<PERSON><PERSON><PERSON> al", "tutorial": "Öğretici", "description4": "Temel (öğretici)", "description5": "1. <PERSON><PERSON>ızı <PERSON>çın", "description6": "2. ··· <PERSON><PERSON><PERSON><PERSON><PERSON>, daha <PERSON>ra <PERSON> et sekmesine gidin", "button_continue": "<PERSON><PERSON>", "hint_text": "Donanım cüzdanınızı tarayarak ", "purpose_connect": "b<PERSON><PERSON><PERSON>", "purpose_sign": "kilit cüzdanınızı okutun", "select_accounts": "<PERSON><PERSON><PERSON>"}, "data_collection_modal": {"accept": "<PERSON><PERSON>", "content": "Pazarlama amacıyla veri toplama seçeneğini kapattınız. Bu, sadece bu cihaz için geçerlidir. MetaMask'i başka cihazlarda kullanırsanız orada da vazgeçtiğinizden emin olun."}, "account_selector": {"prev": "ÖNCEKİ", "next": "SONRAKİ", "unlock": "<PERSON><PERSON><PERSON>", "forget": "Bu cihazı unut"}, "address_selector": {"select_an_address": "<PERSON><PERSON> ad<PERSON> se<PERSON>"}, "app_settings": {"enabling_notifications": "Bil<PERSON><PERSON><PERSON> etkinleştiriliyor...", "updating_notifications": "Bildirimler güncelleniyor...", "updating_account_settings": "<PERSON><PERSON>p <PERSON> güncelleniyor...", "reset_notifications_title": "Bildirimleri sıfırla", "reset_notifications_description": "Bildir<PERSON><PERSON>in sı<PERSON>, bildirim depolama anahtarlarınızı sildiğiniz ve tüm bildirim geçmişinizi sıfırladığınız anlamına gelir. Bunu yapmak istediğinizden emin misiniz?", "reset_notifications": "Bildirimleri sıfırla", "reset_notifications_success": "Bildirim depol<PERSON> anah<PERSON> silindi/yeniden oluşturuldu ve bildirim geçmişi sıfırlandı.", "notifications_dismiss_modal": "<PERSON><PERSON> say", "select_rpc_url": "RPC URL adresini seç", "title": "<PERSON><PERSON><PERSON>", "current_conversion": "Temel Para Birimi", "current_language": "Geçerli Dil", "ipfs_gateway": "IPFS Ağ Geçidi", "ipfs_gateway_content": "<PERSON>aMask, IPFS'de depolanan NFT'lerinizin görüntülerini göstermek, tarayıcınızın adres çubuğuna girilen ENS adresleri ile ilgili bilgileri göstermek ve farklı token'lerin simgelerini almak için üçüncü taraf hizmetleri kullanır. Siz bunları kullanırken IP adresiniz bu hizmetlerle paylaşılabilir.", "ipfs_gateway_down": "Geçerli IPFS ağ geçidiniz bozuk", "ipfs_gateway_desc": "Tercih ettiğiniz IPFS ağ geçidini seçin.", "search_engine": "<PERSON><PERSON>", "new_RPC_URL": "Yeni RPC Ağı", "state_logs": "<PERSON><PERSON>", "add_network_title": "<PERSON><PERSON>", "auto_lock": "Otomatik kilitle", "auto_lock_desc": "Uygulama otomatik olarak kilitlenmeden süre miktarını seçin.", "state_logs_desc": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her sorunu çözmesine yardımcı olur. Lütfen hamburger simgesinden > Geri Bildirim Gönder kısmından MetaMask destek bölümüne gönderin veya varsa mevcut sorgunuza yanıt verin.", "autolock_immediately": "<PERSON><PERSON>", "autolock_never": "<PERSON><PERSON><PERSON>", "autolock_after": "{{time}} saniye sonra", "autolock_after_minutes": "{{time}} da<PERSON>ka sonra", "reveal_seed_words": "Seed <PERSON><PERSON><PERSON><PERSON>", "reset_account": "Hesabı Sıfırla", "state_logs_button": "<PERSON><PERSON> Günlüklerini İndir", "reveal_seed_words_button": "SEED SÖZCÜKLERİNİ GÖSTER", "reset_account_button": "Hesabı Sıfırla", "reset_account_confirm_button": "Eve<PERSON>, sıfırla", "reset_account_cancel_button": "İptal", "reset_account_modal_title": "Hesabı Sıfırla?", "clear_approvals_modal_title": "<PERSON><PERSON> Veriler<PERSON>?", "clear_approvals_modal_message": "Merkeziyetsiz tüm uygulamaların hesap bilgilerini tekrar görüntülemek için erişim istemesi gerekecek.", "clear_browser_history_modal_title": "Tarayıcı Geçmişini Temizle?", "clear_browser_history_modal_message": "Tüm tarayıcı geçmişinizi silmek üzereyiz. <PERSON><PERSON> misiniz?", "clear_cookies_modal_title": "Tarayıcı Çerezlerini Temizle", "clear_cookies_modal_message": "Tarayıcının çerezlerini kaldırmak üzereyiz. <PERSON><PERSON> misiniz?", "reset_account_modal_message": "Hesabınızın sıfırlanması işlem faaliyetinizi temizler.", "save_rpc_url": "KAYDET", "invalid_rpc_prefix": "URI adresleri uygun HTTPS ön eki gerektirir", "invalid_rpc_url": "RPC URL geçersiz", "invalid_block_explorer_url": "Blok Gezgini URL adresi geçersiz", "sync": "SENKRONİZE ET", "clear_approved_dapps": "ONAYLI MERKEZİYETSİZ UYGULAMALARI TEMİZLE", "clear_browser_history": "TARAYICI GEÇMİŞİNİ TEMİZLE", "clear_approve_dapps_desc": "Onaylı merkeziyetsiz uygulamaları temizle", "clear_browser_history_desc": "Tarayıcı geçmişini temizle", "clear_browser_cookies_desc": "Tarayıcı çerezlerini temizle", "clear": "TEMİZLE", "protect_cta": "<PERSON><PERSON>", "protect_title": "Cüzdan kurtarma", "banner_social_login_enabled": "{{authConnection}} ile oturum aç", "manage_recovery_method": "Kurtarma yöntemlerini yönet", "video_failed": "Video Yükleme İşlemi Başarısız Oldu.", "protect_desc": "Cüzdanınıza erişimi asla kaybetmemeniz için Gizli Kurtarma İfadenizi yedekleyin. Yalnızca sizin erişiminiz olan ve unutmayacağınız güvenli bir yerde sakladığınızdan emin olun", "protect_desc_no_backup": "Bu sizin cüzdanınızın 12 sözcükten oluşan ifadesidir. Bu ifade, cüzdanınızda para gönderme kabiliyeti de dahil olmak üzere tüm şimdiki ve gelecekteki hesaplarınızı kontrol etmek için kullanılabilir. Bu ifadeyi güvenli bir şek<PERSON>, hiç kimseyle PAYLAŞMAYIN. MetaMask bu anahtarı kurtarmanıza yardımcı olamaz.", "learn_more": "Daha fazlasını öğren.", "seedphrase_not_backed_up": "Önemli! Gizli Kurtarma İfadesi yedeklenmedi", "seedphrase_backed_up": "<PERSON><PERSON><PERSON> Kurtarma İfadesi yedeklendi", "back_up_now": "<PERSON><PERSON><PERSON>", "back_up_again": "<PERSON><PERSON><PERSON>", "view_hint": "İpucunu gö<PERSON>", "privacy_mode": "<PERSON><PERSON><PERSON> mod", "privacy_mode_desc": "Web siteleri, hesap bilgilerinizi görüntülemek için er<PERSON><PERSON> is<PERSON>dir.", "nft_opensea_mode": "OpenSea API'yi etkin<PERSON>ştir", "nft_opensea_desc": "NFT medya ve verilerinin görüntülenmesi IP adresini merkezi olan sunuculara ifşa edebilir. NFT verilerini almak için OpenSea'nin API'sini kullanın. NFT otomatik algılama OpenSea'nin API'sine güvenilir ve kapatıldığında kullanılamaz. NFT otomatik algılamanın etkinleştirilmesi cüzdanınıza herkes tarafından sahte NFT'lerin gönderilmesine maruz bırakabilir ve bir saldırganın sizin Ethereum adresinizden IP adresinizi öğrenmesine izin verebilir.", "nft_autodetect_mode": "NFT'leri otomatik algıla", "nft_autodetect_desc": "NFT medya ve verilerinin gösterimi IP adresinizi merkezi sunuculara ifşa edebilir. Cüzdanınızdaki NFT'leri algılamak için üçüncü taraf API'ler (OpenSea gibi) kullanılır. Bu da, hesap adresinizi bu hizmetlere maruz bırakır. Uygulamanın bu hizmetlerden veri çekmesini istemiyorsanız bunu devre dışı bırakın.", "show_fiat_on_testnets": "Test ağlarında dönüşümü göster", "show_fiat_on_testnets_desc": "Test ağlarındaki fiat para dönüşümünü göstermek için bunu seçin", "show_fiat_on_testnets_modal_title": "<PERSON><PERSON><PERSON><PERSON> olun", "show_fiat_on_testnets_modal_description": "Sizden bu özelliği açmanız istenirse dolandırılıyor olabilirsiniz. Bu tokenlerin hiçbir parasal değeri yoktur ve sadece test amaçlıdır. <PERSON><PERSON> <PERSON><PERSON><PERSON>, geliştiricilerin uygulamalarının çalıştığından emin olmalarına yardımcı olur.", "show_fiat_on_testnets_modal_learn_more": "Daha fazla bilgi edinin.", "show_fiat_on_testnets_modal_button": "<PERSON><PERSON> et", "show_hex_data": "On Altılı Verileri Göster", "show_hex_data_desc": "<PERSON><PERSON><PERSON><PERSON> ekranında on altılı veri alanını göstermek için bunu seçin.", "show_custom_nonce": "İşlem nonce numarasını kişiselleştir", "custom_nonce_desc": "Onay ekranlarında nonce numarasını (işlem numarası) değiştirmek için bunu açın. Bu gelişmiş bir özelliktir, dikka<PERSON><PERSON> kull<PERSON>.", "accounts_identicon_title": "Account icon", "accounts_identicon_desc": "Choose from three different styles of unique icons that can help you identify accounts at a glance.", "jazzicons": "Jazzicons", "blockies": "Blockies", "general_title": "<PERSON><PERSON>", "general_desc": "Para birimi dö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, birincil para birimi, dil ve arama motoru", "advanced_title": "Gelişmiş", "advanced_desc": "<PERSON><PERSON><PERSON><PERSON> geliştirici özellikleri, <PERSON><PERSON><PERSON><PERSON>ı<PERSON><PERSON>, kurulum test ağları, durum günlükleri, IPFS ağ geçidi ve özel RPC", "notifications_title": "Cüzdanınızın kullanımı", "notifications_desc": "Bildirimlerinizi yönetin", "allow_notifications": "Bildirimlere izin ver", "enable_push_notifications": "Anlık bildirimleri etkinleştir", "allow_notifications_desc": "Bildirimler ile cüzdanınızda neler olduğundan haberdar olun. Bildirimleri kullanmak için cihazlarınızdaki bazı ayarları senkronize etmek amacıyla bir profil kullanıyoruz.", "notifications_opts": {"customize_session_title": "Bildirimlerinizi özelleştirin", "customize_session_desc": "Almak istediğiniz bildirim türlerini açın:", "account_session_title": "<PERSON><PERSON><PERSON> akt<PERSON>", "account_session_desc": "Hakkında bildirim almak istediğiniz hesapları seçin:", "assets_sent_title": "Gönderilen Varlıklar", "assets_sent_desc": "Fonlar ve NFT", "assets_received_title": "Alınan Varlıklar", "assets_received_desc": "Fonlar ve NFT", "defi_title": "<PERSON><PERSON><PERSON>", "defi_desc": "Stake, swap ve köprü", "snaps_title": "Snap'ler", "snaps_desc": "<PERSON>ni özellikler ve güncellemeler", "products_announcements_title": "<PERSON><PERSON><PERSON><PERSON>", "products_announcements_desc": "<PERSON>ni ürünler ve özellikler", "perps_title": "Sürekli vadeli işlem sözleşmeleri ile işlem"}, "contacts_title": "<PERSON><PERSON><PERSON>", "contacts_desc": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, kaldır ve hesapların<PERSON> yönet", "permissions_title": "<PERSON><PERSON><PERSON>", "permissions_desc": "Sitelere ve uygulamalara verilen izinleri yönet", "no_permissions": "İzin yok", "no_permissions_desc": "Bir siteye veya bir uygulamaya bağladığınız hesabı burada göreceksiniz.", "security_title": "Güvenlik ve Gizlilik", "back": "<PERSON><PERSON>", "security_desc": "<PERSON><PERSON><PERSON><PERSON>ı, <PERSON>aMetrics, özel anahtar ve Gizli Kurtarma İfadesi.", "networks_title": "<PERSON><PERSON><PERSON>", "networks_default_title": "Varsayılan Ağ", "network_delete": "Bu ağı silerseniz bu ağdaki varlıklarınızı görüntülemek için bu ağı tekrar eklemeniz gerekir", "networks_default_cta": "<PERSON><PERSON> <PERSON><PERSON><PERSON> kullan", "add_rpc_url": "RPC URL adresi ekle", "add_block_explorer_url": "Blok Gezgini URL adresi ekle", "networks_desc": "Kişisel RPC ağı ekle ve düzenle", "network_name_label": "<PERSON><PERSON> Adı", "network_name_placeholder": "<PERSON><PERSON> (isteğe bağlı)", "network_rpc_url_label": "RPC URL adresi", "network_rpc_name_label": "RPC Adı", "network_rpc_placeholder": "Yeni RPC Ağı", "network_failover_rpc_url_label": "Yedek RPC URL adresi", "failover": "<PERSON><PERSON><PERSON><PERSON>", "network_chain_id_label": "<PERSON><PERSON><PERSON><PERSON>", "network_chain_id_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "network_symbol_label": "Sembol", "network_block_explorer_label": "Blok Gezgini URL adresi", "network_block_explorer_placeholder": "Blok Gezgini URL adresi (isteğe bağlı)", "network_chain_id_warning": "Geçersiz Z<PERSON>ci<PERSON>", "network_other_networks": "<PERSON><PERSON><PERSON>", "network_rpc_networks": "RPC Ağları", "network_add_network": "<PERSON><PERSON>", "network_add_custom_network": "<PERSON>zel bir ağ ekle", "network_add": "<PERSON><PERSON>", "network_save": "<PERSON><PERSON>", "remove_network_title": "Bu ağı kaldırmak istiyor musunuz?", "remove_network": "Kaldır", "cancel_remove_network": "İptal", "info_title": "MetaMask Hakkında", "info_title_beta": "MetaMask Beta Hakkında", "info_title_flask": "MetaMask Flask Hakkında", "experimental_title": "Den<PERSON>sel", "experimental_desc": "WalletConnect ve daha fazlası...", "legal_title": "<PERSON><PERSON><PERSON>", "conversion_title": "Para birimi dönüştürme", "conversion_desc": "Uygulama genelinde belirli bir para birimini kullanarak fiat para değerlerini göster.", "primary_currency_title": "Birincil Para Birimi", "primary_currency_desc": "Zincirin yerli para biriminde değerleri göstermeye öncelik vermek için Yerel seçeneğini seçin (ör. ETH). Kendi seçtiğiniz fiat paradaki değerleri göstermeye öncelik vermek için Fiat Para seçeneğini seçin.", "primary_currency_text_first": "<PERSON><PERSON>", "primary_currency_text_second": "Fiat Para", "language_desc": "Uygulamayı farklı bir desteklenen dile çevir.", "engine_desc": "URL çubuğuna arama terimlerini girerken kullanılan varsayılan arama motorunu değiştir.", "reset_desc": "Bu eylem işlem faaliyetini temizler. Bu veriler geri alınamaz.", "rpc_desc": "Sunulan ağlardan biri yerine URL adresi aracılığıyla kişisel bir RPC özellikli bir ağ kullan.", "hex_desc": "<PERSON><PERSON><PERSON><PERSON> ekranında on altılı veri alanını göstermek için bunu seçin.", "clear_privacy_title": "Giz<PERSON>lik verilerini temizle", "clear_privacy_desc": "Giz<PERSON>lik verileri te<PERSON>, böylece tüm web sitelerinin hesap bilgilerinizi tekrar görüntülemek için erişim istem<PERSON> gere<PERSON>r.", "clear_history_desc": "Tüm tarayıcı geçmişini temizlemek için bu seçeneği seçin.", "clear_cookies_desc": "Tarayıcının çerezlerini temizlemek için bu seçeneği seçin.", "metametrics_title": "MetaMetrics'e katıl", "metametrics_description": "MetaMetrics'in ürünümüzü iyileştirmek için temel kullanım ve tanılama verilerini toplamasına izin verin. Bu cihaz için MetaMetrics'i devre dışı bırakabilirsiniz.", "data_collection_title": "Pazarlama amacıyla veri toplama", "data_collection_description": "MetaMetrics'i, pazarlama iletişimlerimizle nasıl etkileşimde bulunduğunuzu öğrenmek için kullanacağız. İlgili haberleri (ürün özellikleri ve diğer materyaller gibi) paylaşabiliriz.", "batch_balance_requests_title": "<PERSON><PERSON> hesap b<PERSON><PERSON>", "batch_balance_requests_description": "Tek seferde tüm hesaplarınız için bakiye güncellemeleri alın. Bu özelliğin kapatılması başkalarının bir hesabı başka bir hesapla ilişkilendirme olasılığının daha düşük olacağı anlamına gelir.", "third_party_title": "<PERSON><PERSON><PERSON> al", "third_party_description": "Geçmişteki gelen işlemlerini göstermek için Üçüncü taraf API'ler (Etherscan) kullanılır. Bu hizmetlerden veri çekmemizi istemiyorsanız bunu kapatın.", "metametrics_opt_out": "MetaMetrics'ten Ayrıl", "metametrics_restart_required": "Değişikliklerin geçerli olması için uygulamayı yeniden başlatmanız gerekir.", "create_password": "<PERSON><PERSON><PERSON>", "invalid_password": "Şifre geç<PERSON>iz", "invalid_password_message": "Şifre yanlıştı. Lütfen tekrar deneyin.", "security_heading": "Güvenlik", "general_heading": "<PERSON><PERSON>", "privacy_heading": "Gizlilik", "failed_to_fetch_chain_id": "Zincir kimliği alınamadı. RPC URL adresiniz doğru mu?", "endpoint_returned_different_chain_id": "Uç nokta farklı bir zincir kimliği getirdi: %{chainIdReturned}", "chain_id_required": "Zincir kimliği gereklidir. Ağ tarafından getirilen zincir kimliği ile uyumlu olmalıdır. Bir ondalık sayı veya '0x' ön ekli bir on altılı sayı girebilirsiniz.", "invalid_hex_number": "Geçersiz on altılı sayı.", "invalid_hex_number_leading_zeros": "Geçersiz on altılı sayı. Lütfen baştaki tüm sıfırları silin.", "invalid_number": "Geçersiz sayı. Ondalık sayısı veya '0x' ön ekli on altılı bir sayı girin.", "invalid_number_leading_zeros": "Geçersiz sayı. Baştaki tüm sıfırları silin.", "invalid_number_range": "Geçersiz sayı. 1 ile %{maxSafeChainId} arasında bir sayı girin.", "hide_zero_balance_tokens_title": "Bakiyesi Olmayan Tokenleri Gizle", "hide_zero_balance_tokens_desc": "Bakiyesi olmayan tokenlerin token listenizde gösterilmesini önler.", "token_detection_title": "Token'leri otomatik algıla", "token_detection_description": "Cüzdanınıza gönderilen yeni tokenleri algılamak ve göstermek için üçüncü taraf API'leri kullanırız. Bu hizmetlerden veri çekmek istemiyorsanız kapatın.", "theme_button_text": "Temayı Değiştir", "theme_title": "<PERSON><PERSON> ({{theme}})", "theme_description": "Temayı ayarlayarak uygulamanın görünümünü değiştir.", "theme_os": "Sistem", "theme_light": "Açık", "theme_dark": "<PERSON><PERSON>", "mainnet": "<PERSON>", "test_network_name": "Test ağları", "custom_network_name": "<PERSON><PERSON>", "popular": "<PERSON><PERSON><PERSON>", "delete": "Sil", "account": "hesap", "accounts": "hesap", "network": "ağ", "networks": "<PERSON><PERSON><PERSON>", "network_exists": "Bu ağ zaten eklenmiş.", "unMatched_chain": "Kayıtlarımıza göre bu URL adresi bu zincir kimliğinin bilinen bir sağlayıcısı ile uyumlu değil.", "unMatched_chain_name": "Bu zincir kimliği ağ adı ile uyumlu değil.", "url_associated_to_another_chain_id": "Bu URL adresi başka bir zincir kimliği ile ilişkilidir.", "chain_id_associated_with_another_network": "Girdiğiniz bilgiler mevcut bir zincir kimliği ile ilişkilidir. Bilgilerinizi güncelleyin veya", "network_already_exist": "Zaten aynı zincir kimliği veya RPC URL adresi ile bir ağınız var. Yeni bir zincir kimliği veya RPC URL adresi girin", "edit_original_network": "orijinal ağı düzenleyin", "find_the_right_one": "Doğrusunu şurada bulabilirsiniz:", "delete_metrics_title": "MetaMetrics verilerini sil", "delete_metrics_description_part_one": "Bu işlem cüzdanınızla ilişkili geçmiş", "delete_metrics_description_part_two": "MetaMetrics", "delete_metrics_description_part_three": "verilerini silecektir.", "delete_metrics_description_before_delete": "Bu veriler silindikten sonra cüzdanınız ve hesaplarınız tam olarak şimdi olduğu gibi kalacaktır. Bu işlem 30 güne kadar sürebilir. Şunu görü<PERSON>ü<PERSON>in:", "delete_metrics_description_after_delete_part_one": "<PERSON>u eylemi ş<PERSON> başlattınız:", "delete_metrics_description_after_delete_part_two": ". Bu işlem 30 gün kadar sürebilir. <PERSON><PERSON><PERSON>:", "delete_metrics_description_privacy_policy": "Gizlilik Politikası.", "delete_metrics_button": "MetaMetrics verilerini sil", "check_status_button": "<PERSON><PERSON><PERSON>", "delete_metrics_confirm_modal_title": "MetaMetrics verilerini sil?", "delete_metrics_confirm_modal_description": "Tüm MetaMetrics verilerini kaldırmak üzeresiniz. <PERSON><PERSON> misiniz?", "delete_wallet_data_title": "Cüzdanı sıfırla", "delete_wallet_data_description": "Bu işlem cüzdanla ilgili tüm verileri cihazınızdan silecek. Hesaplar blokzincirinde bulunur ve MetaMask ile ilgili değildir. Dilediğiniz zaman Gizli Kurtarma İfadenizi kullanarak hesaplarınızı kurtarabilirsiniz.", "delete_wallet_data_button": "Cüzdanı sıfırla", "delete_data_status_title": "<PERSON><PERSON><PERSON>", "delete_data_status_description": "G<PERSON><PERSON><PERSON> durum ", "delete_metrics_error_title": "<PERSON>u anda bu verileri si<PERSON>.", "delete_metrics_error_description": "Bu talep analitik bir sistem sunucusu sorunundan dolayı şu anda tama<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "ok": "<PERSON><PERSON>", "clear_sdk_connections_title": "Tüm MetaMask SDK Bağlantılarını temizle", "clear_sdk_connections_text": "Tüm bağlantılar temizlenecek ve uygulamaların tekrar bağlantı talep etmesi gerekecek", "sdk_connections": "MetaMask SDK Bağlantıları", "manage_sdk_connections_title": "Bağlantıları yönet", "manage_sdk_connections_text": "Sitelere ve/veya MetaMask SDK bağlantılarını kaldırın.", "fiat_on_ramp": {"title": "Kripto Al ve Sat", "description": "B<PERSON>lge ve daha fazlası...", "current_region": "Mevcut Bölge", "reset_region": "Bölgeyi Sıfırla", "no_region_selected": "<PERSON><PERSON><PERSON>", "sdk_activation_keys": "SDK Aktivasyon Anahtarları", "activation_keys_description": "Aktivasyon Anahtarları belirli özellikleri veya sağlayıcıları etkinleştirecektir.", "add_activation_key": "Aktivasyon Anahtarı Ekle", "edit_activation_key": "Aktivasyon Anahtarını Düzenle", "paste_or_type_activation_key": "Bir Aktivasyon Anahtarı yapıştır veya gir", "add_label": "<PERSON>u anah<PERSON> bir etiket ekle", "label": "Etiket", "key": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal et", "deposit_provider_logout_button": "{{depositProviderName}} oturumunu kapat", "deposit_provider_logged_out": "{{depositProviderName}} oturumu kapatıldı"}, "request_feature": "Bir özellik talep et", "contact_support": "Destek ile iletişime geçin", "display_nft_media": "NFT Medyasını göster", "display_nft_media_desc": "NFT medyasını ve verilerini göstermek IP adresinizi OpenSea veya diğer üçüncü taraflara ifşa edebilir. NFT otomatik algılama bu özelliğe dayanır ve bu kapatıldığında kullanılamaz.", "autodetect_nft_desc": "MetaMask'in üçüncü tara<PERSON> (OpenSea) kullanarak size ait olan NFT'leri eklemesine izin verin. NFT'leri otomatik algılama, IP adresinizi ve hesap adresinizi bu hizmetlere ifşa eder. Bu özelliğin etkinleştirilmesi IP adresinizi Ethereum adresinizle ilişkilendirebilir ve dolandırıcılar tarafından airdrop'u gerçekleştirilen sahte NFT'leri gösterebilir. Bu riski önlemek için token'leri elle ekleyebilirsiniz.", "display_nft_media_desc_new": "NFT medyasını ve verilerini <PERSON>, IP adresinizin OpenSea veya diğer üçüncü taraflarla paylaşılmasına neden olabilir. NFT otomatik algılama bu özelliğe dayanır ve kapatıldığında kullanılamaz. NFT medyası tamamen IPFS'de bulunuyorsa bu özellik kapalı olduğunda bile görüntülenmeye devam edebilir.", "use_safe_chains_list_validation_desc_1": "MetaMask ", "use_safe_chains_list_validation_desc_2": "adlı bir üçüncü taraf hizmetini kullanır. Bu, kötü amaçlı ve yanlış ağa bağlanma ihtimalinizi düşürür. Bu özelliği kullanırken IP adresiniz şununla paylaşılır:  ", "snaps": {"title": "Snap'ler", "description": "Snap'le<PERSON>z için genel bakış ve yönetim", "snap_ui": {"link": {"accessibilityHint": "<PERSON>ni bir sekmede açılır"}}, "snap_settings": {"remove_snap_section_title": "S<PERSON>'i kaldır", "remove_snap_section_description": "<PERSON>u eylem snap'i, verilerini ve verilen izinlerini siler.", "remove_button_label": "{{snapName}} adlı snap'i kaldır", "remove_account_snap_warning": {"title": "S<PERSON>'i kaldır", "description": "Bu Snap kaldırıldığında bu hesaplar MetaMask'ten kaldırılır:", "remove_account_snap_alert_description_1": "Bu snap'i kaldırmak istediğinizi onaylamak için", "remove_account_snap_alert_description_2": "yazın:", "banner_title": "Bu Snap'i kaldırmadan önce bu Snap tarafından oluşturulan tüm hesaplara kendi başınıza eri<PERSON>im sağlayabildiğinizden emin olun", "cancel_button": "İptal et", "continue_button": "<PERSON><PERSON> et", "remove_snap_button": "S<PERSON>'i kaldır", "remove_snap_error": "{{snapName}} kaldırılamadı", "remove_snap_success": "{{snapName}} kaldırıldı"}}, "snap_details": {"install_date": "{{date}} <PERSON><PERSON><PERSON><PERSON>", "install_origin": "<PERSON><PERSON><PERSON><PERSON>", "enabled": "Etkinleş<PERSON><PERSON>", "version": "S<PERSON>r<PERSON><PERSON>"}, "keyring_account_list_item": {"account_name": "<PERSON><PERSON><PERSON> adı", "public_address": "<PERSON><PERSON>"}, "snap_permissions": {"approved_date": "{{date}} ta<PERSON><PERSON><PERSON> on<PERSON>ı", "permission_section_title": "<PERSON><PERSON><PERSON>", "permission_requested_now": "Şimdi talep edildi", "human_readable_permission_titles": {"endowment:long-running": "Süresiz çalıştır", "endowment:network-access": "İnternet erişimi", "endowment:transaction-insight": "İşlem içgörülerini görüntüle", "endowment:cronjob": "Periyodik eylemleri planla ve çalıştır", "endowment:rpc": {"snaps": "Diğer snap'lerin do<PERSON> bu snap ile iletişim kurmasına izin ver", "dapps": "Merkeziyetsiz uygulamaların doğrudan bu <PERSON> ile iletişim kurmasına izin ver"}, "snap_confirm": "Özel iletişim kutuları görüntüle", "snap_manageState": "Verilerinizi cihazınızda saklayın ve yönetin", "snap_notify": "Bildirimleri göster", "snap_getBip32Entropy": "{{protocol}} hesaplarınızı ve varlıklarınızı kontrol et", "snap_getBip32PublicKey": "{{protocol}} i<PERSON><PERSON> genel an<PERSON>ı<PERSON>ı<PERSON><PERSON><PERSON>", "snap_getBip44Entropy": "{{protocol}} hesaplarınızı ve varlıklarınızı kontrol et", "snap_getEntropy": "Bu <PERSON> için e<PERSON><PERSON>z rastgele anahtarları türet", "endowment:keyring": "Ethereum hesaplarını ekleme ve kontrol etme taleplerine izin ver", "wallet_snap": "{{otherSnapName}} adlı snap'e bağlan", "endowment:webassembly": "WebAssembly desteği", "endowment:ethereum-provider": "Ethereum sağlayıcısına erişim sa<PERSON>la", "endowment:unknown": "Bilinmeyen izin", "snap_getLocale": "<PERSON><PERSON><PERSON> ettiğiniz dili gö<PERSON>", "endowment:caveat:transaction-origin": "İşlem öneren web sitelerinin kökenlerini gör", "endowment:extend-runtime": "Çalışma süresini uzat", "snap_dialog": "Özel iletişim kutuları göster", "snap_manageAccounts": "Ethereum hesaplarını ekle ve kontrol et", "endowment:signature-insight": "İmza içgörüleri kipini görüntüle", "endowment:protocol": "Bir veya daha fazla zincir için protokol verileri sağla", "snap_getPreferences": "<PERSON><PERSON><PERSON> ettiğiniz dil ve fiat para gibi bilgileri görün", "endowment:lifecycle-hooks": "<PERSON><PERSON><PERSON> d<PERSON>ü kancalarını kullan", "endowment:name-lookup": "Alan adı ve adres sorgulamaları sağla", "endowment:page-home": "Özelleştirilmiş bir ekran görüntüle"}}}, "privacy_browser_subheading": "Gizlilik veya tarayıcı verilerini temizle", "analytics_subheading": "<PERSON><PERSON><PERSON><PERSON>", "transactions_subheading": "İşlemler", "network_provider": "<PERSON>ğ sağlayıcısı", "token_nft_ens_subheading": "Token, NFT ve ENS otomatik algılama", "security_check_subheading": "Güvenlik kontrolleri", "symbol_required": "Sembol gerekli.", "blockaid_desc": "<PERSON><PERSON><PERSON>, iş<PERSON> ve imza taleplerini aktif bir şekilde inceleyerek kötü amaçlı aktivite konusunda sizi uyarır.", "security_alerts": "Güvenlik uyarıları", "security_alerts_desc": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, iş<PERSON> ve imza taleplerinizi yerel olarak incelerken gizliliğinizi koruyarak Ethereum Ana Ağındaki kötü amaçlı aktivitelere karşı sizi uyarır. <PERSON><PERSON><PERSON> onay<PERSON>adan önce her zaman gereken özeni kendiniz gösterin. Bu özelliğin tüm kötü amaçlı faaliyetleri algılayacağına dair herhangi bir garanti bulunmamaktadır. Bu özelliği etkinleştirerek sağlayıcının kullanım koşullarını kabul etmiş olursunuz.", "dismiss_smart_account_update_heading": "\"Akıllı Hesaba Geçiş Yap\" önerisini yok say", "dismiss_smart_account_update_desc": "Artık herhangi bir hesapta \"Akıllı Hesaba Geçiş Yap\" önerisini görmemek için bunu açın. Akıllı hesaplar daha hızlı işlemlerin, daha düşük ağ ücretlerinin ve bunların ödemesinde daha fazla esnekliğin kilidini açar.", "use_smart_account_heading": "Akıllı hesap kullan", "use_smart_account_desc": "<PERSON><PERSON> h<PERSON><PERSON>, daha düşük ağ ücretleri ve bunların ödenmesinde ödeme esnekliği gibi ilgili özellikler kullanılabilir olduğunda akıllı hesaplara geçmek için MetaMask dahilinde oluşturulan hesaplar arasında otomatik geçiş yapmak için bunu açık tutun.", "use_smart_account_learn_more": "Daha fazla bilgi edinin.", "smart_transactions_opt_in_heading": "Akıllı İşlemler", "smart_transactions_opt_in_desc_supported_networks": "Desteklenen ağlarda daha güvenilir ve güvenli işlemler için Akıllı İşlemleri açın.", "smart_transactions_learn_more": "Daha fazla bilgi edinin.", "simulation_details": "Bakiye değişikliklerini tahmin edin", "simulation_details_description": "İşlemleri onaylamadan önce işlemlerin neden olacağı bakiye değişikliklerini tahmin etmek için bunu açın. İşlemlerinizin nihai sonucunu garanti etmez. ", "simulation_details_learn_more": "Daha fazla bilgi edinin.", "aes_crypto_test_form_title": "AES Crypto - Test Biçimi", "aes_crypto_test_form_description": "Bu kısım özel olarak E2E testi için geliştirilmiştir. Bu, uygulamanızda görünüyorsa lütfen MetaMask destek bölümüne bildirin.", "developer_options": {"title": "Geliş<PERSON><PERSON><PERSON>", "generate_trace_test": "Takip Testi Oluştur", "generate_trace_test_desc": "Geliştirici Testi Sentry takibi oluştur."}}, "aes_crypto_test_form": {"generate_random_salt": "Rastgele Tu<PERSON>", "salt_bytes_count": "<PERSON><PERSON> <PERSON>t sayımı", "generate": "Oluştur", "generate_encryption_key": "Şifreden şifreleme anahtarı oluştur", "password": "Şifre", "salt": "Tuz", "encrypt_with_key": "<PERSON><PERSON><PERSON> <PERSON>", "encrypt": "Şifrele", "encryption_key": "Şifreleme <PERSON>rı", "data": "<PERSON><PERSON><PERSON>", "decrypt_with_key": "<PERSON><PERSON><PERSON> ile ş<PERSON>", "decrypt": "<PERSON><PERSON><PERSON>"}, "sdk": {"disconnect_title": "Tüm sitelerin bağlantısı kesilsin mi?", "disconnect_all_info": "Tüm sitelerin bağlantılarını kaldırırsanız tekrar bağlanmak için izin vermeniz gerekecektir.", "disconnect": "Bağlantıyı kes", "disconnect_all": "Tümünün bağlantısını kes", "disconnect_all_accounts": "<PERSON><PERSON>m <PERSON>ın bağlantısını kes", "manage_connections": "Bağlantıları Yönet", "manage": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "loading": "MetaMask'a bağlanılıyor...", "unkown_dapp": "Merkeziyetsiz uygulama adı kullanılamıyor", "unknown": "Bilinmeyen", "no_connections": "Bağlantı yok", "no_connections_desc": "Bir siteye veya bir uygulamaya bağladığınız hesabı burada göreceksiniz."}, "sdk_session_item": {"connected_accounts": "{{accountsLength}} hesap bağlandı."}, "sdk_disconnect_modal": {"disconnect_all": "Tüm sitelerden bağlantı kesilsin mi?", "disconnect_all_desc": "Tüm sitelerden hesaplarınızın bağlantısını keserseniz onları tekrar bağlamak için izin vermeniz gerekecektir.", "disconnect_account": "<PERSON><PERSON><PERSON><PERSON>n bağlantısı kesilsin mi?", "disconnect_all_accounts": "<PERSON><PERSON>m <PERSON>ın bağlantısını kes", "disconnect_all_accounts_desc": "{{dapp}} uygulamasından tüm hesaplarınızın bağlantısını keserseniz onları tekrar bağlamak için izin vermeniz gerekecektir.", "disconnect_account_desc": "{{dapp}} uygulamasından {{account}} bağlantısını keserseniz onu tekrar bağlamak için izin vermeniz gerekecektir.", "disconnect_confirm": "Bağlantıyı kes", "cancel": "İptal"}, "sdk_return_to_app_modal": {"title": "Uygulamaya geri dön", "postNetworkSwitchTitle": "<PERSON><PERSON> başarılı bir ş<PERSON><PERSON>", "description": "Hizmetlerini kullanmaya devam etmek için lütfen uygulamaya geri dö<PERSON>ün."}, "sdk_feedback_modal": {"ok": "<PERSON><PERSON>", "title": "Hesap bağlantısı kurulamadı", "info": "MetaMask'a tekrar bağlanmak için lütfen merkeziyetsiz uygulama üzerinde QR kodunu tarayın"}, "app_information": {"title": "<PERSON><PERSON><PERSON>", "links": "Bağlantılar", "privacy_policy": "Gizlilik Politikası", "terms_of_use": "Kullanım şartları", "attributions": "<PERSON><PERSON><PERSON><PERSON>", "support_center": "Destek Merkezimizi ziyaret edin", "web_site": "Web Sitemizi ziyaret edin", "contact_us": "Bize Ulaşın"}, "reveal_credential": {"seed_phrase_title": "<PERSON><PERSON><PERSON> Kurtarma İfadesini göster", "private_key_title": "<PERSON><PERSON>", "show_private_key": "<PERSON><PERSON>", "private_key_title_for_account": "\"{{accountName}}\" i<PERSON><PERSON> anah<PERSON> gö<PERSON>", "cancel": "İptal", "done": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "seed_phrase_explanation": ["<PERSON>ze ait", "<PERSON><PERSON><PERSON>", " cü<PERSON><PERSON>ı<PERSON><PERSON><PERSON>,", " paranıza ve hesaplarınıza tam erişim verir.\n\n", "<PERSON><PERSON><PERSON><PERSON>,", "emanete dayalı olmayan bir cüzdandır", "<PERSON><PERSON>,", "<PERSON><PERSON><PERSON> Kurtarma İfadenizin sahibi <PERSON>."], "private_key_explanation": "<PERSON><PERSON><PERSON><PERSON> ve gizli bir yere kaydedin.", "private_key_warning": "<PERSON><PERSON>, ha<PERSON><PERSON><PERSON><PERSON><PERSON> seçili hesabın özel anahtarıdır: {{accountName}}. Bu anahtarı hiçbir zaman ifşa etmeyin. Özel anahtara sahip olan her<PERSON>, paran<PERSON>zın tamamını transfer etmek de dahil olmak üzere hesabınızı tam olarak kontrol edebilir.", "seed_phrase_warning_explanation": ["Hiç kimsenin ekranınıza bakmadığından emin olun.", "MetaMask Destek bölümü bunu asla talep etmez."], "private_key_warning_explanation": "Bu anahtarı hiçbir zaman ifşa etmeyin. Özel anahtarınıza sahip olan her<PERSON>, paran<PERSON>zın tamamını transfer etmek de dahil olmak üzere hesabınızı tamamen kontrol edebilir.", "reveal_credential_modal": ["{{credentialName}} bilgiler<PERSON>z ", "hesabınıza ve paranıza tam erişim sunar.\n\n<PERSON><PERSON>u hiç kimseyle paylaşmayın.", "cüzdanınıza ve paranıza tam erişim verir. \n\n<PERSON><PERSON><PERSON> hiç kimseyle paylaşmayın.\n", "MetaMask Destek bölümü bunu talep etmez,", "ama kimlik avcıları talep edebilir."], "seed_phrase": "<PERSON><PERSON><PERSON>arma İfadeniz", "private_key": "<PERSON><PERSON>", "copy_to_clipboard": "<PERSON><PERSON> k<PERSON>", "enter_password": "<PERSON><PERSON> et<PERSON> i<PERSON> girin", "seed_phrase_copied_ios": "Gizli Kurtarma İfadesi geçici olarak panoya kopyalandı\n", "seed_phrase_copied_android": "Gizli Kurtarma İfadesi panoya kopyalandı", "seed_phrase_copied_time": "(1 dakika boyunca saklanır)", "private_key_copied": "Özel anahtar geçici olarak panoya kopyalandı", "private_key_copied_time": "(1 dakika boyunca saklanır)", "private_key_copied_ios": "Özel anahtar geçici olarak panoya kopyalandı\n", "private_key_copied_android": "Özel anahtar panoya kopyalandı\n", "warning_incorrect_password": "<PERSON><PERSON>re <PERSON>ı<PERSON>", "unknown_error": "Hesabınızın kilidi açılamadı. Lütfen tekrar deneyin.", "hardware_error": "<PERSON>u bir donanım cüzdanı he<PERSON>bıdır, özel kilidinizi dışa aktaramazsınız.", "seed_warning": "Bu sizin cüzdanınızın 12 sözcükten oluşan ifadesidir. Bu ifade, cüzdanınızda para gönderme kabiliyeti de dahil olmak üzere tüm şimdiki ve gelecekteki hesaplarınızı kontrol etmek için kullanılabilir. Bu ifadeyi güvenli bir şek<PERSON>, hiç kimseyle PAYLAŞMAYIN.", "text": "METİN", "qr_code": "QR KODU", "hold_to_reveal_credential": "{{credentialName}} bilgisinin gösterilmesi için tut", "reveal_credential": "{{credentialName}} a<PERSON><PERSON><PERSON><PERSON>", "keep_credential_safe": "{{credentialName}} bilgini güvende tut", "srp_abbreviation_text": "GKİ", "srp_text": "<PERSON><PERSON><PERSON>", "private_key_text": "<PERSON><PERSON>", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "learn_more": "Daha Fazlasını Öğren"}, "screenshot_deterrent": {"title": "Güvenlik uyarısı", "description": "Ekran görüntüleri {{credentialName}} takibinin güvenli bir yöntemi değildir. Hesabınızı güvende tutmak için çevrimiçi olarak yedeklenmeyen bir yerde saklayın.", "srp_text": "<PERSON><PERSON><PERSON>", "priv_key_text": "<PERSON><PERSON>"}, "password_reset": {"password_title": "Şifre", "password_desc": "Cihazınızda MetaMask uygulamasının kilidini açmak için güçlü bir şifre seçin. Bu şifreyi kaybettiğiniz takdirde cüzdanınızı yeniden içe aktarmak için Gizli Kurtarma İfadenize ihtiyacınız olacaktır.", "password_learn_more": "Daha fazlasını öğren.", "change_password": "<PERSON><PERSON><PERSON><PERSON>", "password_hint": "<PERSON><PERSON><PERSON>"}, "fund_actionmenu": {"deposit": "Para Yatır", "deposit_description": "Düşük ücretli banka veya kart transferi", "buy": "Al", "buy_description": "Belirli bir token'ı almak için iyi", "sell": "Çek", "sell_description": "Nakit karşılığı kripto sat"}, "asset_overview": {"send_button": "<PERSON><PERSON><PERSON>", "buy_button": "Satın Al", "token_marketplace": "Token pazar yeri", "sell_button": "Çek", "receive_button": "Al", "portfolio_button": "Portfolio", "deposit_button": "Para Yatır", "earn_button": "Kazan", "perps_button": "Sürekli Vadeli İşlem Sözleşmeleri", "add_collectible_button": "<PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>", "swap": "Kaydır", "bridge": "Köprü", "earn": "Kazan", "disabled_button": {"buy": "Bu hesap için satın alma desteklenmiyor", "sell": "<PERSON>u hesap i<PERSON><PERSON> satma des<PERSON>", "swap": "<PERSON>u hesap i<PERSON>in swap desteklenmiyor", "deposit": "Bu hesap için para yatırma işlemi desteklenmiyor", "bridge": "<PERSON>u hesa<PERSON> i<PERSON><PERSON> k<PERSON> desteklenmiyor", "send": "<PERSON>u hesa<PERSON> i<PERSON><PERSON> g<PERSON>klenmiyor", "action": "Bu hesa<PERSON> i<PERSON>in bu işlem desteklenmiyor", "earn": "Kazanma bu hesa<PERSON> i<PERSON><PERSON>", "perps": "Sürekli vadeli işlem sözleşmeleri ile işlem bu hesap için desteklenmiyor"}, "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "totalSupply": "<PERSON>lam Arz", "address": "<PERSON><PERSON>", "were_unable": "Bakiyenizi", "balance": "yükleyemedik. <PERSON><PERSON><PERSON>", "troubleshooting_missing": "eksik bakiye sorununu giderme hakkındaki", "for_help": "ma<PERSON><PERSON> bakın.", "troubleshoot": "<PERSON><PERSON>", "deposit_description": "Düşük ücretli banka veya kart transferi", "buy_description": "Belirli bir token'ı almak için iyi", "sell_description": "Nakit ka<PERSON>şılığında kripto sat", "swap_description": "Tokenler arasında swap gerçekleştirin", "bridge_description": "Farklı ağlar arasında token transfer et", "send_description": "<PERSON><PERSON><PERSON><PERSON><PERSON> hesaba k<PERSON>", "receive_description": "<PERSON><PERSON><PERSON>", "earn_description": "Token'lerinizden ödül kazanın", "perps_description": "Sürekli vadeli işlem sözleşmeleri ile işlem yap", "chart_time_period": {"1d": "<PERSON><PERSON><PERSON><PERSON>", "7d": "Son 7 gün", "1w": "Geçen hafta", "1m": "Geçen ay", "3m": "Son 3 ay", "1y": "Son bir yıl", "3y": "Son 3 yıl", "all": "Tümü"}, "chart_time_period_navigation": {"1d": "1G", "7d": "7G", "1w": "1H", "1m": "1A", "3m": "3A", "1y": "1Y", "3y": "3Y", "all": "Tümü"}, "no_chart_data": {"title": "<PERSON>ik verileri yok", "description": "Bu <PERSON> için herhangi bir veri alama<PERSON>k"}, "your_balance": "Bakiyeniz", "unable_to_load_balance": "Bakiyeniz yüklenemedi", "about": "Hakkında", "about_content_display": {"show_more": "<PERSON><PERSON> faz<PERSON>", "show_less": "<PERSON><PERSON> a<PERSON> g<PERSON>"}, "activity": "{{symbol}} aktivitesi", "disclaimer": "Piyasa verileri CoinGecko dahil bir veya daha fazla üçüncü taraf veri kaynakları tarafından sunulur. Söz konusu üçüncü taraf içerik sadece bilgi vermek amacıyla sunulur ve belirli bir varlığı satın alma, satma ya da kullanma tavsiyesi olarak görülmemelidir. MetaMask bu içeriğin herhangi bir belirli amaçla kullanımını önermez ve bunun doğruluğundan sorumlu değildir."}, "account_details": {"title": "<PERSON><PERSON><PERSON>", "share_account": "Paylaş", "view_account": "Hesabı Etherscan üzerinde görüntüle", "show_private_key": "<PERSON><PERSON>", "account_copied_to_clipboard": "Genel adres panoya kopyalandı", "share_public_key": "Halka açık anahtarım paylaşılıyor: "}, "enable_nft-auto-detection": {"title": "NFT otomatik algılamayı etkinleştir", "description": "MetaMask'in otomatik algılama ile NFT'lerinizi algılamasına ve göstermesine izin verin. Şunları yapabileceksiniz:", "immediateAccess": "NFT'lerinize anında er<PERSON><PERSON>", "navigate": "Dijital varlıklarınızda zahmetsizce gezinin", "dive": "Doğrudan NFT'lerinizi kullanmaya başlayın", "allow": "<PERSON><PERSON> V<PERSON>", "notRightNow": "<PERSON><PERSON><PERSON>"}, "detected_tokens": {"title": "{{tokenCount}} yeni token bulundu", "title_plural": "{{tokenCount}} yeni token bulundu", "import_cta": "({{tokenCount}}) içe aktar", "hide_cta": "Tümünü <PERSON>", "token_address": "<PERSON><PERSON>: ", "token_lists": "Token listeleri: {{listNames}}", "token_more": " + {{remainingListCount}} daha", "confirm": {"cancel_cta": "İptal", "confirm_cta": "<PERSON><PERSON><PERSON>", "import": {"title": "Seçilen tokenleri içe aktar?", "desc": "Cüzdanınızda sadece seçtiğiniz tokenler gösterilecek. Dilediğiniz zaman onları arayarak gizli tokenleri içe aktarabilirsiniz."}, "hide": {"title": "Emin misiniz?", "desc": "Gizlediğiniz tokenler cüzdanınızda gösterilmez. <PERSON><PERSON>k, yine de onları bulup ekleyebilirsiniz."}}, "address_copied_to_clipboard": "Token adresi hafıza panosuna kopyalandı"}, "qr_scanner": {"invalid_qr_code_title": "QR Kodu geçersiz", "invalid_qr_code_message": "Taramaya çalıştığınız QR kodu geçerli değil.", "allow_camera_dialog_title": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON> izin ver", "allow_camera_dialog_message": "QR kodlarını taramak için izninize ihtiyacımız var", "scanning": "taranıyor...", "ok": "<PERSON><PERSON>", "continue": "<PERSON><PERSON> et", "cancel": "İptal", "error": "<PERSON><PERSON>", "attempting_to_scan_with_wallet_locked": "Görünüşe göre bir QR kodunu taramaya çalışıyorsunuz, bunu kullanmak için cüzdanınızın kilidini açmanız gerekecek.", "attempting_sync_from_wallet_error": "Görünüşe göre uzantı ile senkronize etmeye çalışıyorsunuz. Bunu yapabilmek için mevcut cüzdanınızı silmeniz gerekecek. \n\nUygulamayı sildiğinizde veya uygulamanın yeni bir sürümünü tekrar yüklediğinizde \"MetaMask Uzantısı ile Senkronize Et\" seçeneğini seçin. Önemli! Cüzdanınızı silmeden önce Gizli Kurtarma İfadenizi yedeklediğinizden emin olun.", "not_allowed_error_title": "<PERSON><PERSON><PERSON>", "not_allowed_error_desc": "Bir QR kodunu taramak için cihazınızın ayarlar menüsünden MetaMask'e kamera erişimi vermeniz gerekecektir.", "unrecognized_address_qr_code_title": "Tanınmayan QR kodu", "unrecognized_address_qr_code_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu QR kodu bir hesap adresi ya da bir iletişim adresiyle ilişkilendirilmemiş.", "url_redirection_alert_title": "Harici bir bağlantıyı ziyaret etmek üzeresiniz", "url_redirection_alert_desc": "Bağlantılar insanları dolandırmak ya da kimlik avı için kullanılabilir bu nedenle sadece güendiğiniz web sitelerini ziyaret ettiğinizden emin olun.", "label": "Bir QR kodunu tara", "open_settings": "<PERSON><PERSON><PERSON>", "camera_not_available": "<PERSON><PERSON><PERSON> mev<PERSON> de<PERSON>"}, "action_view": {"cancel": "İptal", "confirm": "<PERSON><PERSON><PERSON>"}, "gas_fee_token_modal": {"title": "Bir token seç", "title_pay_eth": "ETH ile öde", "native_toggle_wallet": "Cüzdanınızdaki bakiyeyi kullanarak ağ ücretini ödeyin.", "list_balance": "Bak:", "insufficient_balance": "<PERSON><PERSON><PERSON>", "native_toggle_metamask": "<PERSON><PERSON><PERSON><PERSON>, bu <PERSON><PERSON><PERSON><PERSON> ta<PERSON> i<PERSON>in baki<PERSON>yi destekliyor.", "title_pay_with_other_tokens": "<PERSON><PERSON><PERSON>'larla <PERSON>de"}, "transaction": {"transaction_id": "İşlem Kimliği", "alert": "UYARI", "amount": "<PERSON><PERSON>", "details": "Ayrıntılar", "next": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "speedup": "Hızlandır", "sign_with_keystone": "Donanım cüzdanı ile imzala", "sign_with_ledger": "Ledger ile oturum aç", "from": "<PERSON><PERSON>", "gas_fee": "Gaz ücreti", "gas_fee_fast": "HIZLI", "gas_fee_average": "ORTA", "gas_fee_slow": "YAVAŞ", "hex_data": "On Altılı Veriler", "custom_nonce": "<PERSON><PERSON>", "custom_nonce_tooltip": "B<PERSON>, bir hesabın işlem numarasıdır. İlk işlem için nonce 0 olup sıralı düzende artar.", "this_is_an_advanced": "<PERSON><PERSON>, bekleyen işlemleri iptal etmek veya hızlandırmak için kullanılan gelişmiş bir özelliktir.", "current_suggested_nonce": "Geçerli önerilen nonce:", "edit_transaction_nonce": "İşlem nonce numarasını düzenle", "think_of_the_nonce": "Nonce numarasını bir hesabın işlem numarası olarak düşünün. Her hesabın nonce numarası ilk işlem için 0 ile başlar ve sıralı düzende devam eder.", "nonce_warning": "Uyarı: <PERSON><PERSON> ed<PERSON>z gelecekteki işlemlerde sorunlarla karşılaşabilirsiniz. Dikkatli kullanın.", "review_details": "AYRINTILAR", "review_data": "VERİLER", "data": "<PERSON><PERSON><PERSON>", "data_description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ilişkili veriler", "review_function_type": "FONKSİYON TÜRÜ", "review_function": "Fonksiyon", "review_hex_data": "On altılı veriler", "insufficient": "Para yetersiz", "insufficient_amount": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ta<PERSON> için {{amount}}{{tokenSymbol}} daha gerekiyor.", "buy_more_eth": "Daha fazla ETH satın al", "buy_more": "Daha Fazla Satın Al", "more_to_continue": "<PERSON><PERSON> et<PERSON>k i<PERSON> da<PERSON> fazla {{ticker}} gerek<PERSON>", "you_can_also_send_funds": "Başka bir hesaptan da para gönderebilirsiniz.", "token_marketplace": "Token pazar yeri", "token_Marketplace": "<PERSON><PERSON>", "go_to_faucet": "Musluğa git", "get_ether": "{{networkName}} a<PERSON><PERSON> <PERSON><PERSON><PERSON> al.", "insufficient_tokens": "{{token}} yetersiz", "invalid_address": "Geçersiz adres", "invalid_from_address": "Gönderen adresi geçersiz", "invalid_amount": "Geçersiz miktar", "invalid_gas": "Geçersiz gaz miktarı", "invalid_gas_price": "Geçersiz gaz fiyatı", "high_gas_price": "Gaz ücretiniz gereksiz bir şekilde yüksek ayarlanmış olabilir ({{currentGasPrice}}). Miktarı düşürmeyi göz önünde bulundurun.", "low_gas_price": "Gaz fiyatı aşırı düşük", "invalid_collectible_ownership": "Bu koleksiyona <PERSON>ğ<PERSON>iniz", "known_asset_contract": "Bilinen varlık sözleşme adresi", "max": "Ma<PERSON>.", "recipient_address": "Alıcı Adresi", "required": "<PERSON><PERSON><PERSON><PERSON>", "to": "<PERSON><PERSON>", "total": "Toplam", "loading": "Yükleniyor...", "conversion_not_available": "Dönüştürme oranı mevcut değil", "value_not_available": "<PERSON><PERSON><PERSON>", "rate_not_available": "Dönüştürme mevcut değil", "optional": "İsteğe bağlı", "no_address_for_ens": "ENS adı için adres yok", "lets_try": "<PERSON><PERSON>, den<PERSON><PERSON>", "approve_warning": "Bu e<PERSON>mi onaylayarak bu sözleşmenin şu miktara kadar harcama yapmasına izin verirsiniz:", "cancel_tx_title": "İptal etmeyi dene?", "cancel_tx_message": "Bu denemeyi göndermek ilk işlemin iptalini garanti etmez. İptal denemesi başarılı olursa sizden yukarıdaki işlem ücreti alınır.", "speedup_tx_title": "H<PERSON>zlandırmayı dene?", "speedup_tx_message": "Bu denemeyi göndermek ilk işlemin hızlandırılmasını garanti etmez. Hızlandırma denemesi başarılı olursa sizden yukarıdaki işlem ücreti alınır.", "nevermind": "Fark etmez", "edit_network_fee": "Gaz ücretini düzenle", "edit_priority": "Önceliği düzenle", "gas_cancel_fee": "Gaz iptal ücreti", "gas_speedup_fee": "Gaz hızlandırma ücreti", "use_max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set_gas": "<PERSON><PERSON><PERSON>", "cancel_gas": "İptal", "transaction_fee_estimated": "<PERSON><PERSON><PERSON>az <PERSON>", "transaction_fee": "Gaz ücreti", "transaction_fee_less": "Ücret yok", "total_amount": "<PERSON>lam tutar", "view_data": "Verileri Görüntüle", "adjust_transaction_fee": "İşlem ücretini ayarla", "could_not_resolve_ens": "ENS <PERSON>", "asset": "Varlık", "balance": "Bakiye", "token_id": "<PERSON><PERSON>", "not_enough_for_gas": "Hesabınızda işlem ücretlerini ödemek için 0 {{ticker}} var.", "send": "<PERSON><PERSON><PERSON>", "confirm_with_qr_hardware": "Donanım cüzdanı ile onayla", "confirm_with_ledger_hardware": "Ledger ile onayla", "confirmed": "Onaylandı", "pending": "Bekliyor", "submitted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Başarısız Oldu", "cancelled": "İptal Edildi", "signed": "Bekliyor", "tokenContractAddressWarning_1": "Bu adres bir ", "tokenContractAddressWarning_2": "token sözleşme adresidir", "tokenContractAddressWarning_3": ". Bu adrese gönderdiğiniz tokenleri kaybedersiniz.", "smartContractAddressWarning": "Bu adres akıllı sözleşme adresidir. Lütfen bu adresin amacını anladığınızdan emin olun; aksi takdirde paranızı kaybetme riskini alırsınız.", "continueError": "Riskleri an<PERSON>ı<PERSON>, devam et", "gas_education_title_ethereum": "Ethereum gaz ücretleri", "gas_education_title": "Gaz ücretleri", "gas_education_1": "Gaz ücretleri", "gas_education_2_ethereum": "Ethereum ağında işlem gerçekleştiren kripto madencilerine ödenir.", "gas_education_2": "ağda işlem gerçekleştiren kripto madencilerine ödenir.", "gas_education_3": "MetaMask gaz ücretlerinden kâr elde etmez.", "gas_education_4": "Gaz ücretleri ağ trafiğine ve işlem karmaşıklığına göre dalgalanır.", "gas_education_learn_more": "Gaz ücretleri hakkında daha fazla bilgi alın", "confusable_title": "Alıcı adresini kontrol edin", "confusable_msg": "ENS adında karıştırılabilir bir karakter algıladık. Olası bir dolandırıcılığı önlemek için ENS adını kontrol edin.", "similar_to": "<PERSON><PERSON> ben<PERSON>:", "contains_zero_width": "sı<PERSON><PERSON>r genişlikli karakter içeriyor", "dapp_suggested_gas": "Bu gaz ücreti %{origin} tarafından önerildi. Eski gaz tahminini kullanıyor ve bu yanlış olabilir. Ancak, bu gaz ücretinin düzenlenmesi işleminizde bir soruna neden olabilir. Sorularınız olursa lütfen %{origin} adresine ulaşın.", "dapp_suggested_eip1559_gas": "Bu gaz ücreti %{origin} tarafından önerildi. Bunu geçersiz kılmak işleminizde bir soruna neden olabilir. Sorularınız olursa lütfen %{origin} adresine ul<PERSON>ı<PERSON>.", "address_invalid": "Alıcı adresi geçersiz.", "ens_not_found": "<PERSON><PERSON> <PERSON><PERSON> i<PERSON> hi<PERSON> ad<PERSON> be<PERSON>nmemiş.", "unknown_qr_code": "Geçersiz OR kodu. Lütfen tekrar deneyin.", "invalid_qr_code_sync": "Geçersiz QR kodu. Lütfen donanım cüzdanının senkronizasyon QR kodunu tarayın.", "no_camera_permission": "Kamera izni yok. Lütfen izin verin ve tekrar deneyin", "invalid_qr_code_sign": "Geçersiz QR kodu. Lütfen donanımınızı kontrol edin ve tekrar deneyin.", "no_camera_permission_android": "Devam etmek için kameranıza MetaMask erişim izni vermeniz gerekiyor. Ayrıca sistem ayarlarınızı değiştirmeniz gerekebilir.", "mismatched_qr_request_id": "Uyumsuz işlem verisi. Lütfen aşağıdaki QR koduyla imzalamak için donanım cüzdanınızı kullanın ve \"İmzayı Al\" tuşuna tıklayın.", "fiat_conversion_not_available": "Fiat para dönüştürmeleri şu anda kullanılmıyor", "hex_data_copied": "On altılı veriler panoya kopyalandı", "invalid_recipient": "Alıcı geçersiz", "invalid_recipient_description": "Adresi kontrol edin ve geçerli olduğundan emin olun", "swap_tokens": "Token swap i<PERSON><PERSON>i ya<PERSON>ın", "fromWithColon": "Gönderen:"}, "custom_gas": {"total": "Toplam", "advanced_options": "Gelişmiş", "basic_options": "Temel", "hide_advanced_options": "Gelişmişi gizle", "gas_limit": "Gaz Limiti:", "gas_price": "Gaz Fiyatı: (GWEI)", "save": "<PERSON><PERSON>", "warning_gas_limit": "Gaz limiti 20999 üzerinde olmalıdır", "warning_gas_limit_estimated": "Tahmini gaz limiti {{gas}}, minimum değer olarak bunu kullanın", "cost_explanation": "Gaz ücreti Ethereum ağında işleminizin gerçekleştirilme maliyetini kapsar. MetaMask bu ücretten kâr elde etmez. Ücret ne kadar yüksek olursa işleminizin gerçekleşme şansı o kadar yüksek olur."}, "spend_limit_edition": {"save": "<PERSON><PERSON>", "title": "İzni düzenle", "spend_limit": "Harcama limiti izni", "allow": "Aşağıdaki tutara kadar para çekmeye ve harcamaya", "allow_explanation": "izin ver:", "proposed": "Önerilen onay limiti", "requested_by": "<PERSON>rcama limiti talep eden", "custom_spend_limit": "<PERSON><PERSON><PERSON><PERSON>", "max_spend_limit": "<PERSON><PERSON><PERSON><PERSON> bir harcama limiti girin", "minimum": "Minimum 1.00 {{tokenSymbol}}", "cancel": "İptal", "approve": "<PERSON><PERSON><PERSON>", "allow_to_access": "<PERSON><PERSON><PERSON>im izni verin:", "allow_to_address_access": "<PERSON>u adrese erişim izni verin:", "allow_to_transfer_all": "Şun<PERSON>ın tümüne erişim ve transfer izni verin:", "spend_cap": "<PERSON><PERSON><PERSON> i<PERSON>in harcama üst limiti:", "token": "token", "nft": "NFT", "you_trust_this_site": "İzin vererek aşağıdaki üçüncü tarafın bakiyenize erişim sağlamasına izin verirsiniz.", "you_trust_this_third_party": "Üçüncü bir tara<PERSON>ın, siz bu erişimi iptal edene kadar başka bildirim yapılmaksızın NFT'lerinize erişim sağlamasına ve NFT'lerinizi transfer edebilmesine izin verir.", "you_trust_this_address": "Bu adrese güveniyor musunuz? Bu izni vererek bu adresin paranıza erişim sağlamasına izin veriyorsunuz.", "edit_permission": "İzni düzenle", "edit": "<PERSON><PERSON><PERSON><PERSON>", "transaction_fee_explanation": "Bu izinle ilişkili bir işlem ücreti.", "view_details": "Ayrıntıları görüntüle", "view_transaction_details": "İşlem bilgilerini görüntüle", "view_data": "Verileri Görüntüle", "transaction_details": "İşlem Bilgileri", "site_url": "Site URL adresi", "permission_request": "<PERSON><PERSON>bi", "details_explanation": "{{host}} er<PERSON><PERSON><PERSON> ve bu hesaptan bu maksimum kadar harcama ya<PERSON>.", "amount": "Tutar:", "allowance": "Ödenek:", "to": "<PERSON><PERSON>:", "contract": "Sözleşme ({{address}})", "contract_name": "Sözleşme adı:", "contract_address": "S<PERSON>zleşme adresi:", "invalid_contract_address": " Contract address is invalid. Enter a new one", "contract_allowance": "Ödenek:", "data": "<PERSON><PERSON><PERSON>", "function_approve": "Fonksiyon: <PERSON><PERSON><PERSON>", "function": "Fonksiyon", "close": "Ka<PERSON><PERSON>", "all_set": "Her şey hazır!", "all_set_desc": "Bu site için izinleri başarılı bir şekilde ayarladın.", "must_be_at_least": "En az {{allowance}} olmalıdır", "access_up_to": "<PERSON><PERSON><PERSON><PERSON>:", "spending_cap": "Harcama üst limiti:", "approve_asset": "Varlığı onayla:"}, "browser": {"title": "Tarayıcı", "reload": "<PERSON><PERSON><PERSON>", "share": "Paylaş", "bookmark": "<PERSON><PERSON>", "add_to_favorites": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "cancel": "İptal", "go_back": "<PERSON><PERSON> git", "go_forward": "İleri git", "home": "<PERSON>", "close": "Ka<PERSON><PERSON>", "open_in_browser": "Tarayıcıda aç", "change_url": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "switch_network": "<PERSON><PERSON>", "dapp_browser": "MERKEZİYETSİZ UYGULAMA TARAYICISI", "dapp_browser_message": "MetaMask, merkeziyetsiz web için cüzdanınız ve tarayıcınızdır. Bir bakın!", "featured_dapps": "ÖNE ÇIKAN MERKEZİYETSİZ UYGULAMALAR", "my_favorites": "FAVORİLERİM", "search": "Ara veya URL adresi gir", "welcome": "Hoş Geldiniz!", "remove": "Kaldır", "new_tab": "<PERSON><PERSON> sekme", "tabs_close_all": "Tümünü <PERSON>", "tabs_done": "<PERSON><PERSON>", "no_tabs_title": "Açık Sekme Yok", "no_tabs_desc": "Merkeziyetsiz webde gezinmek için yeni bir sekme e<PERSON>in", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "max_tabs_title": "<PERSON><PERSON><PERSON><PERSON> sekmeye ulaşıldı", "max_tabs_desc": "Şu anda aynı anda yalnızca 5 açık sekmeyi destekliyoruz. Yenilerini eklemeden önce lütfen mevcut sekmeleri kapatın.", "failed_to_resolve_ens_name": "Bu ENS adını çözümleyemedik", "remove_bookmark_title": "<PERSON>av<PERSON><PERSON> kaldır", "remove_bookmark_msg": "Bu siteyi gerçekten favorilerinizden kaldırmak istiyor musunuz?", "yes": "<PERSON><PERSON>", "undefined_account": "Tanıms<PERSON>z he<PERSON>p", "close_tab": "Sekmeyi kapat", "switch_tab": "<PERSON>k<PERSON><PERSON>", "protocol_alert_options": {"ignore": "<PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON> V<PERSON>"}, "protocol_alerts": {"tel": "Bu web sitesinin otomatik olarak telefon araması yapması engellendi", "mailto": "Bu web sitesinin otomatik olarak e-posta oluşturması engellendi.", "generic": "Bu web sitesinin otomatik olarak harici bir uygulamayı açması engellendi"}, "ipfs_gateway_off_title": "IPFS ağ geçidi kapalı", "ipfs_gateway_off_content": "Bu siteyi görmek için Gizlilik ve Güvenlik Ayarlarında IPFS ağ geçidini açın."}, "backup_alert": {"title": "Cüzdanınızı koruyun", "left_button": "<PERSON><PERSON> sonra hatırlat", "right_button": "Cüzdanı koru"}, "add_favorite": {"title": "<PERSON><PERSON><PERSON>", "title_label": "Ad<PERSON>", "url_label": "<PERSON><PERSON> adresi", "add_button": "<PERSON><PERSON>", "cancel_button": "İptal"}, "approval": {"title": "İşlemi Onayla"}, "approve": {"title": "<PERSON><PERSON><PERSON>", "deeplink": "<PERSON><PERSON>", "qr_code": "QR KODU"}, "transactions": {"tx_review_confirm": "<PERSON><PERSON><PERSON>", "tx_review_transfer": "Transfer Et", "tx_review_contract_deployment": "<PERSON><PERSON>zleşme <PERSON>me", "tx_review_transfer_from": "Şuradan Transfer Et", "tx_review_unknown": "Bilinmeyen Yöntem", "tx_review_approve": "<PERSON><PERSON><PERSON>", "tx_review_increase_allowance": "Ödeneği Artır", "tx_review_set_approval_for_all": "<PERSON><PERSON><PERSON><PERSON>ne <PERSON> V<PERSON>", "tx_review_staking_claim": "<PERSON>ake <PERSON>", "tx_review_staking_deposit": "Stake Para Yatırma", "tx_review_staking_unstake": "Unstake Et", "tx_review_lending_deposit": "Borç Verme Para Yatırma İşlemi", "tx_review_lending_withdraw": "Borç Verme Para Çekme İşlemi", "tx_review_perps_deposit": "Fonlanmış Sürekli Vadeli İşlem Sözleşmeleri hesabı", "sent_ether": "<PERSON><PERSON>", "self_sent_ether": "<PERSON><PERSON>", "received_ether": "<PERSON><PERSON>", "sent_dai": "DAI Gönderdi", "self_sent_dai": "Sana DAI gönderdi", "received_dai": "DAI aldı", "sent_tokens": "<PERSON><PERSON>", "ether": "<PERSON><PERSON>", "sent_unit": "{{unit}} <PERSON><PERSON><PERSON><PERSON>", "self_sent_unit": "Sana {{unit}} <PERSON><PERSON><PERSON><PERSON>", "received_unit": "{{unit}} Aldı", "sent_collectible": "Koleksiyon Gönderdi", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "received": "Alındı", "receive": "Al", "swap": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "redeposit": "Te<PERSON>r para yatır", "interaction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contract_deploy": "<PERSON><PERSON>zleşme <PERSON>me", "to_contract": "<PERSON><PERSON>", "tx_details_free": "Ücretsiz", "tx_details_not_available": "<PERSON><PERSON><PERSON>", "smart_contract_interaction": "Akıllı sözleşme etkileşimi", "swaps_transaction": "<PERSON><PERSON><PERSON>", "bridge_transaction": "Köprü", "approve": "<PERSON><PERSON><PERSON>", "increase_allowance": "Ödeneği Artır", "set_approval_for_all": "<PERSON><PERSON><PERSON><PERSON>ne <PERSON> V<PERSON>", "hash": "Hash", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "details": "Ayrıntılar", "amount": "<PERSON><PERSON>", "fee": {"transaction_fee_in_ether": "İşlem Ücreti", "transaction_fee_in_usd": "İşlem Ücreti (USD)"}, "gas_used": "Kullanılan Gaz (Birim)", "gas_limit": "<PERSON>az <PERSON> (Birimler)", "gas_price": "Gaz Fiyatı (GWEI)", "base_fee": "<PERSON><PERSON> (GEWI)", "priority_fee": "<PERSON><PERSON><PERSON> (GWEI)", "multichain_priority_fee": "Öncelik Ücreti", "max_fee": "Gaz Başına Maks. Ücret", "total": "Toplam", "view_on": "Şurada görüntüle:", "view_on_etherscan": "Etherscan üzerinde görüntüle", "view_full_history_on": "Şurada tüm geçmişi görüntüle:", "view_full_history_on_etherscan": "Etherscan üzerinde tüm geçmişi görüntüle", "hash_copied_to_clipboard": "İşlem hash değeri panoya kopyalandı", "address_copied_to_clipboard": "Adres panoya kopyalandı", "transaction_error": "İşlem hatası", "address_to_placeholder": "<PERSON>, genel adres (0x) veya ENS", "address_from_balance": "Bakiye:", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "nonce": "<PERSON><PERSON>", "from_device_label": "bu cih<PERSON>dan", "import_wallet_row": "<PERSON><PERSON><PERSON> bu cihaza e<PERSON>", "import_wallet_label": "<PERSON><PERSON><PERSON>", "import_wallet_tip": "Gelecekte bu cihazdan yapılan tüm işlemlerde zaman damgasının yanında \"bu cihazdan\" etiketi bulunacaktır. Hesabın eklenmesinden önceki tarihlerde yapılan işlemler için bu ge<PERSON><PERSON>ş, hangi giden işlemlerin bu cihazdan oluşturulduğunu göstermeyecektir.", "sign_title_scan": "Tara ", "sign_title_device": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>ızla", "sign_description_1": "Donanım cüzdanınızla imzaladıktan <PERSON>ra", "sign_description_2": "İmzayı Al tuşuna tıklayın", "sign_get_signature": "İmza Al", "transaction_id": "İşlem Kimliği", "network": "Ağ", "request_from": "<PERSON><PERSON>", "network_fee": "<PERSON><PERSON>", "network_fee_tooltip": "Ağ üzerinde işlemi gerçekleştirmek için ödenen tutar.", "smart_account_upgrade": "Akıllı hesaba yükselt", "smart_account_downgrade": "Standart hesaba geç<PERSON><PERSON> yap", "batched_transactions": "Toplu işlemler", "gas_modal": {"edit_network_fee": "<PERSON><PERSON> ü<PERSON>i dü<PERSON>", "advanced_gas_fee": "Gelişmiş ağ ücreti", "site_suggested": "Önerilen site", "advanced": "Gelişmiş", "low": "Düşük", "medium": "<PERSON><PERSON><PERSON>", "high": "Agresif", "network_suggested": "Önerilen ağ", "gas_limit": "Gaz Limiti", "save": "<PERSON><PERSON>", "max_base_fee": "Maks. Baz Ücret", "priority_fee": "Öncelik Ücreti", "current_priority_fee": "Geç<PERSON>li: {{min}} - {{max}} GWEI", "historical_priority_fee": "12 sa: {{min}} - {{max}} GWEI", "estimated_base_fee": "Geçerli: {{value}} GWEI", "historical_base_fee": "12 sa: {{min}} - {{max}} GWEI", "gas_price": "Gaz fiyatı", "field_required": "{{field}} gereklidir", "max_base_fee_required": "Maks. baz ücret gereklidir", "gas_price_required": "Gaz fiyatı gereklidir", "priority_fee_required": "Öncelik ücreti gereklidir", "gas_limit_required": "Gaz limiti gereklidir", "only_numbers_allowed": "Yalnızca sayılara izin verilir", "negative_values_not_allowed": "<PERSON>egatif <PERSON> izin verilmez", "max_base_fee_must_be_greater_than_priority_fee": "Maks. baz ücret öncelik ücretinden büyük olmalıdır", "gas_limit_too_low": "Gaz limiti 21000 üzerinde olmalıdır", "priority_fee_too_high": "Öncelik ücreti maks. baz ücretten küçük olmalıdır", "no_zero_value": "{{field}}, 0'dan <PERSON><PERSON><PERSON><PERSON><PERSON>lıdı<PERSON>", "speed": "Hız", "only_integers_allowed": "Yalnızca tam sayılara izin verilir"}}, "smart_transactions": {"status_submitting_header": "İşleminiz gönderiliyor", "status_submitting_description": "<PERSON><PERSON><PERSON> {{timeLeft}}", "status_success_header": "İşleminiz ta<PERSON>landı", "status_submitting_past_estimated_deadline_header": "Beklediğiniz için özür dileriz", "status_submitting_past_estimated_deadline_description": "İşleminiz {{timeLeft}} içinde sonuçlanmazsa iptal edilir ve sizden gaz ücreti alınmaz.", "status_cancelled_header": "İşleminiz iptal edildi", "status_cancelled_description": "İşleminiz tamamlanamadığından gereksiz gaz ücreti ödemenizi önlemek amacıyla iptal edildi.", "status_failed_header": "İşleminiz başarısız oldu", "status_failed_description": "Ani piyasa değişimleri başarısızlıklara sebep olabilir. <PERSON><PERSON> devam ederse MetaMask müşteri destek bölümüne ulaşın.", "view_transaction": "İşlemi görü<PERSON>üle", "view_activity": "Aktiviteyi görü<PERSON>le", "return_to_dapp": "{{dappName}} uygulamasına geri dön", "try_again": "<PERSON><PERSON><PERSON>", "create_new": "Yeni {{txType}} oluştur", "swap": "swap", "send": "<PERSON><PERSON><PERSON>"}, "address_book": {"recents": "Yakın Zamanda Gerçekleşenler", "save": "<PERSON><PERSON>", "delete_contact": "<PERSON><PERSON><PERSON><PERSON> sil", "delete": "Sil", "cancel": "İptal", "add_to_address_book": "<PERSON><PERSON> defterine ekle", "enter_an_alias": "Rumuz gir", "add_this_address": "Bu adresi adres defterine ekle", "next": "<PERSON><PERSON><PERSON>", "enter_an_alias_placeholder": "ör. Vitalik B.", "add_contact_title": "<PERSON><PERSON><PERSON><PERSON>", "add_contact": "<PERSON><PERSON><PERSON><PERSON>", "edit_contact_title": "<PERSON><PERSON><PERSON><PERSON>", "edit_contact": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "address_already_saved": "<PERSON><PERSON>i zaten kaydedil<PERSON>ş", "address": "<PERSON><PERSON>", "name": "Ad<PERSON>", "nickname": "Ad<PERSON>", "add_input_placeholder": "Genel adres (0x) veya ENS", "between_account": "Hesap<PERSON>ım a<PERSON>ında transfer", "others": "Di<PERSON><PERSON><PERSON><PERSON>", "memo": "Memo", "network": "Ağ"}, "duplicate_address": {"title": "Bu yinelenen bir adres", "body": "Ki<PERSON><PERSON> listeniz bu adresi birden fazla zincirde gösteriyor. Para göndermeden önce doğru kişiyi seçtiğinizden emin olun.", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "transaction_submitted": {"title": "İşlem Gönderildi", "your_tx_hash_is": "İşlem hash değeriniz:", "view_on_etherscan": "Etherscan üzerinde görüntüle"}, "networks": {"title": "<PERSON><PERSON><PERSON>", "other_networks": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "status_ok": "T<PERSON>m Sistemler Faal", "status_not_ok": "Ağda bazı sorunlar yaşanıyor", "want_to_add_network": "Bu ağı eklemek istiyor musunuz?", "add_custom_network": "<PERSON>zel ağ ekle", "network_infomation": "Bu, bu ağın MetaMask dahilinde kullanılmasına izin verir", "network_endorsement": "MetaMask kişisel ağları veya onların güvenliğini ciro etmez.", "learn_about": "Dolandırıcılıklar ve ağ güvenlik riskleri", "network_risk": "hakkında bilgi alın", "network_display_name": "G<PERSON><PERSON><PERSON><PERSON><PERSON> adı", "network_chain_id": "<PERSON><PERSON><PERSON><PERSON>", "network_rpc_url": "Ağ URL adresi", "network_rpc_url_label": "Ağ RPC URL adresi", "network_rpc_url_warning_punycode": "Saldırganlar bazen site adresinde küçük değişiklikler yaparak siteleri taklit edebilir. Devam etmeden önce istenilen Ağ URL adresi ile etkileşim kurduğunuzdan emin olun. Punycode sürümü:", "new_default_network_url": "Yeni varsayılan ağ URL adresi", "current_label": "Mevcut", "new_label": "<PERSON><PERSON>", "review": "<PERSON><PERSON><PERSON>", "view_details": "Ayrıntıları görüntüle", "network_details": "Ağ bilgileri", "network_select_confirm_use_safe_check": "Onayla seçeneğinin seçilmesi Ağ bilgileri kontrolünü açar. Ağ bilgileri kontrolünü şurada kapatabilirsiniz: ", "network_settings_security_privacy": "Ayarlar > Güvenlik ve gizlilik", "network_currency_symbol": "Para Birimi <PERSON>ü", "network_block_explorer_url": "Blok Gezgini URL adresi", "search": "Önceden eklenmiş ağı ara", "search-short": "Search", "add": "<PERSON><PERSON>", "continue": "<PERSON><PERSON> et", "cancel": "İptal et", "approve": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "edit_network_details": "Ağ bilgilerini düzenle", "malicious_network_warning": "Kötü amaçlı bir ağ sağlayıcı blokzincirinin durumu hakkında yalan söyleyebilir ve ağ aktivitenizi kaydedebilir. Sadece güvendiğiniz özel ağları ekleyin.", "security_link": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "network_warning_title": "<PERSON><PERSON>", "additional_network_information_title": "<PERSON><PERSON><PERSON> Ağ Bilgileri", "network_warning_desc": "Bu ağ bağlantısı üçüncü taraflara dayalıdır. Bu bağlantı daha az güvenli olabilir veya üçüncü tarafların aktiviteleri takip etmesine olanak sağlayabilir.", "additonial_network_information_desc": "Bu ağların bazıları üçüncü taraflara dayalıdır. Bağlantılar daha az güvenilir olabilir veya üçüncü tarafların aktiviteleri takip etmesine olanak sağlayabilir.", "connect_more_networks": "Daha fazla ağ bağlan", "learn_more": "<PERSON>ha fazla bilgi edin", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/the-risks-of-connecting-to-an-unknown-network/", "switch_network": "<PERSON><PERSON>", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "select_all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "deselect_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> kaldır", "new_network": "Yeni ağ eklendi", "network_name": "{{networkName}} Ağı", "network_added": " artık ağ seçicide mevcut.", "provider": "<PERSON>ze bakiyelerinizi söyleyen ve işlemlerini dürüstçe yayınlayan bir sağlayıcı güvenilirdir.", "no_match": "Eşleşen sonuç bulunamadı.", "empty_popular_networks": "<PERSON><PERSON>m popüler ağları eklediniz.", "add_other_network_here": "bir sorgu gö<PERSON>.", "you_can": "<PERSON><PERSON><PERSON>", "add_network": "manuel o<PERSON> daha fazla ekley<PERSON>.", "add_specific_network": "{{network_name}} ekle", "update_network": "{{network_name}} g<PERSON><PERSON><PERSON>", "select_network": "B<PERSON> ağ seçin", "enabled_networks": "Etkinleştirilen ağlar", "additional_networks": "İlave ağlar", "all_popular_networks": "<PERSON><PERSON><PERSON>", "show_test_networks": "Test ağlarını göster", "deprecated_goerli": "Ethereum'da yaşanan protokol değişikliklerinden dolayı: Goerli test ağı güvenilir bir şekilde çalışmayabilir ve yakında kullanım dışı olacak.", "network_deprecated_title": "Bu ağ artık kullanılmıyor", "network_deprecated_description": "Bağlanmaya çalıştığınız ağ artık Metamask'te desteklenmiyor.", "edit_networks_title": "Ağları düzenle"}, "permissions": {"title_this_site_wants_to": "Bu site şunları yapmak istiyor:", "title_dapp_url_wants_to": "{{dappUrl}} şunları yapmak istiyor:", "title_dapp_url_has_approval_to": "{{dappUrl}} <PERSON><PERSON><PERSON> i<PERSON><PERSON> onay veriyor:", "use_enabled_networks": "Etkin ağlarınızı kullanmak", "wants_to_see_your_accounts": "Hesaplarınızı görün ve işlem önerin", "requesting_for": "Talep: ", "requesting_for_accounts": "{{numberOfAccounts}} hesap i<PERSON>in talep ediliyor", "requesting_for_networks": "{{numberOfNetworks}} ağ için talep ediliyor", "n_networks_connect": "{{numberOfNetworks}} ağ bağlı ", "network_connected": "ağ bağlı ", "see_your_accounts": "Hesaplarınızı görün ve işlem önerin", "connected_to": "Şuraya bağlanıldı: ", "manage_permissions": "İzinleri Yönet", "edit": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "connection_details_title": "Bağlantı Bilgileri", "connection_details_description": "Bu siteye {{connectionDateTime}} itibariyle MetaMask tarayıcısını kullanarak bağlandınız", "title_add_network_permission": "<PERSON>ğ izni ekle", "add_this_network": "<PERSON>u ağı ekle", "permitted_networks": "İzin verilen ağlar", "choose_from_permitted_networks": "İzin verilen ağlardan seçiminizi yapın", "this_site_cant": "Bu site geçerli ağınızla kullanılamaz. Ağ izinlerini ekleyin veya halihazırda izin verilmiş bir ağ seçin.", "non_permitted_network_description": "Bu site geçerli ağınızla kullanılamaz. Ağ izinlerini ekleyin veya halihazırda izin verilmiş bir ağı seçin.", "edit_permissions": "İzinleri düzenle", "permitted_networks_info_sheet_description": "Bu, daha önce bu sitede kullanılmasına izin verdiğiniz ağların bir listesidir. Listeden bir ağı seçin veya bu site için ağ izinlerinin düzenleyin.", "connect_an_account": "<PERSON><PERSON> hesabı bağla", "sdk_connection": "SDK {{originator_platform}} v{{api_version}}"}, "account_dapp_connections": {"account_summary_header": "Bu web sitesini MetaMask'e bağla"}, "select": {"cancel": "İptal", "done": "<PERSON><PERSON>"}, "signature_request": {"title": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "sign": "İmzala", "sign_requested": "İmzanız talep ediliyor", "signing": "Bu mesajı imzala?", "account_title": "Hesa<PERSON>:", "balance_title": "Bakiye:", "message": "<PERSON><PERSON>", "message_from": "<PERSON><PERSON>", "learn_more": "<PERSON>ha fazla bilgi edin", "read_more": "Daha fazlasını oku"}, "watch_asset_request": {"title": "Önerilen <PERSON>", "cancel": "İPTAL", "add": "TOKEN EKLE", "message": "Bu tokeni eklemek istiyor musunuz?", "token": "Token", "balance": "Bakiye"}, "unit": {"eth": "ETH", "sai": "SAI", "dai": "DAI", "negative": "-", "divisor": "/", "token_id": "#", "colon": ":", "point": ".", "week": "hafta", "day": "g<PERSON>n", "hour": "sa", "minute": "dk", "second": "sn", "empty_data": "0x"}, "biometrics": {"enable_touchid": "Touch ID ile kilidi aç?", "enable_faceid": "Face ID ile kilidi a<PERSON>?", "enable_fingerprint": "Parmak İzi ile kilidi aç?", "enable_biometrics": "Biyometri ile kilidi aç?", "enable_device_passcode_ios": "Cihaz parolası ile kilidi aç?", "enable_device_passcode_android": "Cihazın PIN kodu ile kilidi aç?"}, "authentication": {"auth_prompt_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "auth_prompt_desc": "MetaMask'ı kullanabilmek için lütfen doğrulama yapın", "fingerprint_prompt_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fingerprint_prompt_desc": "MetaMask'ın kilidini açmak için parmak izinizi kullanın", "fingerprint_prompt_cancel": "İptal"}, "accountApproval": {"title": "BAĞLANTI TALEBİ", "walletconnect_title": "WALLETCONNECT TALEBİ", "action": "Bu siteye bağlan?", "action_reconnect": "Bağlantıyı sürdürmek için merkeziyetsiz uygulama üzerinde gördüğünüz sayıyı seçin", "action_reconnect_deeplink": "Bu merkeziyetsiz uygulamaya tekrar bağlanmak istiyor musunuz?", "connect": "Bağlan", "resume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "donot_rememberme": "Bu merkeziyetsiz uygulama bağlantısını unut", "disconnect": "Bağlantıyı kes", "permission": "<PERSON><PERSON><PERSON>:", "address": "genel adres", "sign_messages": "<PERSON><PERSON>", "on_your_behalf": "mesajları imzala", "warning": "Bağla düğmesine tıklayarak bu merkeziyetsiz uygulamanın genel adresinizi görüntülemesine izin verirsiniz. Bu, verilerinizi olası kimlik avı risklerinden korumak için önemli bir güvenlik adımıdır."}, "import_private_key": {"title": "Hesabı İçe Aktar", "description_one": "İçe aktarılan özel anahtarlar hesabınızda yedeklenir ve aynı Google veya Apple oturum açma bilgileriyle giriş yaptığınızda otomatik olarak senkronize olur.", "description_srp": "İçe aktarılan hesaplar cüzdanınızda görüntülenebilir ancak MetaMask Gizli Kurtarma İfadenizle kurtarılamaz.", "learn_more": "<PERSON>ha fazla bilgi edin", "learn_more_here": "içe aktarılan anahtarların nasıl çalıştığı hakkında.", "learn_more_srp": "İçe aktarılan hesaplar hakkında daha fazla bilgi alın", "here": "Burada daha fazla ağ keşfedebilirsiniz.", "subtitle": "Özel anahtar dizinizi yapıştırın", "cta_text": "İçe aktar", "example": "ör.\n3a1076bf45ab87712ad64ccb3b10217737f7faacbf2872e88fdd9a537d8fe266", "error_title": "<PERSON><PERSON> ters gitti", "error_message": "Bu özel anahtarı içe aktaramadık. Lütfen doğru girdiğinizden emin olun.", "error_empty_message": "<PERSON>zel anahtarınızı girmeniz gerekiyor.", "or_scan_a_qr_code": "veya bir QR Kodunu taratın"}, "import_private_key_success": {"title": "Hesap başarılı bir şekilde içe aktarıldı!", "description_one": "Artık hesabınızı MetaMask'ta görüntüleyebileceksiniz."}, "import_new_secret_recovery_phrase": {"title": "Gizli Kurtarma İfadesini İçe Aktar", "description": "Cüzdanınızın gizli kurtarma ifadesini girin. Herhangi bir Ethereum, Solana veya Bitcoin gizli kurtarma ifadesini içe aktarabilirsiniz.", "subtitle": "Gizli kurtarma ifadenizi yapıştırın", "cta_text": "<PERSON><PERSON> et", "paste": "Yapıştır", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "srp_number_of_words_option_title": "Sözcük sayısı", "12_word_option": "12 sözcükten oluşan bir ifadem var", "24_word_option": "24 sözcükten oluşan bir ifadem var", "error_title": "<PERSON><PERSON> ters gitti", "error_message": "Bu gizli kurtarma ifadesini içe aktaramadık. Lütfen ifadeyi doğru girdiğinizden emin olun.", "error_empty_message": "G<PERSON><PERSON> kurtarma ifadenizi girmeniz gerekiyor.", "error_number_of_words_error_message": "Gizli Kurtarma İfadeleri 12 veya 24 sözcük içerir", "error_srp_is_case_sensitive": "Giriş geçersiz! Gizli Kurtarma İfadesi büyük/küçük harfe duyarlıdır.", "error_srp_word_error_1": "Sözcük ", "error_srp_word_error_2": " yanlış veya yanlış yazılmış.", "error_multiple_srp_word_error_1": "Sözcük ", "error_multiple_srp_word_error_2": " ve ", "error_multiple_srp_word_error_3": " yanlış veya yanlış yazılmış.", "error_invalid_srp": "G<PERSON>li Kurtarma İfadesi geçersiz", "error_duplicate_srp": "Bu Gizli Kurtarma İfadesi zaten içe aktarıldı.", "success_1": "<PERSON><PERSON><PERSON>", "success_2": "i̇çe aktarıldı"}, "first_incoming_transaction": {"title": "Hesabınıza {{asset}} yatırıldı", "amount": "Tutar:", "account": "Hesa<PERSON>:", "from": "<PERSON><PERSON>:", "cta_text": "<PERSON><PERSON>"}, "secure_your_wallet": {"title": "Cüzdanınızı koruyun", "step_1": "Adım 1:", "step_1_description": "<PERSON><PERSON><PERSON>", "step_2": "Adım 2:", "step_2_description": "Cüzdan Gizli Kurtarma İfadesini kaydedin", "info_text_1": "MetaMask cüzdanınızın kurulumunu bitirmek için birkaç dakika ayırın.", "info_text_2": "<PERSON><PERSON>, sadece cihazınızı kaybettiğiniz takdirde paranıza erişim sağlayabilmenizi ve cüzdanınızı kurtarmanızı sağlar", "cta_text": "<PERSON><PERSON><PERSON>", "creating_password": "<PERSON><PERSON><PERSON> oluşturuluyor...", "srp_list_selection": "<PERSON><PERSON><PERSON> Kurtarma İfadesi Seç"}, "account_backup_step_1": {"remind_me_later": "<PERSON><PERSON> sonra hatırlat", "remind_me_later_subtext": "(Önerilmez)", "title": "Cüzdanınızı koruyun", "info_text_1_1": "Paranızı kaybetme riskini almayın. Cüzdanınızı koruyun,", "info_text_1_2": "<PERSON><PERSON><PERSON>", "info_text_1_3": "güvendiğiniz bir yere ka<PERSON>in.", "info_text_1_4": "Uygulamaya giriş ya<PERSON>ğınızda veya yeni bir cihaz edindiğinizde cüzdanınızı kurtarmanın tek yolu budur.", "cta_text": "Başlarken", "cta_subText": "Mutlaka <PERSON>", "skip_button_cancel": "<PERSON><PERSON><PERSON> koru", "skip_button_confirm": "Atla", "skip_title": "<PERSON><PERSON><PERSON> atla?", "skip_check": "Bu Giz<PERSON> Kurtarma İfadesini kaybederseniz bu cüzdana erişim <PERSON>ğlayamayacaksınız.", "what_is_seedphrase_title": "<PERSON><PERSON><PERSON> Kurtarma İfadesi nedir?", "what_is_seedphrase_text_1": "G\nAnahtar cümle veya kurtarma ifadesi olarak da anılan Gizli Kurtarma İfadesi, kripto cüzdanınıza erişiminizi ve kontrolü sağlayan bir sözcük dizisidir. Cüzdanınızı MetaMask'e taşımak için bu ifadeye ihtiyacınız olur.", "what_is_seedphrase_text_2": "G<PERSON>li Kurtarma İfadenizi gizli ve güvende tutmalısınız. Gizli Kurtarma İfadenizi ele geçiren biri hesaplarınızın kontrolünü de ele geçirir.", "what_is_seedphrase_text_3": "Sadece sizin erişim sağlayabileceğiniz bir yere kaydedin. Kaybettiğiniz takdirde onu kurtarmanıza MetaMask bile yardımcı olamaz.", "what_is_seedphrase_text_4": "<PERSON><PERSON><PERSON> İfadenizi elinde bulunduran herkes:", "seedPhrase_point_1": "Tüm paranızı alabilir", "seedPhrase_point_2": "İşlemleri onaylayabilir", "seedPhrase_point_3": "Oturum açma bilgilerinizi değiştirebilir", "what_is_seedphrase_confirm": "<PERSON><PERSON><PERSON><PERSON>"}, "account_backup_step_1B": {"title": "Cüzdanınızı koruyun", "subtitle_1": "Cüzdanınızın", "subtitle_2": "<PERSON><PERSON><PERSON> Kurtarma İfadesini koruyun.", "cta_text": "Başla", "learn_more": "Daha fazlasını öğren", "why_important": "Neden ö<PERSON>lid<PERSON>?", "manual_title": "<PERSON>", "manual_subtitle": "Gizli Kurtarma İfadenizi bir kağıt parçasının üzerine yazıp güvenli bir yerde saklayın.", "manual_security": "Güvenlik seviyesi: Çok güçlü", "risks_title": "Riskler:", "risks_1": "<PERSON><PERSON><PERSON><PERSON>", "risks_2": "Nereye koyduğunuzu unutmanız", "risks_3": "Bir başkasının onu bulması", "other_options": "<PERSON><PERSON><PERSON> seçenekler: <PERSON><PERSON><PERSON>t olması gerekmez!", "tips_title": "İpuçları:", "tips_1": "Banka kasasında saklayın", "tips_2": "<PERSON><PERSON> kasada sa<PERSON>n", "tips_3": "<PERSON><PERSON> fazla gü<PERSON>li yerde sa<PERSON>n", "why_secure_title": "Cüzdanınızı koruyun", "why_secure_1": "Paranızı kaybetme riskini almayın. Gizli Kurtarma İfadenizi güvendiğiniz bir yere kaydederek koruyun.", "why_secure_2": " Uygulamaya giriş ya<PERSON>ğınızda veya yeni bir cihaz edindiğinizde cüzdanınızı kurtarmanın tek yolu budur."}, "account_backup_step_2": {"cancel_backup_title": "Yedeklemeyi İptal Et", "cancel_backup_message": "Cüzdanınızı geri yüklemek için Gizli Kurtarma İfadenizi mutlaka kaydetmenizi öneririz.", "cancel_backup_ok": "<PERSON><PERSON>, riski al<PERSON>", "cancel_backup_no": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Kurtarma İfadesini yedekle", "title": "<PERSON><PERSON> kağıt kalem alın", "info": "Sonraki adım Gizli Kurtarma İfadenizi yazmaktır.", "info_2_1": "<PERSON><PERSON><PERSON> şu isten<PERSON>ek:", "info_2_2": "onay i<PERSON><PERSON>", "info_2_3": " tekrar gir", "cta_text": "<PERSON><PERSON>"}, "account_backup_step_3": {"cancel_backup_title": "Yedeklemeyi İptal Et", "cancel_backup_message": "Cüzdanınızı geri yüklemek için Gizli Kurtarma İfadenizi mutlaka kaydetmenizi öneririz.", "cancel_backup_ok": "<PERSON><PERSON>, riski al<PERSON>", "cancel_backup_no": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Kurtarma İfadesini yedekle", "title": "<PERSON><PERSON><PERSON> biri i<PERSON>yor mu?", "info_text": "Başka bir kişi veya robotun ekranınızı izlemediğinden emin olun. <PERSON><PERSON><PERSON> Kurtarma İfadeniz kopyalanırsa bu, paranızı çalmak için başka cihazlarda kullanılabilir", "cta_text": "HİÇ KİMSE BENİ İZLEMİYOR"}, "account_backup_step_4": {"cancel_backup_title": "Yedeklemeyi İptal Et", "cancel_backup_message": "Cüzdanınızı geri yüklemek için Gizli Kurtarma İfadenizi mutlaka kaydetmenizi öneririz.", "cancel_backup_ok": "<PERSON><PERSON>, riski al<PERSON>", "cancel_backup_no": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Kurtarma İfadesini yedekle", "back": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>arma İfadeniz", "info_text_1": "Bu sözcükleri dikkatli bir şekilde kağıda yazın. Sıralamaları fark etmez.", "info_text_2": "Sonraki ekranda girmeniz is<PERSON>ek", "cta_text": "İFADEYİ KOPYALADIM", "confirm_password": "Şifrenizi onaylayın", "before_continiuing": "Devam etmeden önce şifrenizi onaylamanız gerekiyor", "confirm": "ONAYLA"}, "account_backup_step_5": {"error_title": "Hay aksi!", "error_message": "Sözcüklerin sıralaması yanlış. Lütfen doğru yazdığınızdan emin olun ve gerekirse önceki ekrana gidin", "back": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>fadesini onayla", "info_text": "Her bir sözcüğü önceki ekranda size sunulan sıralamada girin.", "cta_text": "İFADEYİ ONAYLA", "modal_title": "Gizli Kurtarma İfadesi onaylandı!", "modal_text": "<PERSON><PERSON>, güvenlik önlemine uyduğunuzdan emin olma ama<PERSON>lı<PERSON>ı", "modal_button": "SONRAKİ"}, "account_backup_step_6": {"title": "Güvenlik İpuçları", "info_text": "<PERSON><PERSON><PERSON><PERSON>, Gizli Kurtarma İfadenizi kaybettiğiniz takdirde kurtaramaz", "tip_1": "<PERSON><PERSON><PERSON> İfadenizin birden fazla yedeğini tutun", "tip_2": "İfadeyi güvenilen bir şifre yöneticisinde ve güvenli bir yerde kağıt yedekler halinde sa<PERSON>ın", "tip_3": "<PERSON><PERSON> <PERSON><PERSON><PERSON> asla hiç kimseyle paylaşmayın", "disclaimer": "* Gizli Kurtarma İfadenizi şuraya giderek bulabilirsiniz: ", "disclaimer_bold": "Ayarlar > Güvenlik ve Gizlilik", "cta_text": "ANLADIM!", "modal_title": "Tebrikler!", "modal_text": "Tamamen yedeklisiniz ve gitmeye hazırsınız!", "modal_button": "BİTTİ", "copy_seed_phrase": "<PERSON><PERSON><PERSON> Kurtarma İfadesini PANOYA KOPYALA"}, "manual_backup": {"progressOne": "<PERSON><PERSON><PERSON>", "progressTwo": "Cüzdanı güvenli hale getir", "progressThree": "<PERSON><PERSON><PERSON>fadesini onayla"}, "manual_backup_step_1": {"steps": "Adım {{currentStep}}/{{totalSteps}}", "action": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi kaydedin", "info-1": "Bu size ait", "info-2": "<PERSON><PERSON><PERSON> Kurtarma İfadesini koruyun.", "info-3": "Doğru sıralamada yazın ve güvenli bir şekilde saklayın. G<PERSON>li Kurtarma İfadenizi elinde bulunduran biri cüzdanınıza erişim sağlayabilir.", "info-4": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "continue": "<PERSON><PERSON>", "reveal": "Açığa çıkarmak için dokunun", "watching": "Hiç kimsenin ekranınızı izlemediğinden emin olun.", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm_password": "Şifrenizi onaylayın", "before_continiuing": "<PERSON><PERSON> et<PERSON> i<PERSON> girin", "enter_current_password": "Mevcut şifrenizi girin", "confirm": "<PERSON><PERSON><PERSON>", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "learn_more": "Daha Fazlasını Öğren", "password": "Şifre"}, "manual_backup_step_2": {"steps": "Adım {{currentStep}}/{{totalSteps}}", "action": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi onaylayın", "info": "Eksik sözcükleri doğru sıralamada seçin.", "complete": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "success": "Başarılı", "error-title": "<PERSON><PERSON><PERSON>", "error-description": "<PERSON><PERSON><PERSON>arma İfadenizi kontrol edip tekrar deneyin.", "success-title": "Mükemmel!", "success-description": "Bu doğru! Unutmayın: <PERSON>u ifadeyi asla başkası<PERSON>ş<PERSON>ın.", "success-button": "<PERSON><PERSON><PERSON><PERSON>", "error-button": "<PERSON><PERSON><PERSON> dene", "continue": "<PERSON><PERSON> et"}, "manual_backup_step_3": {"steps": "Adım {{currentStep}}/{{totalSteps}}", "congratulations": "Tebrikler", "success": "Cüzdanınızı başarılı bir şekilde korudunuz! Gizli Kurtarma İfadenizi güvende tutmayı unutmayın, bu sizin sorumluluğunuzdadır!", "hint": "<PERSON><PERSON> bir i<PERSON>cu bırak?", "recover": "Kaybettiğiniz takdirde MetaMask cüzdanınızı kurtaramaz. Gizli Kurtarma İfadenizi Ayarlar > Güvenlik ve Gizlilik kısmında bulabilirsiniz.", "learn": "Daha fazlasını öğren", "done": "<PERSON><PERSON>", "recovery_hint": "<PERSON><PERSON>", "leave_hint": "Kendinize bir ipucu bırakın. Nasıl erişim sağlayabileceğinizi kendinize hatırlatmak için konumu / nereye kaydettiğinizi yazın. Bu bilgiler cihazınızdan ayrılmaz.", "no_seedphrase": "<PERSON><PERSON><PERSON> G<PERSON> Kurtarma İfadenizi yazmak için kullanmayın.", "example": "<PERSON><PERSON>. <PERSON><PERSON> evi", "save": "<PERSON><PERSON>", "cancel": "İptal"}, "phishing": {"ethereum_phishing_detection": "Ethereum Kimlik Avı Algılama", "ethereum_phishing_detector": "Ethereum Kimlik Avı Algılayıcı", "intro": " şu anda MetaMask alan adı uyarı listesindedir. <PERSON><PERSON>, bizde bulunan bilgilere göre MetaMask, bu alan adının şu anda güvenliğinizi ihlal edebiliyor olduğuna inanıyor ve ek bir güvenlik özelliği olarak MetaMask bu siteye erişimi kısıtlamıştır. Bunu geçersiz kılmak için lütfen riski size ait olmak üzere nasıl devam edebileceğiniz hakkındaki talimatlar için bu uyarının geri kalan kısmını okuyun.", "reasons": "Uyarı listemizde sitelerin yer almasının pek çok sebebi vardır ve uyarı listemiz diğer çok kullanılan sektör listelerinden derlenir. Sebepleri şunlar olabilir: Şurada pozitif olan alan adları gibi bilinen dolandırıcılık veya güvenlik riskleri:", "list_content": "Bu uyarı listelerinde yer alan alan <PERSON>, tamamen kötü amaçlı web siteleri ve kötü amaçlı bir aktör tarafından gizliliği ihlal edilen meşru web siteler olabilir.", "to_read_more": "Bu site hakkında daha fazlasını okumak için ", "review_on_etherscam": "lütfen Etherscam üzerinde alan adını inceleyin.", "warning": "Bu uyarı listesinin gönüllü bir temelde derlendiğini unutmayın. Bu liste yanlış veya eksik olabilir. Bir alan adının bu listede yer alması o alan adının güvenliğine dair açıkça bir garanti değildir. Her zaman olduğu gibi işlemlerinizin sorumluluğu size aittir. Uyarı listemizde başka bir alan adı ile etkileşimde bulunmak istiyorsanız", "continue_on_your_own": "riski size ait olmak üzere devam ederek bunu yapabilirsiniz.", "file_an_issue_intro": "Bu alan adına yanlış bir şekilde bayrak eklendiğini veya bloke edilmiş meşru bir web sitesinin kendi güvenlik sorunlarını çözdüğünü düşünüyorsanız", "file_an_issue": "lütfen şikayet bildirin.", "back_to_safety": "G<PERSON><PERSON>liğe geri dön", "site_might_be_harmful": "Bu web sitesi zararlı olabilir", "metamask_flagged_site": "MetaMask, ziyaret etmeye çalıştığınız siteyi aldatıcı olarak işaretledi. Saldırganlar tehlikeli bir şey yapmanız konusunda sizi aldatabilir.", "you_may_proceed_anyway": "<PERSON><PERSON>", "proceed_anyway": "il<PERSON>leyeb<PERSON><PERSON><PERSON><PERSON>", "but_please_do_so_at_your_own_risk": "ancak bunu yapmanın riski yalnızca size aittir.", "report_detection_problem": "Bir algılama problemini bildir", "share_on_twitter": "Bunu yararlı bulduysanız X üzerinde paylaşın!", "share_text": "MetaMask potansiyel olarak zararlı bir siteyi ziyaret etmemi <PERSON>: {{url}}. Güvende kalın!"}, "notifications": {"timeout": "Zaman Aşımı", "no_date": "Bilinmeyen", "yesterday": "<PERSON><PERSON><PERSON>", "staked": "<PERSON>ake <PERSON>", "received": "Alınan", "unstaked": "Unstake edildi", "mark_all_as_read": "Tümünü okundu olarak işaretle", "to": "Alıcı", "rate": "<PERSON><PERSON> (ücre<PERSON> dahil)", "unstaking_requested": "Unstake talep edildi", "stake_completed": "Stake tama<PERSON>landı", "withdrawal_completed": "Para çekme işlemi tamamlandı", "unstake_completed": "Unstake tamamlandı", "withdrawal_requested": "Para çekme talep edildi", "stake_ready_to_be_withdrawn": "Stake çekilmeye hazır", "swap_completed": "{{from}} - {{to}} swap i<PERSON><PERSON>i gerçekleştirildi", "swap": "Swap yapılan:", "sent": "{{address}} ad<PERSON><PERSON>", "menu_item_title": {"metamask_swap_completed": "{{symbolIn}} - {{symbolOut}} swap işlemi gerçekleştirildi", "erc20_sent": "{{address}} ad<PERSON><PERSON>", "erc20_received": "{{address}} adresinden alındı", "eth_sent": "{{address}} ad<PERSON><PERSON>", "eth_received": "{{address}} adresinden alındı", "erc721_sent": "NFT alıcısı: {{address}}", "erc1155_sent": "NFT alıcısı: {{address}}", "erc721_received": "NFT gönderen: {{address}}", "erc1155_received": "NFT gönderen: {{address}}", "rocketpool_stake_completed": "<PERSON>ake <PERSON>", "rocketpool_unstake_completed": "Unstake tamamlandı", "lido_stake_completed": "<PERSON>ake <PERSON>", "lido_withdrawal_requested": "Unstake talep edildi", "lido_withdrawal_completed": "Unstake tamamlandı", "lido_stake_ready_to_be_withdrawn": "Para çekme talep edildi"}, "menu_item_description": {"lido_withdrawal_requested": "{{amount}} {{symbol}} unstake talebiniz gönderildi", "lido_stake_ready_to_be_withdrawn": "<PERSON><PERSON> anda unstake {{symbol}} çekebilirsiniz"}, "modal": {"title_sent": "{{symbol}} <PERSON><PERSON><PERSON><PERSON>", "title_received": "{{symbol}} Aldı", "title_unstake_requested": "Unstake talep edildi", "title_untake_ready": "Para çekme hazır", "title_stake": "{{symbol}} stake edildi", "title_unstake_completed": "Unstake tamamlandı", "title_swapped": "{{symbolIn}} - {{symbolOut}} swap işlemi gerçekleştirildi", "label_address_to": "Alıcı", "label_address_from": "<PERSON><PERSON><PERSON><PERSON>", "label_address_to_you": "<PERSON><PERSON> (<PERSON>ze)", "label_address_from_you": "<PERSON><PERSON> (Sizden)", "label_asset": "Varlık", "label_account": "<PERSON><PERSON><PERSON>", "label_unstaking_requested": "Unstake Talep Edildi", "label_unstake_ready": "Para çekme hazır", "label_staked": "<PERSON>ake <PERSON>", "label_received": "Alınan", "label_unstaking_confirmed": "Unstake Onaylandı", "label_swapped": "Swap yapılan:", "label_to": "Alıcı"}, "received_from": "{{address}} adresinden {{amount}} {{ticker}} alındı", "nft_sent": "NFT alıcısı: {{address}}", "erc721_sent": "NFT alıcısı: {{address}}", "erc1155_sent": "NFT alıcısı: {{address}}", "erc721_received": "NFT gönderen: {{address}}", "erc1155_received": "NFT gönderen: {{address}}", "received_nft": "NFT gönderen: {{address}}", "pending_title": "İşlem gönderildi", "pending_deposit_title": "Para yatırma işlemi sürüyor!", "pending_withdrawal_title": "Para çekme işlemi sürüyor!", "cancelled_title": "İşlem iptal edildi!", "success_title": "İşlem #{{nonce}} Tamamlandı!", "speedup_title": "#{{nonce}} Hızlandırılıyor!", "success_deposit_title": "Para Yatırma İşlemi Tamamlandı!", "success_withdrawal_title": "Para Çekme İşlemi Tamamlandı!", "error_title": "<PERSON> aksi, bir <PERSON><PERSON><PERSON> ters gitti :/", "received_title": "{{amount}} {{assetType}} aldınız", "metamask_swap_completed_title": "Swap <PERSON>ı", "erc20_sent_title": "Para gönderildi", "erc20_received_title": "Para alındı", "eth_sent_title": "Para gönderildi", "eth_received_title": "Para alındı", "rocketpool_stake_completed_title": "Stake tama<PERSON>landı", "rocketpool_unstake_completed_title": "Unstake tamamlandı", "lido_stake_completed_title": "Stake tama<PERSON>landı", "lido_withdrawal_requested_title": "Para çekme talep edildi", "lido_withdrawal_completed_title": "Para çekme işlemi tamamlandı", "lido_stake_ready_to_be_withdrawn_title": "<PERSON><PERSON>, para çekme işlemine hazır", "erc721_sent_title": "NFT gönderildi", "erc721_received_title": "NFT alındı", "erc1155_sent_title": "NFT gönderildi", "erc1155_received_title": "NFT alındı", "default_message_title": "MetaMask", "default_message_description": "Görüntülemek için dokunun", "received_payment_title": "Anında ödeme alındı", "pending_message": "<PERSON><PERSON> be<PERSON>", "pending_deposit_message": "Ya<PERSON>ırma işleminin tamamlanması bekleniyor", "pending_withdrawal_message": "Çekme işleminin tamamlanması bekleniyor", "error_message": "Bu işlemi görüntülemek için dokunun", "error_retrieving_fcm_token": "Error while getting and saving FCM token", "error_fcm_not_found": "getFCMToken: No FCM token found", "error_checking_permission": "Error checking if a user has push notifications permission", "success_message": "Bu işlemi görüntülemek için dokunun", "speedup_message": "İşlem hızlandırılmaya çalışılıyor", "success_deposit_message": "Paranız kullanıma hazır", "success_withdrawal_message": "Paranız cüzdanınıza taşındı", "cancelled_message": "Bu işlemi görüntülemek için dokunun", "received_message": "Bu işlemi görüntülemek için dokunun", "received_payment_message": "{{amount}} DAI aldınız", "eth_received_message": "Bir miktar ETH aldınız", "metamask_swap_completed_message": "{{symbolIn}} - {{symbolOut}} swap işlemi gerçekleştirildi", "erc20_sent_message": "{{address}} ad<PERSON><PERSON>", "erc20_received_message": "Gönderen: {{address}}", "eth_sent_message": "{{address}} ad<PERSON><PERSON>", "rocketpool_stake_completed_message": "<PERSON>ake <PERSON>", "rocketpool_unstake_completed_message": "Unstake tamamlandı", "lido_stake_completed_message": "<PERSON>ake <PERSON>", "lido_withdrawal_requested_message": "Unstake talep edildi", "lido_withdrawal_completed_message": "Unstake tamamlandı", "lido_stake_ready_to_be_withdrawn_message": "Para çekme talep edildi", "push_notification_content": {"funds_sent_title": "Para gönderildi", "funds_sent_description": "Başarılı bir şekilde {{amount}} {{symbol}} gönderdiniz", "funds_sent_default_description": "Başarılı bir şekilde bir miktar token gönderdiniz", "funds_received_title": "Para alındı", "funds_received_description": "{{amount}} {{symbol}} aldınız", "funds_received_default_description": "Bir miktar token aldınız", "metamask_swap_completed_title": "Swap <PERSON>ı", "metamask_swap_completed_description": "MetaMask Swap başarılı oldu", "nft_sent_title": "NFT gönderildi", "nft_sent_description": "Başarılı bir şekilde bir NFT gönderdiniz", "nft_received_title": "NFT alındı", "nft_received_description": "Yeni NFT'ler aldı<PERSON>ız", "rocketpool_stake_completed_title": "Stake tama<PERSON>landı", "rocketpool_stake_completed_description": "RocketPool stake işleminiz başarılı oldu", "rocketpool_unstake_completed_title": "Unstake tamamlandı", "rocketpool_unstake_completed_description": "RocketPool unstake işleminiz başarılı oldu", "lido_stake_completed_title": "Stake tama<PERSON>landı", "lido_stake_completed_description": "Lido stake işleminiz başarılı oldu", "lido_stake_ready_to_be_withdrawn_title": "<PERSON><PERSON>, para çekme işlemine hazır", "lido_stake_ready_to_be_withdrawn_description": "Lido stake işleminiz şu anda para çekme işlemine hazır", "lido_withdrawal_requested_title": "Para çekme talep edildi", "lido_withdrawal_requested_description": "Lido para çekme talebiniz g<PERSON>il<PERSON>", "lido_withdrawal_completed_title": "Para çekme işlemi tamamlandı", "lido_withdrawal_completed_description": "Lido para çekme işleminiz başarılı oldu", "perps_position_liquidated_title": "Pozisyon likidasyona uğradı", "perps_position_liquidated_description_long": "{{symbol}} uzun pozisyonunuz kapandı.", "perps_position_liquidated_description_short": "{{symbol}} kısa pozisyonunuz kapandı.", "perps_stop_loss_triggered_title": "Zararda durdur tetiklendi", "perps_stop_loss_triggered_description_long": "{{symbol}} uzun pozisyonunuz zararda durdur noktanızda kapandı.", "perps_stop_loss_triggered_description_short": "{{symbol}} kısa pozisyonunuz zararda durdur noktanızda kapandı.", "perps_take_profit_triggered_title": "Kazancı al tetiklendi", "perps_take_profit_triggered_description_long": "{{symbol}} uzun pozisyonunuz kazancı al noktanızda kapandı.", "perps_take_profit_triggered_description_short": "{{symbol}} kısa pozisyonunuz kazancı al noktanızda kapandı.", "perps_limit_order_filled_title": "Limit <PERSON><PERSON>", "perps_limit_order_filled_description_long": "{{symbol}} uzun pozisyonunuz artık açık.", "perps_limit_order_filled_description_short": "{{symbol}} kısa pozisyonunuz artık açık."}, "prompt_title": "Anlık Bildirimleri Al", "notifications_enabled_error_title": "<PERSON><PERSON> ters gitti", "notifications_enabled_error_desc": "Bildirimleri etkinleştiremedik. Lütfen daha sonra tekrar deneyin.", "prompt_desc": "Cüzdan aktivitesi hakkında önemli uyarıları ve daha fazlasını almak için Ayarlar kısmından bildirimleri açın.", "prompt_ok": "Aç", "prompt_cancel": "<PERSON>ki daha sonra", "wc_connected_title": "{{title}} <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "wc_signed_title": "İmzalandı", "wc_sent_tx_title": "İşlem gönderdi", "wc_connected_rejected_title": "Bağlantı talebin<PERSON>", "wc_signed_rejected_title": "<PERSON><PERSON><PERSON>", "wc_signed_failed_title": "Bu imza talebi başarıs<PERSON>z oldu", "wc_sent_tx_rejected_title": "İşlem talebini <PERSON>", "approved_tx_rejected_title": "<PERSON>zin vermeyi <PERSON>", "wc_description": "Lütfen uygulamayı kontrol edin", "wallet": "Cüzdan", "web3": "Web3", "staking_provider": "Stake Sağlayıcısı", "network_fee_not_available": "<PERSON><PERSON> mevcut değil", "empty": {"title": "Burada gösterilecek bir şey yok", "message": "Burası cüzdanınızda aktivite olduğunda bildirimleri bulabileceğiniz yerdir. "}, "list": {"0": "Tümü", "1": "Cüzdan", "2": "<PERSON><PERSON><PERSON><PERSON>"}, "copied_to_clipboard": "Panoya kopyalandı", "address_copied_to_clipboard": "Adres hafıza panosuna kopyalandı", "transaction_id_copied_to_clipboard": "İşlem numarası hafıza panosuna kopyalandı", "activation_card": {"title": "Bildirimleri aç", "description_1": "Bildirimler ile cüzdanınızda neler olduğundan haberdar olun.", "description_2": "Bu özelliği kullanmak üzere hesabınız için anonim bir kimlik oluşturacağız. Bu kimlik sadece MetaMask'teki verilerinizi senkronize etmek için kullanılır ve aktivitelerinizi veya diğer tanımlayıcıları bağlamayarak gizliliğinizi korur.", "learn_more": "Bu özelliği kullanırken gizliliğinizi nasıl koruduğumuzu öğrenin.", "manage_preferences_1": "Bildirimleri dilediğiniz zaman şurada kapatabilirsiniz: ", "manage_preferences_2": "<PERSON><PERSON><PERSON> > <PERSON><PERSON><PERSON><PERSON><PERSON>.", "cancel": "İptal", "cta": "Aç"}}, "protect_your_wallet_modal": {"title": "Cüzdanınızı koruyun", "body_for_password": "Bir şifre belirleyerek ve Gizli Kurtarma İfadenizi (gereklidir) kaydederek cüzdanınızı koruyun.", "body_for_seedphrase": "Değer cüzdanınıza eklendiğine göre bir şifre belirleyerek ve Gizli Kurtarma İfadenizi (gereklidir) kaydederek cüzdanınızı koruyun.", "button": "Cüzdanı koru"}, "transaction_update_retry_modal": {"title": "İşlem Güncelleme Başarısız Oldu", "text": "<PERSON><PERSON><PERSON> denemek ister misiniz?", "cancel_button": "İptal", "retry_button": "<PERSON><PERSON><PERSON>"}, "payment_request": {"title": "<PERSON><PERSON>", "search_top_picks": "En çok seçilenler", "search_assets": "Varlık ara", "search_results": "<PERSON><PERSON>", "search_no_tokens_found": "Token bulunamadı", "your_tokens": "Tokenleriniz", "enter_amount": "<PERSON><PERSON> girin", "choose_asset": "Talep edeceğ<PERSON>z <PERSON>ı<PERSON>çin", "request_error": "<PERSON><PERSON> g<PERSON>, lütfen tekrar deneyin", "reset": "Sıfırla", "next": "<PERSON><PERSON><PERSON>", "amount_placeholder": "0.00", "link_copied": "Bağlantı panoya kopyalandı", "send_link_title": "Bağlantıyı Gönder", "description_1": "Talep bağlantınız gönderilmeye hazır!", "description_2": "Bu bağlantıyı bir arkadaşınıza gönderin ve ondan da göndermesi istenecek", "copy_to_clipboard": "<PERSON><PERSON> k<PERSON>", "qr_code": "QR KODU", "send_link": "Bağlantıyı Gönder", "request_qr_code": "Ödeme Talebi QR Kodu", "balance": "Bakiye"}, "receive_request": {"title": "Al", "share_title": "<PERSON><PERSON><PERSON>", "share_description": "Adresinizi e-posta veya mesajla g<PERSON>", "qr_code_title": "QR KODU", "qr_code_description": "Adresinizi okuyabilen taranabilir bir görü<PERSON>ü", "request_title": "<PERSON><PERSON>", "request_description": "Arkadaşlarınızdan varlık talep edin", "buy_title": "Satın Al", "buy_description": "Banka kartı veya banka transferi ile kripto satın alın", "public_address": "<PERSON><PERSON>", "public_address_qr_code": "<PERSON><PERSON>", "coming_soon": "Çok yakında...", "request_payment": "Ödeme <PERSON>", "copy": "<PERSON><PERSON>", "scan_address": "Ödeme almak için adresi tara", "copy_address": "<PERSON><PERSON><PERSON>"}, "experimental_settings": {"wallet_connect_dapps": "WalletConnect Oturumları", "wallet_connect_dapps_desc": "Aktif WalletConnect oturumlarının listesini görüntüle", "wallet_connect_dapps_cta": "Oturumları görüntüle", "network_not_supported": "Geçerli ağ desteklenmiyor", "select_provider": "Tercih ettiğiniz sağlayıcıyı seçin", "switch_network": "Lütfen ana ağa veya sepolia ağına geçin"}, "walletconnect_sessions": {"no_active_sessions": "<PERSON>kt<PERSON> yok", "end_session_title": "<PERSON><PERSON><PERSON><PERSON>", "end": "Sonlandır", "cancel": "İptal", "session_ended_title": "Oturum Sonlandırıldı", "session_ended_desc": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> son verildi", "session_already_exist": "Bu oturum zaten bağlı.", "close_current_session": "Yeni bir oturum başlatmadan önce geçerli oturumu kapat."}, "paymentRequest": {"title": "ÖDEME TALEBİ", "title_complete": "ÖDEME TAMAMLANDI", "confirm": "ÖDE", "cancel": "REDDET", "is_requesting_you_to_pay": "ödeme yapmanızı istiyor", "total": "TOPLAM:"}, "webview_error": {"title": "<PERSON><PERSON> ters gitti", "message": "Bu sayfayı yükleyemedik", "return_home": "<PERSON> <PERSON><PERSON><PERSON>"}, "offline_mode": {"title": "Çevrimdışısınız", "text": "B<PERSON>k<PERSON><PERSON>ri <PERSON> bağlanılamıyor.", "try_again": "<PERSON><PERSON><PERSON>", "learn_more": "Daha fazlasını öğren"}, "walletconnect_return_modal": {"title": "Tamamen hazırsınız!", "text": "Şimdi <PERSON>ıcınıza geri dönebilirsiniz"}, "account_bar": {"depositing_to": "Şuraya yatırılıyor:"}, "fiat_on_ramp": {"buy_eth": "ETH Satın Al", "buy": "{{ticker}} Satın Al", "purchased_currency": "{{currency}} Satın <PERSON>ı", "network_not_supported": "Geçerli ağ desteklenmiyor", "switch_network": "Lütfen Ana Ağa geçin", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purchases": "Satın <PERSON>lemleri", "purchase_method": "Satın <PERSON>", "amount_to_buy": "Satın alınacak tutar", "transak_webview_title": "Transak", "moonpay_webview_title": "MoonPay", "wyre_user_agreement": "<PERSON><PERSON> Kullanıcı Sözleşmesi", "wyre_terms_of_service": "Wyre Hizmet Şartları", "best_deal": "En iyi fırsat", "purchase_method_title": {"wyre_first_line": "Apple Pay kullandığınızda", "wyre_second_line": "%0 ücret.", "wyre_sub_header": "1 Temmuz 2020 tarihine kadar geçerli", "first_line": "Satın alma işleminizi nasıl", "second_line": "yapmak is<PERSON><PERSON>z?"}, "buy_ticker": "{{ticker}} Satın Al", "buy_ticker_stablecoins": "{{ticker}} ve Stable kripto para satın al", "multiple_payment_methods": "Çoklu Ödeme Yöntemleri", "debit_credit_bank_transfers_country": "Ülkeye göre banka/kredi ve banka havaleleri.", "debit_credit_bank_transfers_more_country": "Ülkeye göre banka/kredi, banka havaleleri ve daha fazlası.", "options_fees_vary": "100'den fazla <PERSON>, ücret ve limitler değişiklik gösterir", "moonpay_options_fees_vary": "145'ten faz<PERSON>, ü<PERSON>t ve limitler değişiklik gösterir", "some_states_excluded": "Bazı ülkeler ha<PERSON>çtir", "purchase_method_modal_close": "Ka<PERSON><PERSON>", "transak_cta": "Transak ile ETH satın al", "transak_cta_ticker": "Transak ile {{ticker}} satın al", "apple_pay": "Apple Pay", "via": "ile", "fee": "ücret", "Fee": "Ücret", "limited_time": "sınırlı süre", "supported_countries": "Desteklenen ülkeler", "no_countries_result": "“{{searchString}}” ile uyumlu desteklenen ülke yok", "wyre_loading_rates": " ", "fast_no_registration": "Hızlı - Kayıt gerekmez", "debit_credit_card_required": "Banka veya Kredi kartları. Apple Cash desteklenmez.", "select_card_country": "Kartınızın kayıtlı olduğu ülkeyi seçin (sizin bulunduğunuz ülke değil).", "search_country": "Ülke ara", "wyre_countries": "55'ten faz<PERSON>, ü<PERSON>t ve limitler değişiklik gösterir", "wyre_fees_us": "%2,9 + 0,30$ + gaz ücretleri (5$ minimum ücret)", "wyre_fees_us_fee": "%2,9 ücret + 0,30$ + gaz ($5 minimum ücret)", "wyre_fees_outside_us_fee": "%3.9 ücret + 0,30$ + gaz ($5 minimum ücret)", "wyre_estimated": "<PERSON><PERSON><PERSON> {{amount}} {{currency}}", "wyre_modal_text": "Wyre tarafından desteklenen Apple Pay ile ödeme, CT, HI, NC, NH, NY, VA ve VT eyaletleri hariç olmak üzere Amerika Birleşik Devletleri'nde desteklenir.🇺🇸", "wyre_minimum_deposit": "Minimum yatırılması gereken tutar{{amount}}", "wyre_maximum_deposit": "<PERSON><PERSON><PERSON><PERSON> yatırılması gereken tutar {{amount}}", "apple_pay_purchase": "{{currency}} <PERSON><PERSON>ın <PERSON>", "apple_pay_provider_total_label": "{{provider}}(MetaMask ile)", "buy_with": "<PERSON><PERSON><PERSON><PERSON> satın al:", "plus_fee": "Artı {{fee}} ücret", "date": "<PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "status": "Durum", "completed": "Tamamlandı", "pending": "Bekliyor", "failed": "Başarısız Oldu", "cancelled": "İptal Edildi", "amount": "<PERSON><PERSON>", "total_amount": "<PERSON>lam tutar", "gas_education_carousel": {"step_1": {"title": "{{ticker}} satın almadan önce gaz ücretlerini öğrenin", "average_gas_fee": "Güncel ortalama gaz ücreti:", "subtitle_1": "{{ticker}} öğenizi değiştirmeyi ve aktarmayı planlıyorsanız gaz ücretlerini karşılamak için ekstra satın alın.", "subtitle_2": "İşlemlerin gaz ücretleri {{ticker}} satın almanın maliyetinden ayrıdır.", "subtitle_3": "MetaMask gaz ücretlerinden kâr elde etmez."}, "step_2": {"title": "Gaz ücretleri nedir?", "subtitle_1": "Ethereum ağındaki gaz gücü işlemleri. İşlemlerinizi gerçekleştiren kripto madencilerine {{ticker}} cinsinde ödenen bir ücrettir.", "subtitle_2": "MetaMask gaz ücretlerinden kâr elde etmez.", "learn_more": "Gaz ücretleri hakkında daha fazla bilgi alın"}, "step_3": {"title": "Ne kadara ihtiyacım var?", "subtitle_1": "Gaz ücretleri ağ trafiğine ve işlem türüne göre dalgalanır.", "subtitle_2": "“Swap” işlemi gibi karmaşık bir işlem, bir “g<PERSON>nder” işleminin 5 katı - 10 katı maliyetinde olabilir.", "subtitle_3": "Gaz ücretlerini tahmin etmenin en iyi yolu", "subtitle_4": "önce işlemi denemek", "subtitle_5": "ve ne kadar gaz ücreti çıktığına bakmaktır.", "cta": "{{ticker}} satın almak için devam edin"}}}, "fiat_on_ramp_aggregator": {"buy": "al", "sell": "sat", "orders": "Transfers", "All": "Tümü", "Buy": "Satın Al", "Sell": "Sat", "token_marketplace": "Token pazar yeri", "Purchased": "Satın <PERSON>", "Sold": "Satıldı", "empty_orders_list": "No bank or card transfers yet.", "empty_buy_orders_list": "Hiç al emriniz yok", "empty_sell_orders_list": "<PERSON><PERSON> sat emriniz yok", "purchased_currency": "{{currency}} Satın <PERSON>ı", "sold_currency": "{{currency}} satıldı", "order_status_pending": "Bekliyor", "order_status_processing": "Gerçekleştiriliyor", "order_status_completed": "Tamamlandı", "order_status_failed": "Başarısız Oldu", "order_status_cancelled": "İptal Edildi", "network_switcher": {"title": "Desteklenmeyen {{rampType}} Ağı", "description": "Kripto {{rampType}} için desteklenen bir ağa geçmeniz gerekir", "no_networks_found": "Desteklenen ağ bulunamadı"}, "onboarding": {"what_to_expect": "Bekleyebileceklerin", "quotes": "Kripto satın alma özelliğimiz, entegre satıcılardan teklifleri toplayarak bekleme süresi olmadan doğrudan cüzdanınıza kripto para alabilmeniz için size bu kaynaklardan teklifleri sunar.", "quotes_sell": "Artık doğrudan MetaMask'te paraya çevirebilirsiniz! Biz size yolun her adımında rehberlik ederken güvenilir sağlayıcılardan en güncel teklifleri alın.", "benefits": "Daha az gaz ücreti ödenir ve çeşitli ağ, token ve ödeme yöntemleri desteklenir", "benefits_sell": "Kriptodan yerel para biriminize off-ramp artık hiç olmadığı kadar kolay.", "get_started": "Başlarken"}, "payment_method": {"payment_method": "<PERSON><PERSON><PERSON>", "cash_destination": "Nakit Varış Yeri", "instant": "Anında", "less_than": "En fazla", "minute": "dk.", "minutes": "dk.", "hour": "saat", "hours": "saat", "business_day": "<PERSON><PERSON>", "business_days": "<PERSON><PERSON>", "lowest_limit": "en düşük limit", "medium_limit": "orta limit", "highest_limit": "en yüksek limit", "lowest_sell_limit": "en düşük sat limiti", "medium_sell_limit": "orta sat limiti", "highest_sell_limit": "en yüksek sat limiti", "continue_to_amount": "<PERSON><PERSON> de<PERSON> et", "no_payment_methods_title": "{{regionName}} bölgesinde Ödeme Yöntemi yok", "no_cash_destinations_title": "{{regionName}} bölgesinde Nakit Varış Yeri yok", "no_payment_methods_description": "<PERSON><PERSON> anda bölgenizde desteklenen ödeme yöntemleri bulunmamaktadır. Lütfen yakında tekrar kontrol edin; yeni bölgelere desteği sık sık genişletiyoruz.\n\n{{regionName}} bölgesini yanlışlıkla seçtiyseniz bölgenizi sıfırlamak için aşağıdaki düğmeye tıklayın.", "no_cash_destinations_description": "<PERSON>u anda bölgenizde desteklenen nakit varış yeri yok. Lütfen kısa bir süre sonra kontrol edin; yeni bölgeleri desteklemek için sıklıkla hizmet ağımızı genişletiyoruz.\n\n{{regionName}} bölgesini yanlışlıkla seçtiyseniz bölgenizi sıfırlamak için aşağıdaki düğmeye tıklayın.", "reset_region": "Bölgeyi Sıfırla"}, "continue": "<PERSON><PERSON> et", "new_quotes_in": "<PERSON><PERSON> te<PERSON>r i<PERSON><PERSON> kalan süre", "fetching_new_quotes": "<PERSON>ni teklifler alınıyor...", "quotes_expire_in": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitmesine kalan süre:", "get_new_quotes": "<PERSON><PERSON> te<PERSON> al", "explore_more_options": "Daha fazla seçenek keşfedin", "one_more_option": "1 seçenek daha var", "more_options": "{{count}} se<PERSON><PERSON>k daha var", "previously_used": "<PERSON><PERSON> k<PERSON>anı<PERSON>", "best_rate": "En iyi oran", "most_reliable": "En güvenilir", "quotes_timeout": "Teklifler için zaman aşımı", "request_new_quotes": "En yeni en iyi oranı almak için lütfen yeni teklif talep edin.", "terms_of_service": "Hizmet Şartları", "amount_to_buy": "Satın alınacak tutar", "amount_to_sell": "Satılacak tutar", "want_to_buy": "Şunu almak istiyorsun:", "want_to_sell": "Satmak istediğiniz", "current_balance": "<PERSON><PERSON><PERSON> bakiye", "amount": "<PERSON><PERSON>", "send_cash_to": "Nakdinizi şuraya g<PERSON>in", "get_quotes": "<PERSON><PERSON><PERSON><PERSON> al", "done": "<PERSON><PERSON>", "fetching_quotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "select_a_quote": "<PERSON><PERSON><PERSON><PERSON>", "recommended_quote": "<PERSON><PERSON><PERSON><PERSON> tek<PERSON>", "select_a_cryptocurrency": "Kripto para seç", "select_a_cryptocurrency_description": "Select from the list of tokens available.", "search_by_cryptocurrency": "<PERSON><PERSON><PERSON> paraya göre arama yap", "search_by_currency": "Para birimi ara", "update_payment_method": "<PERSON><PERSON><PERSON>", "select_payment_method": "Ödeme yönte<PERSON> se<PERSON>", "select_cash_destination": "Nakdinizin nereye g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "select_region_currency": "Bölge Para Birimini Seç", "no_tokens_match": "“{{searchString}}” ile eşleşen token bulunamadı", "no_currency_match": "“{{searchString}}” ile eşleşen para birimi yok", "compare_rates": "Bu sağlayıcıların oranlarını karşılaştırın. Teklifler genel fiyata göre sıralanır.", "pay_with": "Ödeme <PERSON>", "continue_with": "{{provider}} ile devam et", "minimum": "Yatırılabilecek minimum miktar", "maximum": "Yatırılabilecek maksimum miktar", "insufficient_balance": "<PERSON>u tutar bakiyenizden yüksek", "insufficient_native_balance": "Gaz ücretlerini karşılamak için yet<PERSON> {{currency}} yok.", "enter_larger_amount": "<PERSON><PERSON> et<PERSON><PERSON> i<PERSON> daha b<PERSON><PERSON>k bir tutar girin", "enter_smaller_amount": "<PERSON><PERSON> etmek i<PERSON>in daha küçük bir tutar girin", "enter_lower_gas_fees": "Gaz ücretlerini ödemek için daha düşük bir tutar girin", "max": "MAKS.", "try_again": "<PERSON><PERSON><PERSON> dene", "error": "<PERSON><PERSON>", "something_went_wrong": "<PERSON> aksi, bir <PERSON><PERSON><PERSON> ters gitti", "report_this_issue": "<PERSON>u sorunu bildir", "no_providers_available": "Sağlayıcı mevcut değil", "try_different_amount_to_buy_input": "Farklı bir ödeme yöntemi seçmeyi dene veya satın almak istediğin tutarı artırmayı veya azaltmayı dene!", "try_different_amount_to_sell_input": "Farklı bir nakde çevirme varış yeri seçmeyi deneyin veya satmak istediğiniz tutarı artırın ya da azaltın!", "webview_received_error": "WebView şu hata durum kodunu aldı: {{code}}", "no_tokens_available_title": "Token Yok", "no_tokens_available": "<PERSON><PERSON> anda {{network}} üzerinde seçili ödeme yöntemi ile mevcut token bulunmamaktadır.", "no_sell_tokens_available": "<PERSON><PERSON> anda {{network}} üzerinde seçili nakit varış yeri ile mevcut token bulunmamaktadır.", "this_network": "bu a<PERSON>", "change_payment_method": "<PERSON>deme y<PERSON>", "change_cash_destination": "Na<PERSON>t varış yer<PERSON>tir", "try_different_region": "Farklı bir b<PERSON><PERSON>", "return_home": "<PERSON>", "region": {"buy_crypto_tokens": "Kripto Token Satın Al", "sell_crypto_tokens": "Kripto Token Sat", "title": "Bölgeni Seç", "description": "Kullanabileceğin ödeme yöntemleri ve tokenler üçüncü taraf entegrasyonlarımız tarafından belirlenir ve bölgen ile entegrasyonlarımızın desteğine bağlı olarak değişiklik gösterebilir.", "sell_description": "Nakit varış yeri seçenekleri ve tokenler bölgenize bağlı olarak değişiklik gösterebilir.", "search_by_country": "Ülkeye göre ara", "search_by_state": "Eyalete göre ara", "no_region_results": "Eşleşen bölge yok", "your_region": "<PERSON><PERSON>", "select_region": "Bölgeni seç", "select_region_title": "Bölgeni Seç", "select_country_registered": "Kartının kayıtlı olduğu ülkeyi seç (bulunduğun yere bakılmaksızın).", "unsupported": "<PERSON><PERSON><PERSON>", "unsupported_description": "Mümkün olan en kısa süre içinde bölgenizi de kapsamak için çok çalışıyoruz. Bu sırada, kripto {{rampType}} için diğer yolları öğrenmek üzere destek makalemize bakın.", "unsupported_link": "Destek Makalesini Ziyaret Et", "popular_regions": "<PERSON><PERSON><PERSON>", "regions": "<PERSON><PERSON><PERSON><PERSON>"}, "order_details": {"details_main": "<PERSON><PERSON>", "successful": "Emir Başarılı!", "your": "<PERSON>ze ait", "available_in_account": "<PERSON>u anda he<PERSON>", "delayed_bank_transfer": "Nakdin banka hesabınıza yansıması birkaç gün sürebilir.", "crypto": "kripto para", "failed": "Emir <PERSON>arı<PERSON>ız <PERSON>u", "cancelled": "<PERSON><PERSON>", "pending": "<PERSON><PERSON>", "failed_description": "Bir şeyler ters gitti ve {{provider}} emrinizi tamamlayamadı. Lütfen tekrar veya başka bir sağlayıcı ile deneyin.", "continue_order_description": "Emrinize devam etmek için bu sayfanın alt kısmındaki düğmeyi seçmeniz gerekecek.", "the_provider": "sağlayıcı", "processing": "<PERSON><PERSON>ştiriliyo<PERSON>", "processing_card_description": "Kredi Kartı/Banka Kartı satın alma işlemleri genellikle birkaç dakika sürer", "processing_bank_description": "Banka transferleri genellikle birkaç iş günü sürer", "details": "<PERSON><PERSON>", "via": "aracı", "purchase_amount": "Satın Alınan Toplam Tutar", "amount_received_total": "Alınan Toplam Tutar", "etherscan": "<PERSON><PERSON>m ayrıntıları şurada görüntüle", "start_new_order": "<PERSON><PERSON> bir emir b<PERSON><PERSON>", "continue_order": "Bu emre devam et", "questions": "So<PERSON><PERSON><PERSON>n mı var?", "contact_support": "Destek ile İletişim Kur", "support": "Destek", "view_order_status": "<PERSON><PERSON> du<PERSON> {{provider}} üzerinde görü<PERSON><PERSON><PERSON>in", "id": "<PERSON><PERSON>", "date_and_time": "<PERSON><PERSON><PERSON> ve <PERSON>", "payment_method": "<PERSON><PERSON><PERSON>", "destination": "Varış yeri", "token_amount": "Token Miktarı", "token_quantity_sold": "Satılan Token Miktarı", "exchange_rate": "Dönüştürme Oranı", "total_fees": "Toplam Ücretler", "amount": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "a_block_explorer": "bir blok gezgini"}, "send_transaction": {"sell_crypto": "<PERSON><PERSON><PERSON> sat", "send_description": "Siz {{provider}} ad<PERSON><PERSON>layıcıya {{cryptocurrency}} g<PERSON><PERSON><PERSON><PERSON>z, o da ardından nakdinizi {{paymentMethod}} ödeme yöntemine gönderir", "next": "<PERSON><PERSON><PERSON>", "hold_to_send": "Göndermek için tut", "send": "<PERSON><PERSON><PERSON>", "sent": "Gönderildi!"}, "notifications": {"purchase_failed_title": "{{currency}} satın alma işlemi başarısız oldu! Lütfen tekrar deneyin, verdiğimiz rahatsızlıktan dolayı özür dileriz!", "purchase_failed_description": "Ödeme yönteminizi ve kart desteğini doğrulayın", "purchase_cancelled_title": "Satın alma işleminiz iptal edildi", "purchase_cancelled_description": "Ödeme yönteminizi ve kart desteğini doğrulayın", "purchase_completed_title": "{{amount}} {{currency}} satın alma işleminiz başarılı oldu!", "purchase_completed_description": "{{currency}} para biriminiz artık kullanılabilir", "purchase_pending_title": "{{currency}} satın alma işleminiz gerçekleşiyor", "purchase_pending_description": "Bu işlem sadece birkaç dakika sürecektir...", "sale_failed_title": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oldu", "sale_failed_description": "Emriniz tamamlanamadı.", "sale_cancelled_title": "<PERSON><PERSON> i<PERSON> edil<PERSON>", "sale_cancelled_description": "Emriniz tamamlanamadı.", "sale_completed_title": "<PERSON><PERSON>", "sale_completed_description": "Emriniz başarılı oldu.", "sale_pending_title": "{{currency}} satışı gerçekleştiriliyor", "sale_pending_description": "<PERSON><PERSON><PERSON> anda gerçekleştiriliyor.", "no_date": "Bilinmeyen"}, "deposit_order_title": "{{currency}} Para Yatırma İşlemi"}, "swaps": {"title": "Kaydır", "onboarding": {"get_the": "<PERSON><PERSON><PERSON> z<PERSON>ın en iyi likidite", "best_price": "kaynaklarından", "from_the": "en iyi fiyatı", "top_liquidity": "en iyi likidite", "sources": "kaynakları.", "find_the": "En iyi", "best_swap": "swap i<PERSON><PERSON><PERSON>", "across": "bul...", "want_to_learn_more": "Daha fazla bilgi almak mı istiyorsunuz?", "learn_more": "MetaMask Swap işlemleri hakkında daha fazla bilgi alın", "what_are": "Token swap nedir?", "review_audits": "<PERSON><PERSON><PERSON><PERSON>me denetimimizi inceleyin", "start_swapping": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "feature_off_title": "Geçici olarak kullanılamıyor", "feature_off_body": "MetaMask Swaps şu anda bakım altındadır. Lütfen daha sonra tekrar kontrol edin.", "wrong_network_title": "Swaplar kullanılamıyor", "wrong_network_body": "Sadece Ethereum Ana Ağında token swap işlemi gerçekleştirebilirsiniz.", "unallowed_asset_title": "Bu token ile swap gerçekleştirilemiyor", "unallowed_asset_body": "<PERSON><PERSON><PERSON>z mekanikleri olan bazı tokenler şu anda swap i<PERSON><PERSON> des<PERSON>nmiyor.", "convert_from": "Şuradan Dönüştür:", "convert_to": "Şuna Dönüştür:", "verify": "<PERSON><PERSON><PERSON><PERSON>", "verified_on_sources": "{{sources} kaynaklarında doğrulandı.", "verify_on": "Her zaman token adresini <PERSON>:", "verify_address_on": "Token adresini şurada doğrulayın: ", "added_manually": "Bu token manuel o<PERSON>.", "verify_this_token_on": "Bu tokeni şurada doğrulayın: ", "only_verified_on": "{{symbol}} sadece {{occurrences}} kaynağında doğrulanır.", "block_explorer": "blok gezgini", "a_block_explorer": "bir blok gezgini", "make_sure_trade": "ve bunun alıp satmak istediğiniz token olduğundan emin olun.", "token_verification": "Token doğrulama", "token_multiple": "Birden fazla token aynı isim ve sembolü kullanabilir.", "token_check": "<PERSON><PERSON><PERSON>", "token_to_verify": "bunun sizin aradığınız token olup olmadığını doğrulayın.", "continue": "<PERSON><PERSON>", "select_a_token": "Token seç", "search_token": "Token adı girin veya adresi yapıştırın", "no_tokens_result": "“{{searchString}}” ile eşleşen token bulunamadı", "find_token_address": "Token adresini bul", "cant_find_token": "<PERSON>ir <PERSON>i bulamıyor musun?", "manually_pasting": "<PERSON><PERSON> <PERSON><PERSON> ad<PERSON> ya<PERSON>ırarak manuel olarak ekle", "token_address_can_be_found": "Token sözleşme adresi şurada bulunabilir", "gathering_token_details": "Token ayrıntıları toplanıyor...", "error_gathering_token_details": "<PERSON> aksi, token ayrıntıları toplanırken bir hata oldu.", "Import": "İçe aktar", "invalid_token_contract_address": "Token sözleşme adresi geçersiz.", "please_verify_on_explorer": "Lütfen şurada doğrula:", "add_warning": "Mevcut tokenlerin projeleri temsil ettiğini iddia eden sahte sürümleri dahil olmak üzere herkes token oluşturabilir.", "import_token": "Tokeni içe aktar?", "contract": "Sözleşme:", "available_to_swap": "{{asset}} ile swap gerçekleştirilebilir.", "use_max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "not_enough": "Bu swap işlemini gerçekleştirmek için <PERSON> {{symbol}} yok", "max_slippage": "<PERSON><PERSON><PERSON>", "max_slippage_amount": "<PERSON><PERSON><PERSON> kayma {{slippage}}", "slippage_info": "Emrinizin verildiği zaman ile emrinizin onaylandığı zaman arasında oran değişirse buna “kayma” denir. Kayma “maks. kayma” ayarınızı aştığı takdirde swap işleminiz otomatik olarak iptal edilir.", "slippage_warning": "Ne yaptığınızı bildiğinizden emin olun!", "allows_up_to_decimals": "{{symbol}}, {{decimals}} on<PERSON><PERSON>k sayıya kadar izin verir", "get_quotes": "<PERSON><PERSON><PERSON><PERSON> al", "starting": "Başlatılıyor...", "fetching_quotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "finalizing": "Sonlandırılıyor...", "quote": "<PERSON><PERSON><PERSON><PERSON>", "of": "/", "checking": "<PERSON><PERSON><PERSON>", "fetching_new_quotes": "<PERSON>ni teklifler alınıyor...", "you_need": "Bu <PERSON> i<PERSON><PERSON><PERSON> tama<PERSON>lamak için", "more_to_complete": "daha fazlasına ihtiyacınız var.", "more_gas_to_complete": "gaz için daha fazlasına ihtiyacınız var.", "token_marketplace": "Token pazar yeri", "market_price_unavailable_title": "İlerlemeden önce oranını kontrol et", "market_price_unavailable": "Piyasa fiyatı mevcut değil ve fiyat etkisi bu nedenle bilinmiyor. Swap gerçekleştirmeden önce lütfen alacağınız token miktarı konusunda rahat olduğunuzu doğrulayın.", "price_difference": "{{amount}} fiyat farkı", "price_difference_title": "<PERSON><PERSON><PERSON>ı", "price_difference_body": "<PERSON><PERSON>a fiyatlarındaki fark aracılardan alınan ücretler, p<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tica<PERSON><PERSON> büyüklüğü veya piyasa yetersizliğinden etkilenebilir.", "price_impact_title": "<PERSON><PERSON><PERSON>", "price_impact_body": "Fiyat etkisi geçerli piyasa fiyatı ve işlemin gerçekleşmesi sırasında alınan tutar arasındaki farktır. <PERSON>yat etkis<PERSON>, likidite havuzunun büyüklüğüne göre sizin ticaretinizin büyülüğünün bir fonksiyonudur.", "quotes_update_often": "Teklifler sık güncellenir", "quotes_update_often_text": "Teklifler güncel piyasa koşullarını yansıtmak amacıyla sık güncellenir.", "about_to_swap": "Swap gerçekleştirmek üzeresiniz:", "for": "<PERSON><PERSON><PERSON>", "new_quotes_in": "<PERSON><PERSON> te<PERSON>r i<PERSON><PERSON> kalan süre", "i_understand": "Anlıyorum", "quotes_expire_in": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitmesine kalan süre:", "saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n_quotes": "{{numberOfQuotes}} teklif", "view_details": "Ayrıntıları görüntüle", "estimated_gas_fee": "<PERSON><PERSON><PERSON>az <PERSON>", "gas_fee": "Gaz ücreti", "included": "dahil", "max_gas_fee": "Maks. gaz ücreti", "edit": "<PERSON><PERSON><PERSON><PERSON>", "quotes_include_fee": "%{{fee}} MetaMask ücreti tekliflere dahildir", "quotes_include_gas_and_metamask_fee": "Gaz ve %{{fee}} MetaMask ücreti teklife dahildir", "tap_to_swap": "Swap gerçekleştirmek için dokun", "swipe_to_swap": "Swap gerçekleştirmek için kaydır", "swipe_to": "Swap gerçekleştirmek için", "swap": "<PERSON><PERSON><PERSON>", "completed_swap": "Swap gerçekleştir!", "metamask_swap_fee": "MetaMask Swap ü<PERSON>ti", "fee_text": {"fee_is_applied": "Bu teklife otomatik olarak {{fee}} ücret dahil edilmiştir. MetaMask'in likidite sağlayıcı bilgilerinin toplandığı yazılımı kullanma lisansı karşılığında ödersiniz.", "fee_is_not_applied": "MetaMask bu teklife ek bir ücret eklemez."}, "enable": {"this_will": "<PERSON><PERSON>,", "enable_asset": "swap iş<PERSON>i i<PERSON> {{asset}}", "for_swapping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit_limit": "<PERSON><PERSON>"}, "quotes_overview": "<PERSON><PERSON><PERSON><PERSON><PERSON> genel bakış", "quote_details": "<PERSON><PERSON><PERSON><PERSON>", "receiving": "Alınıyor", "overall_value": "<PERSON><PERSON>", "best": "En İyi", "rate": "<PERSON><PERSON>", "quote_details_max_slippage": "<PERSON><PERSON><PERSON> kayma", "source": "<PERSON><PERSON><PERSON>", "estimated_network_fees": "Ta<PERSON>ini gaz ücretleri", "guaranteed_amount": "<PERSON><PERSON><PERSON><PERSON> tutar", "quote_source_dex": {"1": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "2": "merkeziyetsiz borsa", "3": "protokolünden gelir."}, "quote_source_rfq": {"1": "<PERSON><PERSON> tek<PERSON><PERSON>", "2": "bir teklife doğrudan yanıt veren", "3": "özel bir piyasa düzenleyicisinden gelir."}, "quote_source_agg": {"1": "<PERSON><PERSON> tek<PERSON><PERSON>", "2": "emrinizi farklı merkeziyetsiz borsalar arasında bölerek en iyi fiyatı bulan bir toplayıcıdan", "3": "farklı merkeziyetsiz borsalar arasında fiyatları karşılaştırır ve emrinizi böler."}, "quote_source_cnt": {"1": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "2": "gaz maliyetlerinden tasarruf edebilen yerli <PERSON>leri paketleyip a<PERSON>n", "3": "akıllı bir sözleşmeden gelir."}, "quotes_timeout": "Teklifler için zaman aşımı", "request_new_quotes": "En yeni en iyi oranı almak için lütfen yeni teklif talep edin.", "quotes_not_available": "<PERSON><PERSON><PERSON><PERSON> yok", "try_adjusting": "Tu<PERSON><PERSON> düzenlemeyi deneyip tekrar deneyin.", "error_fetching_quote": "<PERSON><PERSON><PERSON><PERSON> hata", "unexpected_error": "Beklenmeyen bir hata oldu, en yeni en iyi oranı almak için lütfen yeni teklif talep edin (hata: {{error}})", "get_new_quotes": "<PERSON><PERSON> te<PERSON> al", "try_again": "<PERSON><PERSON><PERSON>", "terms_of_service": "Hizmet şartları", "transaction_label": {"swap": "{{sourceToken}} ile {{destinationToken}} swap ger<PERSON><PERSON><PERSON>ş<PERSON>r", "approve": "<PERSON>wap i<PERSON><PERSON><PERSON>i i<PERSON> {{sourceToken}} onayla: En fazla {{upTo}}"}, "notification_label": {"swap_pending": "<PERSON><PERSON><PERSON> ({{sourceToken}} ile {{destinationToken}})", "swap_confirmed": "Swap i<PERSON><PERSON> ({{sourceToken}} ile {{destinationToken}})", "approve_pending": "Swap işlem<PERSON>i i<PERSON> {{sourceToken}} onaylanıyor", "approve_confirmed": "Swap işlemleri için {{sourceToken}} onaylandı"}, "medium_selected_warning": "Swap işlemleri zamana duyarlıdır. “Orta” önerilmez.", "high_recommendation": "Swap işlemleri genellikle zamana du<PERSON>lıdır. “Yüksek”, piyasa koşullarındaki değişikliklerden kaynaklanan olası zararları önlemeye yardımcı olacaktır.", "recommended": "Önerilir", "recommended_gas": "Önerilen gaz ücreti", "gas_included_tooltip_explanation": "<PERSON><PERSON> <PERSON>, g<PERSON><PERSON><PERSON>n veya alınan token miktarı düzenlenerek gaz ücretlerini içerir. Aktivite listenizde ayrı bir işlemde ETH alabilirsiniz.", "gas_included_tooltip_explanation_link_text": "Gaz ücretleri hakkında daha fazla bilgi edinin", "gas_education_title": "Ta<PERSON>ini gaz ücretleri", "gas_education_1": "Gaz ücretleri", "gas_education_2_ethereum": "Ethereum ağında işlem gerçekleştiren kripto madencilerine ödenir.", "gas_education_2": "ağda işlem gerçekleştiren kripto madencilerine ödenir.", "gas_education_3": "MetaMask gaz ücretlerinden kâr elde etmez.", "gas_education_4": "Bu", "gas_education_5": "“Tahmini gaz ücreti”", "gas_education_6": "olmasını beklediğimiz gerçek ücrettir. Tam tutar ağ koşullarına bağlıdır.", "gas_education_7": "“Maks. gaz ücreti”", "gas_education_8": "en çok harcayacağınız ücrettir. Ağ volatil olduğunda bu büyük bir tutar olabilir.", "gas_education_learn_more": "Gaz ücretleri hakkında daha fazla bilgi alın"}, "protect_wallet_modal": {"title": "Cüzdanınızı koruyun", "top_button": "Cüzdanı koru", "bottom_button": "<PERSON><PERSON> sonra hatırlat", "text": "Paranızı kaybetme riskini almayın. Gizli Kurtarma İfadenizi güvendiğiniz bir yere kaydederek koruyun.", "text_bold": "Uygulamaya giriş ya<PERSON>ğınızda veya yeni bir cihaz edindiğinizde cüzdanınızı kurtarmanın tek yolu budur.", "action": "Daha fazlasını öğren"}, "deeplink": {"invalid": "<PERSON><PERSON> bağlantı geçersiz", "not_supported": "<PERSON><PERSON> bağlantı desteklenmiyor"}, "error_screen": {"title": "<PERSON>ir hata o<PERSON>", "subtitle": "Bilgileriniz gösterilemiyor. Endişelenmeyin, cüzdanınız ve paranız güvendedir.", "try_again_button": "<PERSON><PERSON><PERSON>", "submit_ticket_1": "Düzeltebilmemiz için lütfen bu sorunu bildirin:", "submit_ticket_2": "Bu ekranın bir ekran görüntüsünü alın.", "submit_ticket_3": "<PERSON><PERSON>", "submit_ticket_4": "mesajını panoya kopyala.", "submit_ticket_5": "<PERSON><PERSON><PERSON>", "submit_ticket_6": "bir sorgu gö<PERSON>.", "submit_ticket_7": "Lütfen hata mesajını ve ekran görüntüsünü dahil edin.", "submit_ticket_8": "Bize bir hata raporu g<PERSON>", "submit_ticket_9": "Lütfen sorunla ilgili ayrıntıları ekleyin.", "bug_report_prompt_title": "Ne olduğunu bize sö<PERSON>yin", "bug_report_prompt_description": "<PERSON><PERSON>in ters gittiğini anlayabilmemiz için ayrıntıları ekleyin.", "bug_report_thanks": "Teşekkürler! Kısa bir süre sonra inceleyeceğiz.", "save_seedphrase_1": "Bu hatayı almaya devam ederseniz", "save_seedphrase_2": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi kaydedin", "save_seedphrase_3": "ve uygulamayı tekrar yükleyin. Unutmayın: <PERSON><PERSON><PERSON>arma İfadeniz olmadan cüzdanınızı geri yükleyemezsiniz.", "copied_clipboard": "Panoya kopyalandı", "ok": "<PERSON><PERSON>", "cancel": "İptal et", "send": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "modal_title": "Ne olduğunu açıklayın", "modal_placeholder": "Hatayı nasıl tekrarlayabileceğimiz gibi ayrıntıların paylaşılması problemi düzeltmemize yardımcı olacaktır.", "error_message": "Hata mesajı:", "copy": "Kopyala", "describe": "Ne olduğunu açıklayın", "try_again": "<PERSON><PERSON><PERSON> dene", "contact_support": "Destek ile iletişime geçin"}, "whats_new": {"title": "What's New", "remove_gns_new_ui_update": {"title": "New UI update", "introduction": "We've made updates to improve the app experience.", "descriptions": {"description_1": "Network dropdown moved to your assets", "description_2": "Swap and Bridge in one simple flow", "description_3": "Streamlined Send experience", "description_4": "A fresh account view"}, "more_information": "Now you can focus on your tokens and activity, not the networks behind them.", "got_it": "<PERSON><PERSON><PERSON><PERSON>"}}, "invalid_network": {"title": "Kişisel ağ \n%{network} \niçin zincir kimliğinin yeniden girilmesi gerekiyor.", "message": "Sizi kötü amaçlı veya kusurlu ağ sağlayıcılarından korumak amacıyla tüm kişisel ağlarda zincir kimlikleri gerekir.", "hint": "En popüler ağların zincir kimliklerini burada bulabilirsiniz: ", "edit_network_button": "<PERSON><PERSON><PERSON>ü<PERSON>", "cancel": "İptal"}, "switch_custom_network": {"title_existing_network": "Bu site ağ değiştirmek istiyor", "title_new_network": "Yeni ağ eklendi", "switch_warning": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, MetaMask'te seçili bir ağı önceden eklenmiş bir ağ ile değiştirecek:", "add_network_and_give_dapp_permission_warning": "Bu ağı MetaMask'e ekliyor ve {{dapp_origin}} uygulamasına bunu kullanma izni veriyorsunuz.", "update_network_and_give_dapp_permission_warning": "Bu ağı MetaMask'te güncelliyor ve {{dapp_origin}} uygulamasına bunu kullanma izni veriyorsunuz.", "request_update_network_url": "{{dapp_origin}} varsayılan ağ URL adresinizi güncellemeyi talep ediyor. Varsayılanları ve ağ bilgilerini dilediğiniz zaman düzenleyebilirsiniz.", "available": "artık ağ seçicide mevcut.", "cancel": "İptal", "switch": "<PERSON><PERSON>"}, "add_custom_network": {"title": "Bu sitenin ağ eklemesine izin ver?", "warning": "<PERSON>u, bu ağın MetaMask dahilinde kullanılmasına izin verir.", "warning_subtext_1": "MetaMask kişisel ağları veya onların güvenliğini doğrulamaz.", "warning_subtext_2": "Dolandırıcılıklar ve ağ güvenlik riskleri", "warning_subtext_3": "hakkında bilgi alın", "display_name": "G<PERSON><PERSON><PERSON><PERSON><PERSON> adı", "chain_id": "<PERSON><PERSON><PERSON><PERSON>", "network_url": "Ağ URL adresi", "currency_symbol": "Para birimi sembolü", "block_explorer_url": "Blok Gezgini URL adresi", "details_title": "Ağ bilgileri", "cancel": "İptal", "approve": "<PERSON><PERSON><PERSON>", "unrecognized_chain": "Bu özel ağ tanınmadı", "invalid_chain": "Bu Zincir Kimliği için %{rpcUrl} zincir kimliği ağ listesi ile uyumlu değil", "alert_recommend": "Size", "alert_verify": "zincir kimliğini doğrulamanızı öneririz", "warning_subtext_new": {"1": "MetaMask özel ağları doğrulamaz, bu yüzden sadece güvendiğiniz ağları onaylayın.", "2": "Ağ güvenlik riskleri ve dolandırıcılıklar hakkında daha fazla bilgi alın."}, "invalid_rpc_url": "Bu ağın URL adresi bu zincir kimliği için bilinen bir sağlayıcı ile eşleşmiyor.", "invalid_chain_token_decimals": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>, bu ağın on<PERSON><PERSON><PERSON> zincirinin kimliği ile eşleşmiyor.", "unrecognized_chain_name": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gö<PERSON>, bu ağın gö<PERSON><PERSON><PERSON><PERSON><PERSON>me adı zincir kimliği ile eşleşmiyor.", "unrecognized_chain_ticker": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu ağın sembolü bu zincir kimliği ile eşleşmiyor.", "unrecognized_chain_id": "Bu ağı tanıyamadık. Devam etmeden önce zincir kimliğinin doğru olduğundan emin olun."}, "media_player": {"loading": "Yükleniyor...", "not_found": "Ortam bulunamadı"}, "edit_gas_fee_eip1559": {"advanced_options": "Gelişmiş seçenekler", "gas_limit": "Gaz limiti", "max_priority_fee": "Maks. öncelik ücreti", "max_fee": "Maks. <PERSON>", "estimate": "<PERSON><PERSON><PERSON>", "recommended_gas_fee": "Önerilen gaz ücreti", "swaps_warning": "<PERSON>wap i<PERSON><PERSON><PERSON><PERSON> zamana son derec<PERSON>. “<PERSON><PERSON><PERSON><PERSON>, piyasa koşullarındaki değişikliklerden kaynaklanan olası zararları önlemeye yardımcı olacaktır.", "priority_fee_at_least_0_error": "Öncelik ücreti en az 1 GWEI olmalıdır", "learn_more": {"title": "Nasıl seçim <PERSON>?", "intro": "Doğru gaz ücretinin seçilmesi işlem türüne ve bunun sizin için ne kadar önemli olduğuna bağlıdır.", "aggressive_label": "Agresif", "aggressive_text": "Volatil piyasalarda bile yüksek olasılık. Popüler NFT düşüşleri gibi şeyler nedeniyle ağ trafindeki dalgalanmaları kapsamak için Agresif özelliğini kullanın.", "market_label": "<PERSON><PERSON><PERSON>", "market_text": "Güncel piyasa fiyatında hızlı işleme almak için Piyasa özelliğini kullanın.", "low_label": "Düşük", "low_text": "Düşük bir gaz ücreti sadece önemli olmayan işlemler için veya ağ trafiği çok düşük olduğunda kullanılmalıdır. Daha düşük bir ücret ile işleminizin ne zaman başarılı olacağını (veya başarılı olup olmayacağını) öngörmek zor olabilir.", "link": "Gaz ücretini kişiselleştirme hakkında daha fazla bilgi al."}, "save": "<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "max_priority_fee_low": "Maks. Öncelik Ücreti güncel ağ koşulları için düşük", "max_priority_fee_high": "Maks. Öncelik Ücreti gerekenden yüksek", "max_priority_fee_speed_up_low": "Maks. Öncelik Ücreti en az {{speed_up_floor_value}} GWEI olmalıdır (ilk işlemin %10 üzerinde)", "max_priority_fee_cancel_low": "Maks. Öncelik Ücreti en az {{cancel_value}} GWEI olmalıdır (ilk işlemin %50 üzerinde)", "max_fee_low": "Maks. Ücret güncel ağ koşulları için düşük", "max_fee_high": "Maks. Ücret gerekenden yüksek", "max_fee_speed_up_low": "Maks. Ücret en az {{speed_up_floor_value}} GWEI olmalıdır (ilk işlemin %10 üzerinde)", "max_fee_cancel_low": "Maks. Ücret en az {{cancel_value}} GWEI olmalıdır (ilk işlemin %50 üzerinde)", "learn_more_gas_limit": "Gaz limiti kullanmak istediğiniz maksimum gaz birimidir. Gaz birimleri “Maks. öncelik ücreti” ile “Maks. ücret” çarpanıdır. ", "learn_more_max_priority_fee": "Maks. <PERSON><PERSON><PERSON> (di<PERSON><PERSON> <PERSON><PERSON><PERSON> “madenci bahş<PERSON>”) do<PERSON><PERSON>an madencilere gider ve işleminizi önceliklendirmeleri için onlara teşvik sunar. Genellikle maks. ayarınızı ödersiniz.", "learn_more_max_fee": "Maks. ücret ödeyeceğiniz en fazla ücrettir (baz ücret + öncelik ücreti).", "learn_more_new_gas_fee": "Gaz ücretini geçerli ağ koşullarına göre güncelledik ve onu en az %10 oranında artırdık (ağ tarafından gerekli olan).", "learn_more_cancel_gas_fee": "Gaz ücretini geçerli ağ koşullarına göre güncelledik ve onu en az %50 oranında artırdık (ağ tarafından gerekli olan).", "low": "Düşük", "medium": "Orta", "high": "<PERSON><PERSON><PERSON><PERSON>", "market": "<PERSON><PERSON><PERSON>", "aggressive": "Agresif", "low_fee_warning": "İşlem sürenizi not alın. Gelecekteki işlemler bu işlemden sonra kuyruğa alınacaktır.", "edit_priority": "Önceliği düzenle", "speed_up_transaction": "İşlemi hızlandır", "cancel_transaction": "İşlemi iptal et", "new_gas_fee": "<PERSON>ni gaz ücreti", "edit_suggested_gas_fee": "Önerilen gaz ücretini düzenle", "gas_price": "Gaz fiyatı", "learn_more_gas_limit_legacy": "Gaz limiti kullanmak istediğiniz maksimum gaz birimidir. Gaz birimleri “Gaz fiyatı” çarpanıdır. ", "learn_more_gas_price": "Bu ağ için bir işlem gönderirken “Gaz fiyatı” alanı gereklidir. Gaz fiyatı gaz birimi başına ödemek istediğiniz maksimum tutardır.", "gas_price_low": "Gaz fiyatı geçerli ağ koşulları için düşük", "gas_price_high": "Gaz fiyatı gerekenden yüksek"}, "transaction_review_eip1559": {"estimated_gas_fee": "<PERSON><PERSON><PERSON>az <PERSON>", "network_fee": "<PERSON><PERSON>", "max_fee": "Maks. <PERSON>", "total": "Toplam", "max_amount": "<PERSON><PERSON><PERSON> tutar", "estimated_gas_fee_tooltip": "Gaz ücretleri nedir?", "estimated_gas_fee_tooltip_text_1": "Gaz ücretleri", "estimated_gas_fee_tooltip_text_2": " Ethereum", "estimated_gas_fee_tooltip_text_3": "ağında işlem gerçekleştiren kripto madencilerine ödenir.", "estimated_gas_fee_tooltip_text_4": "MetaMask gaz ücretlerinden kâr elde etmez.", "estimated_gas_fee_tooltip_text_5": "Gaz ücretleri ağ tarafından belirlenir ve ağ trafiğine ve işlemin karmaşıklığına göre dalgalanır.", "learn_more": "Gaz ücretleri hakkında daha fazla bilgi alın", "legacy_gas_suggestion_tooltip": "Bu gaz ücreti yanlış olabilen eski gaz tahmininin kullanılmasını öneriyor."}, "times_eip1559": {"unknown": "Bilinmeyen işlem süresi", "maybe": "Belki şu süre içinde", "likely": "Büyük olasılıkla şu süre içinde <", "likely_in": "Büyük olasılıkla şu süre içinde ", "very_likely": "Çok büyük olasılıkla şu süre içinde <", "at_least": "En az", "less_than": "En fazla", "warning_very_likely": "Bu gaz ücreti mevcut olan diğer seçeneklerden çok daha yüksek.", "warning_very_likely_title": "Gaz ücretleri çok yüksek", "warning_unknown": "Maks. ücret veya maks. öncelik ücretiniz geçerli piyasa koşulları için düşük olabilir. İşleminizin ne zaman gerçekleşeceğini (veya gerçekleşip gerçekleşmeyeceğini) bilmiyoruz.", "warning_low": "<PERSON><PERSON>, maksimum ücretinizi düşürür ama ağ trafiği artarsa işleminiz gecikebilir veya başarısız olabilir.", "warning_low_title": "Düşük öncelik", "warning_unknown_title": "Bilinmeyen işlem süresi"}, "review_prompt": {"high_fees": "Ücretler neden bu kadar yü<PERSON>?", "missing_tokens": "Tokenlerim eksik...", "swap_issues": "Swap gerçekleştiremiyorum...", "mobile_sentiment": "MetaMask mobil hakkında ne düşünüyorsunuz?", "sentiment_good_face": "😁", "sentiment_bad_face": "☹️", "sentiment_good": "Bayıldım!", "sentiment_bad": "Çok iyi değil...", "help_title": "<PERSON><PERSON><PERSON>, olamaz! Nasıl yardımcı olabiliriz?", "help_description_1": "Yardımcı olmak için buradayız! Aşağıda SSS bölümünü inceleyin veya", "help_description_2": "destek bölümü ile iletişime geçerek", "help_description_3": " yardım alın!"}, "nickname": {"add_nickname": "Takma ad ekle", "edit_nickname": "Takma adı düzenle", "save_nickname": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>", "name": "Takma Ad", "name_placeholder": "Bu adrese bir takma ad ekle", "contract": "S<PERSON>zleşme", "nickname": "takma ad"}, "network_information": {"things_to_keep_in_mind": "<PERSON><PERSON><PERSON><PERSON> tutulması gerekenler", "testnet_network": "{{type}} Test Ağı", "first_description": "Bu a<PERSON><PERSON> token {{ticker}}. Bu, gaz ücretleri için kullanılan token'dır.", "second_description": "Varlıkları doğrudan bir ağdan diğerine göndermeye çalışırsan bu, kalı<PERSON><PERSON> varlık kaybına neden olabilir. Bir köprü kullandığından emin ol.", "third_description": "Tokenleriniz cüzdanınızda otomatik olarak görünmeyebilir.", "private_network": "Bu ağ bilinmiyor ve gaz ücretleri için özel bir token kullanabilir.", "unknown_network": "Bilinmeyen ağ", "switched_network": "Şuna geçiş yaptın:", "learn_more": "<PERSON><PERSON> fazla bilgi", "add_token": "Tokenleri manuel olarak eklemek için buraya tıklayın", "add_token_manually": "<PERSON> o<PERSON>.", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "error_title": "<PERSON> aksi, bir <PERSON><PERSON><PERSON> ters gitti.", "error_message": "Ağ bilgileri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar dene.", "private_network_third_description": "Tanımlamayı kolaylaştırmak için lütfen bu ağ için bir ad gir.", "learn_more_url": "https://support.metamask.io/networks-and-sidechains/managing-networks/user-guide-custom-networks-and-sidechains/", "enable_token_detection": "Otomatik token algılamayı etkinleştir", "token_detection_mainnet_title": "Token algılama etkinleştirildi böylece pek çok token otomatik olarak cüzdanınızda gösterilecek.", "token_detection_mainnet_link": "<PERSON> ekleyebilirsiniz.", "or": "veya", "non_evm_first_description": "<PERSON><PERSON> a<PERSON><PERSON> yer<PERSON> varlık {{ticker}}. İşlem ücretleri için kullanılır.", "non_evm_second_description": "Başka bir ağdan veya başka bir ağa göndermeye çalışırsanız varlıklarınızı kaybedersiniz."}, "download_files": {"error": "Ek indirilemedi.", "unknownError": "Bilinmeyen bir hata o<PERSON>"}, "remember_me": {"enable_remember_me": "Beni Hatırla özelliğini aç", "enable_remember_me_description": "Beni Hatırla özelliği açıldığında telefonunuza erişimi olan herkes MetaMask hesabınıza erişim sağlayabilir."}, "turn_off_remember_me": {"title": "Beni Hatırla özelliğini kapatmak için şifrenizi girin", "placeholder": "Şifre", "description": "Bu seçeneği kapatırsanız şu andan itibaren MetaMask'in kilidini açmak için şifrenizi girmeniz gerekecektir.", "action": "Beni Hatırla özelliğini kapat"}, "dapp_connect": {"warning": "Bu özelliği kullanabilmek için lütfen uygulamayı en yeni sürüme güncelleyin."}, "confirmation_modal": {"cancel_cta": "İptal et", "confirm_cta": "<PERSON><PERSON><PERSON>"}, "automatic_security_checks": {"title": "Otomatik güvenlik kontrolleri", "description": "Güncellemelerin otomatik kontrolü IP adresini GitHub sunucularında açığa çıkarabilir. Bu durum sadece IP adresinin MetaMask kullandığını gösterir. Başka hiçbir bilgi ya da hesap adresi açığa çıkmaz."}, "terms_of_use_modal": {"title": "Kullanım Şartları bölümümüzü inceleyin", "terms_of_use_check_description": "MetaMask ve tüm özelliklerini kullanırken geçerli olan Kullanım Koşulları bölümünü kabul ediyorum", "accept_cta": "Kabul ediyorum", "accept_helper_description": "<PERSON><PERSON><PERSON> bölümleri okumak için lütfen kaydırın", "agree_cta": "Kabul Ediyorum"}, "update_needed": {"title": "En yeni özelliklere sahip olun", "description": "Cüzdanını<PERSON><PERSON>ha <PERSON>, daha sorun<PERSON>z hale getirdik ve bazı yeni özellikler ekledik. Korunmaya devam etmek ve en yeni iyileştirmelerimizi kullanmak için şimdi güncelleyin.", "primary_action": "En son s<PERSON><PERSON><PERSON><PERSON>"}, "enable_automatic_security_check_modal": {"title": "Güvenlik güncellemelerini otomatik kontrol et?", "description": "Güncellemelerin otomatik kontrolü IP adresini GitHub sunucularında açığa çıkarabilir. Bu durum sadece IP adresinin MetaMask kullandığını gösterir. Başka hiçbir bilgi ya da hesap adresi açığa çıkmaz.", "primary_action": "Otomatik güvenlik kontrollerini etkinleştir", "secondary_action": "Hayır, teşekkürler"}, "contract_allowance": {"custom_spend_cap": {"max": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Harcama ü<PERSON> limiti", "use_site_suggestion": "Sitenin önerisini kullanın", "from_your_balance": "varsayılanı kulanın.", "default_error_message": "<PERSON><PERSON><PERSON> anda ya da gelecekte üçüncü taraf harcaması konusunda rahat olduğunuz bir değer girin. Harcama üst limitini daha sonra dilediğiniz zaman artırabilirsiniz.", "this_contract_allows": "<PERSON><PERSON>, sitenin geçerli bakiyenizden", "amount_greater_than_balance": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, üst limite ulaşana kadar ya da siz harcama üst limitini iptal edene kadar üçüncü tarafın tüm token bakiyenizi harcamasına izin verir. Amacınız bu değilse daha düşük bir harcama üst limiti ayarlamayı deneyin.", "info_modal_description_default": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ba<PERSON>ka herhangi bir uyarı ya da rıza olmaksızın tüm token bakiyenizi harcayabilir. Düşük bir harcama limiti belirleyerek kendinizi koruyun.", "set_spend_cap": "Bir harcama limiti belirleyin", "be_careful": "<PERSON><PERSON><PERSON><PERSON> olun", "error_enter_number": "Hata: <PERSON><PERSON><PERSON> sayı girin", "enter_number": "Buraya bir sayı girin", "learn_more": "<PERSON>ha fazla bilgi edin"}, "token_allowance": {"verify_third_party_details": "Üçüncü taraf bilgilerini doğrula", "protect_from_scams": "Kendinizi dolandırıcılardan korumak için bir dakikanızı ayırarak üçüncü taraf bilgilerini doğrulayın.", "learn_to_verify": "Üçüncü taraf bilgilerinin nasıl doğrulanacağını öğrenin", "spending_cap": "harca<PERSON>i", "access": "<PERSON><PERSON><PERSON><PERSON>", "nft_contract": "NFT sözleşmesi", "token_contract": "<PERSON><PERSON>mes<PERSON>", "third_party_requesting_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> taraf talebi {{action}}", "third party": "üçüncü taraf", "address": "<PERSON><PERSON>"}}, "restore_wallet": {"restore_needed_title": "<PERSON><PERSON> g<PERSON>", "restore_needed_description": "Bir sorunla karşılaşıldı ama endişelenme! Cüzdanını geri geri yüklemeye çalışalım.", "restore_needed_action": "Cüzdanı geri yükle"}, "wallet_restored": {"wallet_restored_title": "Cüzdanınız hazır!", "wallet_restored_action": "Cüzdana devam et", "wallet_restored_description_part_one": "Bazı varlıklarını, ağlarını ve ayarlarını tekrar manuel olarak eklemen gerekebilir.", "wallet_restored_description_part_two": "Bir dakikanı ayırarak", "wallet_restored_description_link": "<PERSON><PERSON><PERSON>fadeni yedekle", "wallet_restored_description_part_three": "çünkü gelecekte cüzdanını tekrar geri yüklemen gerekebilir."}, "new_wallet_needed": {"new_wallet_needed_title": "<PERSON><PERSON> cüzda<PERSON> gere<PERSON>li", "new_wallet_needed_create_new_wallet_action": "<PERSON>ni bir cüzdan oluştur", "new_wallet_needed_create_try_again_action": "Cüzdanı kurtarmayı dene", "new_wallet_needed_description_part_one": "Cüzdanınızla ilgili bir sorun oluştu ve yeni bir tane oluşturmanız gerekecek. Hesaplarınız blokzincirinde olduğu için hala güvende. Sadece cihazınızda kayıtlı olan terc<PERSON>, ka<PERSON><PERSON><PERSON>n ağlar, hesap adları ve ilgili veriler kayboldu.", "new_wallet_needed_description_part_two": "Hesapları yeni bir cüzdana aktarmak için Gizli Kurtarma İfadene ihtiyacın olacak. Gizli Kurtarma İfaden yoksa hesaplarını içe aktaramayacaksın.", "new_wallet_needed_description_part_three": "<PERSON><PERSON><PERSON> tekrar olmasını önlemek için her zaman MetaMask uygulamanı ve işletim sistemini en yeni sürümde güncel tuttuğundan emin ol."}, "srp_security_quiz": {"title": "Güvenlik testi", "introduction": "Gizli Kurtarma İfadenizi görmek için iki soruyu doğru cevaplamanız gerekmektedir", "get_started": "Başlarken", "learn_more": "<PERSON>ha fazla bilgi edin", "try_again": "<PERSON><PERSON><PERSON> dene", "continue": "<PERSON><PERSON> et", "of": "/", "question_one": {"question": "G<PERSON><PERSON> Kurtarma İfadenizi kaybederseniz MetaMask...", "right_answer": "Size yardımcı olamaz", "wrong_answer": "Size onu tekrar verebilir", "right_answer_title": "Doğru! Hiç kimse Gizli Kurtarma İfadenizi geri almanıza yardımcı olamaz", "right_answer_description": "<PERSON>ir yere yazın, bir metalin üzerine kazıyın veya asla kaybetmemeniz için birden fazla noktada saklayın. Kaybederseniz sonsuza dek kaybolur.", "wrong_answer_title": "Yanlış! Hiç kimse Gizli Kurtarma İfadenizi geri almanıza yardımcı olamaz", "wrong_answer_description": "Gizli Kurtarma İfadenizi kaybederseniz sonsuza dek kaybolur. Söylediklerinin ne olduğuna bakılmaksızın hiç kimse onu geri almanıza yardımcı olamaz."}, "question_two": {"question": "<PERSON><PERSON><PERSON>, bir destek temsilcisi bile sizden Gizli Kurtarma İfadenizi isterse...", "right_answer": "Dolandırılıyorsunuzdur", "right_answer_title": "Doğru! Gizli Kurtarma İfadenizi paylaşmak asla iyi bir fikir <PERSON>dir", "right_answer_description": "Gizli Kurtarma İfadenizi isteyen kişi size yalan söylüyordur. Kendisi ile paylaşırsanız varlıklarınızı çalacaktır.", "wrong_answer": "<PERSON><PERSON><PERSON> ve<PERSON>", "wrong_answer_title": "Hayır! Gizli Kurtarma İfadenizi asla hiç kimse ile paylaşmayın, asla", "wrong_answer_description": "Gizli Kurtarma İfadenizi isteyen kişi size yalan söylüyordur. Kendisi ile paylaşırsanız varlıklarınızı çalacaktır."}}, "ledger": {"open_settings": "Ayarları aç", "view_settings": "Ayarları görüntüle", "bluetooth_off": "Bluetooth kapalı", "bluetooth_off_message": "Lütfen cihazınızda Bluetooth özelliğini açın", "bluetooth_access_blocked": "<PERSON>ger, mobil cihazınızla eşleşmek için Bluetooth erişimine ihtiyaç duyuyor.", "bluetooth_access_blocked_message": "Bluetooth kullanarak Ledger cihazını eşleştirmek istiyorsanız Ayarlar kısmında Bluetooth'u etkinleştirip tekrar deneyin.", "location_access_blocked": "MetaMask, ledger'ınızla eşleştirmek için konum erişimi iznine ihtiyaç duyuyor.", "location_access_blocked_message": "Bluetooth kullanarak Ledger cihazınızı eşleştirmek istiyorsanız Ayarlar kısmında konum erişimini etkinleştirip tekrar denemeniz gerekecek.", "nearbyDevices_access_blocked": "<PERSON><PERSON><PERSON><PERSON>, Ledger'ınızla eşleştirmek için yakındaki cihazlar iznine ihtiyaç duyuyor.", "nearbyDevices_access_blocked_message": "Bluetooth kullanarak Ledger cihazınızı eşleştirmek istiyorsanız Ayarlar kısmında yakındaki cihazlar erişimini etkinleştirip tekrar denemeniz gerekli.", "bluetooth_scanning_error": "<PERSON><PERSON>az ta<PERSON>ı<PERSON> hata", "bluetooth_scanning_error_message": "Lütfen cihazınızın kilidinin açık olduğundan ve Ethereum uygulamasının çalıştığından emin olun", "bluetooth_connection_failed": "Bluetooth bağlantısı başarısız oldu", "bluetooth_connection_failed_message": "Lütfen Ledger'ınızın kilidinin açık olduğundan ve Bluetooth özelliğinin etkin olduğundan emin olun", "ethereum_app_open": "Ledger onaya ihtiyaç duyuyor", "ethereum_app_open_message": "Ethereum uygulamasını açmak için lütfen cihazınızda onaylayın. Yapıktan sonra Tamam düğ<PERSON> basın.", "ethereum_app_unconfirmed_error": "Ethereum uygulamasını açma talebini reddettiniz.", "failed_to_open_eth_app": "Ethereum uygulaması açılamadı.", "ethereum_app_open_error": "Lütfen Ledger cihazınızda Ethereum uygulamasını yükleyin/kabul edin.", "running_app_close": "Çalışan uygulama kapatılamadı.", "running_app_close_error": "Ledger cihazınızda çalışan uygulama kapatılamadı.", "ethereum_app_not_installed": "Ethereum uygulaması yüklü değil.", "ethereum_app_not_installed_error": "Lütfen Ledger cihazınıza Ethereum uygulamasını yükleyin.", "ledger_is_locked": "<PERSON><PERSON> kilitli", "unlock_ledger_message": "Lütfen Ledger cihazınızın kilidini açın", "cannot_get_account": "<PERSON><PERSON><PERSON>", "connect_ledger": "Led<PERSON><PERSON><PERSON> bağla", "looking_for_device": "Cihaz aranıyor", "ledger_reminder_message": "Lütfen Ledger cihazınızın şöyle olduğundan emin olun:", "ledger_reminder_message_step_one": "1. <PERSON>ger cihazınızn kilidini açın", "ledger_reminder_message_step_two": "2. Ethereum uygulamasını yükleyin ve açın", "ledger_reminder_message_step_three": "Bluetooth'u etkinleş<PERSON>rin", "ledger_reminder_message_step_four": "4. <PERSON><PERSON>, hassas konum kullan özelliği açık olarak etkinleştirildi", "ledger_reminder_message_step_four_Androidv12plus": "4. Yakındaki cihazlar etkinleştirildi", "ledger_reminder_message_step_five": "5. <PERSON><PERSON><PERSON><PERSON><PERSON> etme modu kapatılmalı", "blind_signing_message": "6. <PERSON><PERSON> \"kö<PERSON>\" özelliğini etkinleştirin.", "available_devices": "Kullanılabilir cihazlar", "retry": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON> et", "confirm_transaction_on_ledger": "Ledger'ınızda işlemi onaylayın", "bluetooth_enabled_message": "Bluetooth özelliğinin etknleştirilmiş olduğundan emin olun", "device_unlocked_message": "Cihazın kilidi açık", "ledger_disconnected": "Cihazınızın bağlantısı kesildi", "ledger_disconnected_error": "Cihazınız ile bağlantı kaybedildi. Lütfen tekrar deneyin.", "unknown_error": "Beklenmedik hata o<PERSON>.", "unknown_error_message": "Beklenmeyen bir hata oluş<PERSON>. Lütfen tekrar deneyin.", "error_occured": "<PERSON>ir hata o<PERSON>", "how_to_install_eth_app": "Ledger cihazına Ethereum uygulaması nasıl yüklenir?", "ledger_account_count": "Ledger cihazınızda MetaMask Mobil ile 1 hesap kullanıyorsunuz.", "open_eth_app": "Lütfen Ethereum uygulamasını açın", "open_eth_app_message_one": "Ethereum uygulamasının yüklü olduğunu ancak açık olmadığını tespit ettik; ", "open_eth_app_message_two": "devam etmek için lütfen cihazdaki iki düğmeye basarak Ethereum uygulamasını açma komutunu kabul edin.", "toast_bluetooth_connection_error_title": "<PERSON> aksi, bir <PERSON><PERSON><PERSON> ters gitti :/", "toast_bluetooth_connection_error_subtitle": "Ledger'a bağlanılmadı", "try_again": "<PERSON><PERSON><PERSON> dene", "forget_device": "Led<PERSON>'<PERSON> unut", "sign_with_ledger": "Ledger ile oturum açın", "ledger_pending_confirmation": "Ledger <PERSON>", "ledger_pending_confirmation_error": "Ledger cihazınızda bekleyen bir eylem var. Lütfen önce eylemi temizleyip ardından tekrar deneyin.", "not_supported": "İşlem desteklenmiyor", "not_supported_error": "G<PERSON>len veri imzalama işleminde sadece sürüm 4 desteklenmektedir.", "error_during_connection": "Bilinmeyen bir hata o<PERSON>", "error_during_connection_message": "Ledger cihazınıza bağlanırken küçük bir sorun olu<PERSON>, bunu tekrar denemek iç<PERSON> a<PERSON><PERSON> \"Tekrar Dene\" d<PERSON><PERSON><PERSON><PERSON> dokunun. Bazen MetaMask Mobil ile eşleştirme sürecinin başlangıcında Ledger cihazında ETH uygulamasının açık olması nedeniyle bu durum meydana gelir.", "how_to_install_eth_webview_title": "Ethereum uygulaması nasıl yüklenir?", "nonce_too_low": "Nonce çok düşük", "nonce_too_low_error": "Ayarlanan nonce çok düşük", "select_accounts": "<PERSON><PERSON> hesap seç", "select_hd_path": "HD Yolunu seç", "select_hd_path_description": "Beklediğiniz hesapları görmüyorsanız HD yoluna veya geçerli seçilen ağa geçmeyi deneyin.", "ledger_live_path": "Ledger Live", "ledger_legacy_path": "Eski Adı (MEW/MyCrypto)", "ledger_bip44_path": "BIP44 (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "ledger_legacy_label": " (eski)", "blind_sign_error": "<PERSON><PERSON><PERSON>ı", "blind_sign_error_message": "<PERSON><PERSON>r imzalama Ledger cihazınızda etkinleştirilmemiş. Lütfen ayarlar kısmında etkinleştirin.", "user_reject_transaction": "Kullanıcı işlemi reddetti", "user_reject_transaction_message": "Kullanıcı Ledger cihazında işlemi reddetti.", "multiple_devices_error_message": "Birden fazla cihaz henüz desteklenmiyor. Yeni bir Ledger cihaz eklemek için eskisini kaldırmanız gereklidir.", "hd_path_error": "HD Yolu geçersiz: {{path}}", "unspecified_error_during_connect": "Ledger Donanımı bağlanırken belirtilmemiş hata,", "account_name_existed": "{{accountName}} adlı hesap zaten mevcut"}, "account_actions": {"edit_name": "<PERSON><PERSON><PERSON> adı<PERSON>", "add_account_or_hardware_wallet": "Hesap veya donanım cüzdanı ekleyin", "connect_hardware_wallet": "<PERSON><PERSON> hesabı bağla", "import_wallet_or_account": "Bir cüzdanı veya hesabı içe aktar", "add_account": "<PERSON><PERSON> bir he<PERSON><PERSON>", "add_multichain_account": "Yeni bir {{networkName}} hesabı oluştur", "create_an_account": "<PERSON><PERSON> bir he<PERSON><PERSON>", "add_new_account": "Ethereum hesabı", "create_new_wallet": "<PERSON>ni bir cüzdan oluştur", "import_wallet": "Bir cüzdanı içe aktar", "import_srp": "<PERSON><PERSON><PERSON>", "add_hardware_wallet": "Donanım cüzdanı", "import_account": "<PERSON><PERSON>", "add_bitcoin_account": "Bitcoin hesabı", "add_solana_account": "<PERSON><PERSON> hesa<PERSON>ı", "switch_to_smart_account": "Akıllı Hesaba Geçiş Yap", "rename_account": "Hesabı yeniden adlandır", "addresses": "<PERSON><PERSON><PERSON>", "headers": {"bitcoin": "Bitcoin", "solana": "Solana"}}, "show_nft": {"show_nft_title": "NFT'y<PERSON>", "show_nft_content_1": "IPFS'de depolanan NFT'lerinizin görüntülerini göstermek, tarayıcınızın adres çubuğuna girilen ENS adresleri ile ilgili bilgileri göstermek ve farklı tokenlerin simgelerini almak için üçüncü taraf hizmetleri kullanırız. Siz bu özellikleri kullanırken IP adresiniz bu hizmetlerle paylaşılabilir.", "show_nft_content_2": "Seçiliyor", "show_nft_content_3": "<PERSON><PERSON><PERSON>", "show_nft_content_4": "IPFS çözünürlüğünü açar. Dilediğiniz zaman", "show_nft_content_5": "Ayarlar > Güvenlik ve gizlilik", "show_nft_content_6": "kapatabilirsiniz."}, "show_display_nft_media": {"show_display_nft_media_title": "NFT medyasını görüntüle", "show_display_nft_media_content_1": "Bir NFT'yi görmek için şunu açmanız gerekir:", "show_display_nft_media_content_2": "NFT medyasını görüntüle.", "show_display_nft_media_content_3": "NFT medyasını ve verilerini göstermek IP adresinizi OpenSea veya diğer üçüncü taraflara ifşa edebilir. NFT otomatik algılama bu özelliğe dayanır ve bu kapatıldığında kullanılamaz.", "show_display_nft_media_content_4": "NFT medyasını Görüntüle seçeneğini şuradan kapatabilirsiniz:", "show_display_nft_media_content_5": "Ayarlar > Güvenlik ve gizlilik."}, "ipfs_gateway_banner": {"ipfs_gateway_banner_title": "IPFS ağ geçidi", "ipfs_gateway_banner_content1": "Bu bir IPFS web sitesidir. Bu siteyi görmek için", "ipfs_gateway_banner_content2": "IPFS ağ geçidi", "ipfs_gateway_banner_content3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ipfs_gateway_banner_content4": "<PERSON><PERSON><PERSON>."}, "ipfs_gateway": {"ipfs_gateway_title": "IPFS ağ geçidi", "ipfs_gateway_content1": "IPFS'de depolanan NFT'lerinizin görüntülerini göstermek, tarayıcınızın adres çubuğuna girilen ENS adresleri ile ilgili bilgileri göstermek ve farklı token'lerin simgelerini almak için üçüncü taraf hizmetleri kullanıyoruz. Siz bunları kullanırken IP adresiniz bu hizmetlerle paylaşılabilir.", "ipfs_gateway_content2": "IPFS çözünürlüğünü dilediğiniz zaman", "ipfs_gateway_content3": "Ayarlar > Güvenlik ve gizlilik", "ipfs_gateway_content4": "menüsünden kapatabilirsiniz"}, "install_snap": {"title": "Bağlantı talebi", "description": "{{origin}}, {{snap}} kullanmak istiyor.", "permissions_request_title": "<PERSON><PERSON>bi", "permissions_request_description": "{{origin}}, {{snap}} adlı snap'i yüklemek istiyor ve bu snap aşağıdaki izinleri talep ediyor.", "approve_permissions": "<PERSON><PERSON><PERSON>", "installed": "Yüklendi", "install_successful": "{{snap}} b<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>.", "okay_action": "<PERSON><PERSON>", "error_title": "Yükleme başarısız oldu", "error_description": "{{snap}} y<PERSON><PERSON><PERSON><PERSON> başarısız oldu."}, "earn": {"empty_state_cta": {"heading": "{{tokenSymbol}} borç verin ve kazanın", "body": "{{protocol}} ile {{tokenSymbol}} token'ınızı borç verin ve", "annually": "yıll<PERSON>k olarak kazanın.", "learn_more": "Daha fazla bilgi edinin.", "earn": "Kazan"}, "service_interruption_banner": {"maintenance_message": "Bakım çalışması nedeniyle kapalıyız. <PERSON><PERSON>sa sürede tekrar burada olacağız!"}, "deposit": "Para Yatır", "approve": "<PERSON><PERSON><PERSON>", "approval": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal et", "transaction_submitted": "İşlem Gönderildi", "every_minute": "Her dakika", "immediate": "<PERSON><PERSON>", "apr": "APR", "protocol": "Protokolünden gelir", "receive_tooltip": "araç ipucunu al", "button": "<PERSON><PERSON><PERSON><PERSON>", "receive": "Al", "tooltip_content": {"apr": {"part_one": "Geçen haftanın ödül oranına göre para yatırma işleminizin değerinde beklenen yıllık artış.", "part_two": "Not: APR zaman içinde değişir."}, "protocol": "<PERSON><PERSON><PERSON> verme <PERSON>, token'leri öd<PERSON>nç vererek ödül kazanmanıza olanak sağlayan bir akıllı sözleşmedir. <PERSON><PERSON>ı zamanda kullanı<PERSON>ın, borç verenlere ödenen bir ücret karşılığında başka token'leri teminat olarak göstererek token ödünç almasına da olanak sağlar.", "reward_frequency": "Ödüllerinizin hesaplanma sıklığı.", "withdrawal_time": "Token'inizi protokolden çekip cüzdanınıza geri almanızın süresi", "receive": "Bu token, varlıklarınızı ve ödüllerinizi takip etmek için kullanılır. Bunu transfer etmeyin veya bununla işlem yapmayın, aksi takdirde varlıklarınızı çekemezsiniz.", "health_factor": {"your_health_factor_measures_liquidation_risk": "Sağlık Faktörünüz likidasyon riskini ölçer", "above_two_dot_zero": "2,0 üzeri", "safe_position": "<PERSON><PERSON><PERSON><PERSON> pozisyon", "between_one_dot_five_and_2_dot_zero": "1,5-2,0 a<PERSON>ı", "medium_liquidation_risk": "<PERSON><PERSON> Likidas<PERSON>", "below_one_dot_five": "1,5 altı", "higher_liquidation_risk": "Yüksek likidasyon riski"}, "lending_risk_aware_withdrawal_tooltip": {"why_cant_i_withdraw_full_balance": "<PERSON>en tüm bakiyemi çekemiyorum?", "your_withdrawal_amount_may_be_limited_by": "Para çekme tutarınız şununla sınırlı olabilir", "pool_liquidity": "<PERSON><PERSON><PERSON>", "not_enough_funds_available_in_the_lending_pool_right_now": "<PERSON><PERSON> anda borç verme havuzunda yeterli para yok.", "existing_borrow_positions": "Mevcut Borç Alma Pozisyonları", "withdrawing_could_put_your_existing_loans_at_risk_of_liquidation": "Para çekmek, mevcut kredi pozisyonlarınızı likidasyon riskine atabilir."}}, "withdraw": "Çek", "deposit_more": "<PERSON>ha fazla para yatır", "earning": "Kazanç", "withdrawal_time": "Para çekme zamanı", "withdrawing_to": "Şuraya çek:", "network": "Ağ", "health_factor": "Sağlık faktörü", "liquidation_risk": "Likidasyon riski", "insufficient_pool_liquidity": "<PERSON><PERSON><PERSON>", "available_to_withdraw": "çekilebilir", "unknown": "bilinmeyen", "how_it_works": "Nasıl çalışır?", "market_historic_apr_modal": {"earn_rewards_on_your_token": "{{tokenSymbol}} token'ınızda ödül kazanın", "lend_and_earn_daily_rewards": "{{protocol}} ile {{tokenSymbol}} token'ınızı borç verin ve günlük ödül kazanın. Ödüller zaman içinde büyür ve APR değişiklik gösterir.", "withdraw_whenever_you_want": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get_asset_back_in_your_wallet_instantly": "{{tokenSymbol}} token'ı anında cüzdanınıza geri alın."}, "amount_exceeds_safe_withdrawal_limit": "Tutar güvenli para çekme limitini aşıyor", "view_earnings_history": {"lending": "Pozisyon geçmişini görüntüle", "staking": "Kazanç geçmişini görüntüle"}, "earnings_history_list_title": {"lending": "Pozisyon geçmişi", "staking": "Ödeme geçmişi"}, "allowance_reset": "Ödenek Sıfırlama"}, "stake": {"stake": "Pay", "earn": "Kazan", "stake_eth": "ETH Stake Et", "unstake_eth": "ETH Unstake Et", "staked_balance": "Stake edilen bakiye", "staked_ethereum": "Stake Edilen Ethereum", "unstake": "Unstake", "stake_more": "Daha fazla stake et", "claim": "Al", "your_earnings": "Kazançlarınız", "annual_rate": "Yıllık oran", "lifetime_rewards": "<PERSON><PERSON><PERSON>", "estimated_annual_earnings": "<PERSON><PERSON><PERSON> yıllık kazançlar", "accessibility_labels": {"stake_annual_rate_tooltip": "Yıllık oran araç ipucu"}, "estimated_annual_rewards": "<PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON><PERSON>", "estimated_annual_reward": "<PERSON><PERSON><PERSON> yıllık ödül", "reward_frequency": "Öd<PERSON>l sıklığı", "reward_frequency_tooltip": "Stake edilen bakiyeniz yeni ödülleri belirlemek için {{frequency}} aralıklarla güncellenir.", "withdrawal_time": "Para çekme zamanı", "metamask_pool": "MetaMask Havuzu", "enter_amount": "Tutar gir", "review": "<PERSON><PERSON><PERSON>", "not_enough_eth": "Yeterli ETH yok", "not_enough_token": "<PERSON><PERSON><PERSON> {{ticker}} yok", "balance": "Bakiye", "stake_eth_and_earn": "ETH stake et ve kazan", "how_it_works": "Nasıl çalışır?", "stake_any_amount_of_eth": "Dilediğiniz tutarda ETH stake edin.", "no_minimum_required": "Gerekli minimum yoktur.", "earn_eth_rewards": "ETH ödüllerini kazan.", "earn_eth_rewards_description": "Stake eder etmez ka<PERSON> başlayın. <PERSON><PERSON><PERSON>ller otomatik olarak birleştirilir.", "flexible_unstaking": "Esnek unstake.", "flexible_unstaking_description": "Dilediğiniz zaman unstake edin. Gerçekleşmesi genellikle 3 günden kısa sürer ancak 11 güne kadar da sürebilir.", "disclaimer": "<PERSON><PERSON>, ödülleri garanti etmez ve para kaybını da içeren riskler taşır.", "learn_more": "Daha fazla bilgi edinin", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "your_balance": "Bakiyeniz", "stake_your_eth_cta": {"base": "MetaMask Havuzu ile ETH'nizi stake edin ve", "annually": "yıllık kazanç sağlayın.", "learn_more_with_period": "Daha fazla bilgi edinin."}, "day": {"zero": "", "one": "g<PERSON>n", "other": "g<PERSON>n"}, "hour": {"zero": "", "one": "saat", "other": "saat"}, "minute": {"zero": "", "one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "banner_text": {"has_claimable_eth": "{{amountEth}} ETH alabilirsiniz. Alındıktan sonra cüzdanınıza ETH alacaksınız.", "unstaking_in_progress": {"base": "{{amountEth}} ETH unstake işlemi sürüyor. <PERSON><PERSON> kadar süre sonra tekrar gelin:", "and": "ve", "to_claim_it": "alın.", "default": "{{amountEth}} ETH unstake işlemi devam ediyor. Almak için birkaç gün sonra tekrar gelin."}, "geo_blocked": "Unstake veya stake işlemlerine izin verilmeyen bir bölgedesiniz.", "approximately": "yaklaşık"}, "unstake_input_banner_description": "Unstake edilen ETH'nin alınabilir olması ortalama 3 günden kısa sürer ancak 11 güne kadar sürebilir.", "max": "<PERSON><PERSON><PERSON><PERSON>", "staking_from": "Stake edilecek yer", "advanced_details": "Gelişmiş bilgiler", "ethereum_mainnet": "Ethereum Ana Ağı", "interacting_with": "Etkileşimde", "12_hours": "12 saat", "terms_of_service": "Hizmet şartları", "risk_disclosure": "Risk açı<PERSON>", "cancel": "İptal et", "confirm": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON> et", "estimated_changes": "<PERSON><PERSON><PERSON>", "you_receive": "Aldığınız", "up_to_n": "En fazla {{count}}", "unstaking_to": "Unstake alıcısı", "claiming_to": "Alıcı hesap", "max_modal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "eth": {"description": "<PERSON><PERSON><PERSON><PERSON>, sahip olduğunuz toplam ETH tutarından stake edilmesi gereken gaz ücretinin düşülmüş halidir. Gelecekteki işlemler için cüzdanınızda bir miktar ekstra ETH tutmanız iyi bir fikir olacaktır."}}, "use_max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "estimated_unstaking_time": "1 ila 11 gün", "proceed_anyway": "<PERSON><PERSON> de <PERSON> et", "gas_cost_impact": "Gaz maliyeti etkisi", "select_a_token_to_deposit": "Para yatırmak için bir token seçin", "select_a_token_to_withdraw": "Para çekmek için bir token seçin", "you_could_earn_up_to": "<PERSON><PERSON><PERSON>", "per_year_on_your_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deposit": "Para Yatır", "gas_cost_impact_warning": "Uyarı: <PERSON><PERSON><PERSON>in gaz maliyeti yatırdığınız paranın %{{percentOverDeposit}} fazlası olacaktır.", "earnings_history_title": "{{ticker}} kazançları", "apr": "APR", "interactive_chart": {"timespan_buttons": {"7D": "7G", "1M": "1A", "3M": "3A", "6M": "6A"}}, "today": "<PERSON><PERSON><PERSON><PERSON>", "one_week_average": "1 haftalık ortalama", "one_month_average": "1 aylık ortalama", "three_month_average": "3 aylık ortalama", "six_month_average": "6 aylık ortalama", "one_year_average": "1 yıllık ortalama"}, "default_settings": {"title": "Cüzdanınız hazır", "description": "<PERSON><PERSON><PERSON><PERSON>, gü<PERSON>lik ve kullanım kolaylığını en iyi şekilde dengelemek için varsayılan ayarları kullanır. Gizliliğinizi daha fazla artırmak için bu ayarları değiştirin.", "learn_more_about_privacy": "En iyi gizlilik uygulamaları hakkında daha fazla bilgi edinin.", "privacy_policy": "Gizlilik politikası", "default_settings": "Varsayılan Ayarlar", "done": "<PERSON><PERSON>", "basic_functionality": "Temel işlevsellik", "manage_networks": "Ağınızı seçin", "manage_networks_body": "Ethereum verilerine mümkün olan en güvenilir ve gizli erişimi sunmak amacıyla uzak yordam çağrısı (RPC) sağlayıcısı olarak Infura kullanırız. Kendi RPC'nizi seçebilirsiniz ancak tüm RPC'lerin işlemleri gerçekleştirmek için IP adresinizi ve Ethereum cüzdanınızı alacağını unutmayın. Infura'nın verileri nasıl kullandığı hakkında daha fazla bilgi edinmek için ", "manage_networks_body2": " ve Infura'nın EVM hesaplarının verilerini nasıl ele aldığını öğrenin; Solana hesapları için ise ", "manage_networks_body3": "buraya tıklayın.", "functionality_body": "MetaMask, internet hizmetleri üzerinden token bilgileri ve gaz ayarları gibi temel özellikler sunar. İnternet hizmetlerini kullandığınızda IP adresiniz, bu durumda MetaMask ile, paylaşılır. Bu tıpkı herhangi bir web sitesini ziyaret ettiğinizde olduğu gibidir. MetaMask bu verileri geçici olarak kullanır ve verilerinizi hiçbir zaman satmaz. Bir VPN kullanabilir veya bu hizmetleri kapatabilirsiniz ancak bu durum MetaMask deneyiminizi etkileyebilir. Daha fazla bilgi için ", "functionality_body2": " bölümümüzü inceleyin.", "sheet": {"title_off": "Temel işlevselliği kapat", "description_off": "<PERSON><PERSON>, MetaMask'te zamanınızı tamamen optimize edemeyeceğiniz anlamına gelir. <PERSON>mel özellikler (token bilgileri, en iyi gaz ayarları vb. gibi) sizin için sunulmayacaktır.", "description_off2": "<PERSON><PERSON><PERSON> ka<PERSON>ığınızda şuradaki tüm özellikler de devre dışı bırakılır:", "description_off2_related_features1": "güvenlik ve gizlilik, yedekleme ve senkronizasyon", "description_off2_related_features1_and": "ve", "description_off2_related_features2": "cüzdanınızın kullanımı.", "title_on": "Temel işlevselliği aç", "description_on": "MetaMask'te zamanınızı tamamen optimize etmek için bu özelliği açmanız gerekecektir. Temel özellikler (token bilgileri, en iyi gaz ayarları, bildirimler vb. gibi) web3 deneyimi için önemlidir.", "checkbox_label": "Anlıyorum ve devam etmek istiyorum", "buttons": {"cancel": "İptal", "turn_on": "Aç", "turn_off": "Ka<PERSON><PERSON>", "reset": "Sıfırla"}}, "drawer_general_title": "<PERSON><PERSON>", "drawer_general_title_desc": "Cihazlar genelindeki ayarları senkronize edin, ağ tercihlerini seçin ve token verilerini takip edin", "drawer_assets_title": "Varlıklar", "drawer_assets_desc": "Cüzdanınızdaki tokenler otomatik algılansın, NFT'ler gösterilsin ve toplu hesap bakiye güncellemeleri alınsın", "drawer_security_title": "Güvenlik", "drawer_security_desc": "Güvensiz ağlara katılma şansınızı azaltın ve hesaplarınızı koruyun", "network_details_check_desc": "MetaMask, doğru ve standartlaştırılmış ağ bilgilerini göstermek için chainid.network adlı üçüncü taraf bir hizmet kullanır. Bu, kötü amaçlı veya yanlış ağa bağlanma şansınızı düşürür. Bu özelliği kullanırken IP adresiniz chainid.network ile paylaşılır."}, "simulation_details": {"failed": "Tahmininiz yüklenirken bir hata oldu.", "fiat_not_available": "<PERSON><PERSON><PERSON>", "incoming_heading": "Aldığınız", "no_balance_changes": "Değişiklik yok", "outgoing_heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverted": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON><PERSON>z olması muhtemel", "title": "<PERSON><PERSON><PERSON>", "tooltip_description": "<PERSON><PERSON><PERSON>iklikler bu işlemi gerçekleştirirseniz meydana gelebilecek değişikliklerdir. Bu bir g<PERSON><PERSON>, sadece bir tahm<PERSON>.", "total_fiat": "Toplam = {{currency}}"}, "spam_filter": {"block_origin_requests_for_1_minute": "Bu siteyi geçici olarak engelle", "cancel": "İptal et", "description": "Birden fazla taleple istenmeyen mesaj alıyorsanız siteyi geçici olarak engelleyebilirsiniz.", "got_it": "<PERSON><PERSON><PERSON><PERSON>", "site_blocked_description": "Site 1 dakika boyunca engellenecek.", "site_blocked_title": "Bu siteyi geçici olarak engellediniz", "title": "<PERSON><PERSON> fazla talep fark ettik"}, "common": {"please_wait": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "disconnect_you_from": "Bu, {{dappUrl}} ile bağlantınızı kesecektir", "disconnect": "Bağlantıyı kes"}, "tooltip_modal": {"reward_rate": {"title": "Ödül oranı", "tooltip": "Geçen haftanın ödül oranına göre stake'inizin değerinde beklenen yıllık artış."}, "estimated_gas_fee": {"title": "<PERSON><PERSON><PERSON>az <PERSON>", "gas_recipient": "Gaz ücretleri, Ethereum ağında işlemleri gerçekleştiren kripto madencilerine ödenir. MetaMask gaz ücretlerinden herhangi bir kazanç elde etmemektedir.", "gas_fluctuation": "Gaz ücretleri tahmini olup ağ trafiği ve işlem karmaşıklığına göre dalgalanır.", "gas_learn_more": "Gaz ücretleri hakkında daha fazla bilgi edinin"}, "reward_frequency": {"title": "Öd<PERSON>l sıklığı", "tooltip": "Stake edilen bakiyeniz yeni ödülleri belirlemek için 12 saatte bir güncellenir."}, "unstaking_time": {"title": "Unstake zamanı", "tooltip": "ETH'nizin unstake işlemi genellikle 3 günden kısa sürer ancak gerçekleşmesi 11 güne kadar sürebilir. <PERSON>, unstake ettiğiniz tutara ve ETH stake faaliyetine bağlıdır"}}, "confirm": {"cancel": "İptal et", "confirm": "<PERSON><PERSON><PERSON>", "staking_footer": {"part1": "Devam ederek <PERSON> bölümlerimizi kabul edersiniz: ", "terms_of_use": "Kullanım Şartları", "part2": " ve ", "risk_disclosure": "Risk <PERSON>", "part3": "."}, "label": {"amount": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "balance": "Bakiye", "interacting_with": "Etkileşimde", "network": "Ağ", "primary_type": "<PERSON><PERSON><PERSON><PERSON>", "request_from": "<PERSON><PERSON>", "signing_in_with": "Şununla giriş yapılıyor:", "spender": "<PERSON><PERSON><PERSON>", "now": "Ş<PERSON>di", "switching_to": "Geçiş Yapılan", "bridge_estimated_time": "<PERSON><PERSON><PERSON> s<PERSON>re", "pay_with": "Ödeme a<PERSON>ı", "total": "Toplam", "transaction_fee": "Transaction fee", "metamask_fee": "MetaMask ücreti", "network_fee": "<PERSON><PERSON>", "bridge_fee": "Bridge provider fee"}, "title": {"signature": "<PERSON><PERSON><PERSON>", "permit": "Harcama ü<PERSON> limiti", "permit_revoke": "İzni kaldır", "permit_NFTs": "<PERSON> çekme talebi", "signature_siwe": "<PERSON><PERSON><PERSON>", "contract_interaction": "İşlem talebi", "contract_deployment": "B<PERSON> sözleşme kullanın", "transfer": "Transfer talebi", "switch_account_type": "<PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON>", "perps_deposit": "<PERSON>on e<PERSON>"}, "sub_title": {"permit": "Bu site token'larınızı harcamak için izin istiyor.", "permit_revoke": "<PERSON><PERSON><PERSON>ızdan token harcama iznini kaldırıyorsunuz.", "permit_NFTs": "Bu site NFT'lerinizi çekmek için izin istiyor.", "permit_revoke_NFTs": "Bu site, NFT'leriniz için para çekme limitini sıfırlamak istiyor", "signature": "Onaylamadan önce talep bilgilerini inceleyin.", "signature_siwe": "Bir site, bu hesabın sahibinin siz olduğunuzu kanıtlamak için giriş yapmanızı istiyor.", "contract_interaction": "Onaylamadan önce talep bilgilerini inceleyin.", "switch_to_smart_account": "Akıllı bir hesaba geçiş yapıyorsunuz.", "switch_to_standard_account": "<PERSON><PERSON><PERSON> standart bir hesaba geçiş yapıyo<PERSON>unuz (EOA).", "contract_deployment": "Bu site bir sözleşme kullanmanızı istiyor", "decrease_allowance": "Bu size token'lar<PERSON><PERSON><PERSON><PERSON> için harcama üst limitini düşürmek istiyor."}, "tooltip": {"perps_deposit": {"transaction_fee": "We'll swap your tokens for USDC on HyperEVM, the network used by Perps. Swap providers may charge a fee, but MetaMask won't."}, "title": {"transaction_fee": "<PERSON><PERSON><PERSON>"}}, "spending_cap": "Harcama ü<PERSON> limiti", "withdraw": "Çek", "nfts": "NFT'ler", "permission_from": "İzni veren:", "spender": "<PERSON><PERSON><PERSON>", "request_from": "<PERSON><PERSON>", "staking_from": "Stake edilecek yer", "signing_in_with": "Şununla giriş yapılıyor:", "message": "<PERSON><PERSON>", "personal_sign_tooltip": "Bu site imzanızı istiyor", "transaction_tooltip": "Bu site, işleminizi istiyor", "details": "Ayrıntılar", "qr_get_sign": "İmza Al", "qr_scan_text": "Donanım cüzdanınızla ta<PERSON>ın", "sign_with_ledger": "Ledger ile oturum aç", "smart_account": "Akıllı Hesap", "smart_contract": "Akıllı sözleşme", "standard_account": "<PERSON><PERSON>", "siwe_message": {"url": "URL Adresi", "network": "Ağ", "account": "<PERSON><PERSON><PERSON>", "version": "S<PERSON>r<PERSON><PERSON>", "chain_id": "<PERSON><PERSON><PERSON><PERSON>", "nonce": "<PERSON><PERSON>", "issued": "D<PERSON><PERSON>lendi", "requestId": "<PERSON><PERSON>", "resources": "<PERSON><PERSON><PERSON><PERSON>"}, "simulation": {"decoded_tooltip_bid_nft": "Teklif kabul edildiğinde NFT cüzdanınıza yansıyacak.", "decoded_tooltip_list_nft": "Sadece biri NFT'lerinizi satın alırsa değişiklik olmasını bekleyin.", "edit_value_balance_info": "<PERSON><PERSON><PERSON>:", "info_permit": "Harcama yapan tarafa he<PERSON>ızdan bu kadar token harcama izni veriyorsunuz.", "info_revoke": "<PERSON><PERSON><PERSON>ızdan token harcama iznini kaldırıyorsunuz.", "label_change_type_bidding": "Teklifiniz", "label_change_type_listing": "Listelediğiniz", "label_change_type_nft_listing": "Liste fiyatı", "label_change_type_permit": "Harcama ü<PERSON> limiti", "label_change_type_permit_nft": "Çek", "label_change_type_receive": "Aldığınız", "label_change_type_revoke": "İptal et", "label_change_type_transfer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label_change_type_approve": "Şunları onaylarsınız:", "personal_sign_info": "Bir siteye giriş yapıyorsunuz ve hesabınız için öngörülen herhangi bir değişiklik yok.", "title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON>iklikler bu işlemi gerçekleştirirseniz meydana gelebilecek değişikliklerdir. Bu bir g<PERSON><PERSON>, sadece bir tahm<PERSON>.", "unavailable": "<PERSON><PERSON><PERSON>"}, "7702_functionality": {"smartAccountLabel": "Akıllı Hesap", "standardAccountLabel": "<PERSON><PERSON>", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchBack": "<PERSON><PERSON>", "splashpage": {"accept": "<PERSON><PERSON>", "betterTransaction": "<PERSON><PERSON> hı<PERSON><PERSON><PERSON>, <PERSON><PERSON> dü<PERSON><PERSON>k ücretler", "betterTransactionDescription": "İşlemleri bir arada gerçekleştirerek hem zamandan hem de paradan tasarruf edin.", "featuresDescription": "Aynı hesap ad<PERSON>; dilediğiniz zaman tekrar geçiş yapabilirsiniz.", "payToken": "Dilediğiniz token ile dilediğiniz zaman ödeme yapın", "payTokenDescription": "Zaten sahip olduğunuz token'leri kull<PERSON> ağ ücretlerini karşılayın.", "reject": "Hay<PERSON><PERSON>", "sameAccount": "<PERSON><PERSON><PERSON>, daha akıllı özellikler.", "splashTitle": "Akı<PERSON><PERSON> hesap kullan?"}, "includes_transaction": "{{transactionCount}} işlem içerir", "useSmartAccount": "Akıllı hesap kullan", "successful": "Başarılı!", "success_message": "Bir sonraki işleminizle hesabınız akıllı hesaba güncellenecektir."}, "edit_spending_cap_modal": {"account_balance": "<PERSON><PERSON><PERSON>", "cancel": "İptal et", "description": "Sizin adınıza harcanması konusunda rahat hissettiğiniz tutarı girin.", "invalid_number_error": "Harcama üst limiti bir sayı olmalıdır", "no_empty_error": "Harcama üst limiti boş o<PERSON>az", "no_extra_decimals_error": "Harcama üst limiti token'dan daha fazla ondalık basamak içeremez", "no_zero_error": "Harcama üst limiti 0 olamaz", "no_zero_error_decrease_allowance": "0 harcama üst limitinin 'decreaseAllowance' yönteminde hiçbir etkisi yoktur", "no_zero_error_increase_allowance": "0 harcama üst limitinin 'increaseAllowance' yönteminde hiçbir etkisi yoktur", "save": "<PERSON><PERSON>", "title": "<PERSON><PERSON> limitini d<PERSON>"}, "unlimited": "Sınırsız", "all": "Tümü", "none": "Hiç<PERSON>i", "advanced_details": "Gelişmiş bilgiler", "interacting_with": "Etkileşimde", "data": "<PERSON><PERSON>", "review": "<PERSON><PERSON><PERSON>", "transferRequest": "Transfer talebi", "nested_transaction_heading": "{{index}} i<PERSON><PERSON><PERSON>", "transaction": "İşlem", "available_balance": "Kullanılabilir: ", "edit_amount_done": "<PERSON><PERSON>", "deposit_edit_amount_done": "<PERSON><PERSON>"}, "change_in_simulation_modal": {"title": "Sonuç<PERSON>", "description": "Bu iş<PERSON> için tahmin edilen değişiklikler güncellendi. Devam etmeden önce dikkatle inceleyin.", "proceed": "<PERSON><PERSON> et", "reject": "İşlemi reddet"}, "snap_account_custom_name_approval": {"title": "MetaM<PERSON>'e hesap e<PERSON>in", "input_title": "<PERSON><PERSON><PERSON> adı", "add_account_button": "<PERSON><PERSON><PERSON>", "name_taken_message": "Bu hesap adı zaten mevcut"}, "smart_transactions_migration": {"title": "İşlemler daha akıllı hale geldi", "link": "<PERSON>ha yüksek başarı oranları", "description": " ve MEV koruması. Artık varsayılan olarak açıktır."}, "bridge": {"continue": "<PERSON><PERSON> et", "confirm_bridge": "Köprü", "confirm_swap": "<PERSON><PERSON><PERSON>", "terms_and_conditions": "<PERSON><PERSON>", "select_token": "Token seç", "select_network": "<PERSON><PERSON>", "all_networks": "<PERSON><PERSON><PERSON>", "num_networks": "{{numNetworks}} ağ", "one_network": "1 ağ", "select_all_networks": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "deselect_all_networks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON> kaldır", "see_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gör", "apply": "<PERSON><PERSON><PERSON><PERSON>", "slippage": "<PERSON><PERSON>", "slippage_info": "Emrinizin verildiği zaman ile onaylandığı zaman arasında fiyat değişirse buna “kayma” denir. Kayma burada belirlediğiniz toleransı aşarsa swap işleminiz otomatik olarak iptal edilir.", "network_fee": "<PERSON><PERSON>", "included": "<PERSON>hil", "estimated_time": "<PERSON><PERSON><PERSON>", "quote": "<PERSON><PERSON><PERSON><PERSON>", "rate": "<PERSON><PERSON>", "quote_details": "<PERSON><PERSON><PERSON><PERSON>", "price_impact": "<PERSON><PERSON>t <PERSON>", "time": "Zaman", "quote_info_content": "The best rate we found from providers, including provider fees and a 0.875% MetaMask fee.", "quote_info_title": "<PERSON><PERSON>", "network_fee_info_title": "<PERSON><PERSON>", "network_fee_info_content": "Network fees depend on how busy the network is and how complex your transaction is.", "points": "<PERSON><PERSON>", "points_tooltip": "<PERSON><PERSON>", "points_tooltip_content": "Bu işlemden kazanacağınız tahmini MetaMask Ödül Puanı. Puanların Ödül bakiyenizde onaylanması 1 saate kadar sürebilir", "unable_to_load": "Unable to load", "points_error": "We can't load points right now", "points_error_content": "You'll still earn any points for this transaction. We'll notify you once they've been added to your account. You can also check your rewards tab in about an hour.", "see_other_quotes": "<PERSON><PERSON><PERSON>", "receive_at": "Şurada alınacak:", "error_banner_description": "Bu işlem rotası şu anda kullanılamıyor. <PERSON><PERSON><PERSON>, ağı veya token'ı değiştirmeyi deneyin ve sizin için en iyi seçeneği bulalım.", "insufficient_funds": "Para yetersiz", "insufficient_gas": "Yetersiz gaz", "select_amount": "<PERSON><PERSON>", "bridge_to": "Köprü yapılan:", "swap_to": "<PERSON><PERSON>", "title": "Köprü", "submitting_transaction": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "fetching_quote": "<PERSON><PERSON><PERSON><PERSON>", "fee_disclaimer": "%0,875 MM ücreti dahildir", "hardware_wallet_not_supported": "Donanım cüzdanları henüz desteklenmiyor. Devam etmek için sıcak cüzdan kullanın.", "hardware_wallet_not_supported_solana": "Donanım cüzdanları henüz Solana için desteklenmiyor. Devam etmek için sıcak cüzdan kullanın.", "price_impact_info_title": "<PERSON><PERSON>t <PERSON>", "price_impact_info_description": "<PERSON>yat etkisi, takas emrinizin varlığın piyasa fiyatını nasıl etkilediğini yansıtır. İşlem boyutuna ve havuzdaki mevcut likiditeye bağlıdır. MetaMask'in fiyat etkisi üzerinde bir etkisi veya kontrolü bulunmamaktadır.", "price_impact_info_gasless_description": "Price impact reflects how your swap order affects the market price of the asset. If you don't hold enough funds for gas, part of your source token is automatically allocated to cover fees, which increases price impact. MetaMask does not influence or control price impact.", "slippage_info_title": "<PERSON><PERSON>", "slippage_info_description": "The % change in price you're willing to allow before your transaction is canceled.", "blockaid_error_title": "Bu işlem geri alınacak", "max": "Ma<PERSON>.", "approval_needed": "This will approve {{amount}} {{symbol}} for swapping.", "approval_tooltip_title": "Grant exact access", "approval_tooltip_content": "You are allowing access to the specified amount, {{amount}} {{symbol}}. The contract will not access any additional funds.", "minimum_received": "Minimum Received", "minimum_received_tooltip_title": "Minimum Received", "minimum_received_tooltip_content": "The minimum amount you'll receive if the price changes while your transaction is processing, based on your slippage tolerance. This is an estimate from our liquidity providers. Final amounts may differ."}, "quote_expired_modal": {"title": "<PERSON><PERSON> tek<PERSON>r mevcut", "description": "Oranlar {{refreshRate}} dakikada bir <PERSON>, bu yüzden hazır olduğunuzda Yeni teklif al seçeneğine dokunun.", "get_new_quote": "<PERSON><PERSON> te<PERSON> al"}, "blockaid_modal": {"simulation_title": "İşlem simülasyonda başarısız oldu", "validation_title": "İşlem doğrulama başarısız oldu", "go_back": "<PERSON><PERSON> git"}, "bridge_transaction_details": {"status": "Durum", "date": "<PERSON><PERSON><PERSON>", "total_gas_fee": "Toplam gaz ücreti", "estimated_completion": "<PERSON><PERSON><PERSON>", "bridge_step_action_bridge_complete": "{{destChainName}} üzerinde {{destSymbol}} alındı", "bridge_step_action_bridge_pending": "{{destChainName}} üzerinde {{destSymbol}} alınıyor", "bridge_step_action_swap_complete": "{{srcSymbol}} - {{destSymbol}} swap i<PERSON><PERSON><PERSON> ger<PERSON>ek<PERSON>ştirildi", "bridge_step_action_swap_pending": "{{srcSymbol}} - {{destSymbol}} swap işlemi gerçekleştiriliyor", "view_on_block_explorer": "Blok Gezgininde Görüntüle", "block_explorer_description": "Bu işlem iki ağda da bulunuyor. <PERSON><PERSON><PERSON><PERSON> b<PERSON>, kaynağı gösterirken ikincisi onaylandıktan sonra hedefi gösterir.", "transaction_details": "İşlem Bilgileri", "bridge_to_chain": "{{chainName}} i<PERSON><PERSON>", "recipient": "Alıcı"}, "account_status": {"account_already_exists": "Cüzdan zaten mevcut", "account_already_exists_description": "Zaten \"{{accountName}}\" kullanan bir cüzdan mevcut. Onun yerine oturum açmayı denemek ister misiniz?", "log_in": "Oturum aç", "account_not_found": "Cüzdan bulunamadı", "account_not_found_description": "\"{{accountName}}\" i<PERSON>in bir cüzdan bulamadık. Bu oturumla yeni bir cüzdan oluşturmak ister misiniz?", "create_new_wallet": "<PERSON>ni bir cüzdan oluştur", "use_different_login_method": "Farklı bir oturum açma yöntemi kullanın"}, "error_sheet": {"still_there_title": "Halen orada mısınız?", "still_there_description": "Oturumunuz eylemsizlikten dolayı zaman aşımına uğradı. Hazır olduğunuzda tekrar deneyin.", "unable_to_login_title": "Bağlanılamıyor", "unable_to_login_description": "İnternet bağlantınız stabil değil. Bağlantınızı kontrol edip tekrar deneyin.", "something_went_wrong_title": "<PERSON><PERSON> ters gitti", "something_went_wrong_description": "Oturum açılırken bir hata oluştu. Te<PERSON>r deneyin ve sorun devam ederse", "support_button": "MetaMask Destek.", "error_button": "<PERSON><PERSON><PERSON> dene", "user_cancelled_title": "Oturum açma işlemi iptal edildi", "user_cancelled_description": "Oturum açma işlemini iptal ettiniz.\nHazır olduğunuzda tekrar deneyin.", "user_cancelled_button": "<PERSON><PERSON><PERSON> dene", "google_login_no_credential_title": "Google oturumu açılamadı", "google_login_no_credential_description": "Bu oturum açma bilgileri ile ilişkili bir Google hesabı bulamadık. Farklı bir oturum açma yöntemiyle tekrar deneyin.", "google_login_no_credential_button": "<PERSON><PERSON><PERSON> dene", "oauth_error_title": "Oturum açılamadı", "oauth_error_description": "Oturum açılırken bir hata oluştu.\nTekrar deneyin ve sorun devam ederse MetaMask Destek ile iletişime geçin.", "oauth_error_button": "<PERSON><PERSON><PERSON> dene", "no_internet_connection_title": "Bağlanılamıyor", "no_internet_connection_description": "İnternet bağlantınız kararlı değil. Bağlantınızı kontrol edip tekrar deneyin.", "no_internet_connection_button": "<PERSON><PERSON><PERSON> dene"}, "password_hint": {"title": "<PERSON><PERSON><PERSON>", "description": "Kendinize şifrenizi hatırlamanıza yardımcı olacak bir ipucu bırakın. Bu ipucu cihazınıza kaydedilecek ve paylaşılmayacaktır.", "description2": "Unutmayın: Şifrenizi kaybederseniz cüzdanınızı kullanamayacaksınız.", "button": "İpucu o<PERSON>ştur", "placeholder": "ör. annemin evi", "saved": "<PERSON><PERSON>", "saved_toast": "<PERSON><PERSON><PERSON>", "error_matches_password": "İpucu olarak şifrenizi kullanamazsınız"}, "protect_your_wallet": {"title": "<PERSON>ü<PERSON><PERSON>", "login_with_social": "Sosyal Medya Hesapları ile oturum aç", "setup": "<PERSON><PERSON><PERSON>", "secret_recovery_phrase": "<PERSON><PERSON><PERSON> k<PERSON> ifadesi {{num}}", "back_up": "<PERSON><PERSON><PERSON>", "reveal": "Açığa <PERSON>", "social_recovery_title": "{{authConnection}} KURTARMA", "social_recovery_enable": "Etkinleş<PERSON><PERSON>", "social_login_description": "Hesabınızı ve gizli kurtarma ifadelerinizi kurtarmak için {{authConnection}} oturumunuzu ve MetaMask şifrenizi kullanın.", "srps_title": "GİZLİ KURTARMA İFADELERİ", "srps_description": "Cüzdanınız en çok her iki kurtarma yöntemi de ayarlandığında en iyi şekilde korunur. <PERSON>ir tanesi başarısız olursa diğeri cüzdanınızı kurtarmanıza yardımcı olur."}, "backupAndSync": {"title": "Yedekleme ve senkronizasyon", "description": "Hesaplarınızı yedekleyin ve ayarları senkronize edin.", "enabling": "Yedekleme ve senkronizasyon etkinleştiriliyor", "disabling": "Yedekleme ve senkronizasyon devre dışı bırakılıyor", "enable": {"title": "Yedekleme ve senkronizasyonu aç", "confirmation": "Yedekleme ve senkronizasyonu açtığınızda temel işlevselliği de açarsınız. Devam etmek istiyor musunuz?", "description": "Yedekleme ve senk<PERSON><PERSON>, <PERSON>zel ayarlarınız ve özellikleriniz için şifrelenmiş verileri depolamamızı sağlar. Bu, cihazlar arasında aynı MetaMask deneyimini yaşamanızı sağlar ve MetaMask'i yeniden yüklemeniz gerekirse ayarları ve özellikleri geri yükler. Bu işlem Gizli Kurtarma İfadenizi yedeklemez.", "updatePreferences": "Tercihlerinizi dilediğiniz zaman şurada güncelleyebilirsiniz:", "settingsPath": "Ayarlar > <PERSON><PERSON><PERSON><PERSON> ve senkron<PERSON>on."}, "privacyLink": "Gizliliğinizi nasıl koruduğumuzu öğrenin", "features": {"accounts": "<PERSON><PERSON><PERSON><PERSON>", "contacts": "<PERSON><PERSON><PERSON>"}, "manageWhatYouSync": {"title": "<PERSON><PERSON>ri senkronize ettiğinizi yönetin", "description": "Cihazlarınız arasında nelerin senkronize edildiğini açın."}}, "snap_ui": {"asset_selector": {"title": "Bir varlık seçin"}, "account_selector": {"title": "<PERSON><PERSON><PERSON>"}, "dropdown": {"title": "Bir seçenek seçin"}, "hideSentitiveInfo": {"message": "<PERSON><PERSON><PERSON> bil<PERSON>i gizle"}, "doNotShare": {"message": "<PERSON><PERSON><PERSON> hiç kimseyle <PERSON>şmayın"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON>"}, "show_more": "daha fazla", "show_less": "daha az"}, "multichain_accounts": {"intro": {"title": "Introducing multichain accounts", "section_1_title": "What are multichain accounts?", "section_1_description": "One account, addresses on multiple networks MetaMask supports. So now you can use Ethereum, Solana, and more without switching accounts.", "section_2_title": "Same address, more networks", "section_2_description": "We’ve merged your accounts. You can keep using MetaMask the same way as before. Your funds are safe and unchanged.", "view_accounts_button": "View accounts", "learn_more_button": "Learn more"}, "learn_more": {"title": "Learn more", "description": "Multichain accounts are now the default. To opt out, turn off basic functionality.", "checkbox_label": "Turn off basic functionality", "confirm_button": "Confirm"}, "add_wallet": "<PERSON><PERSON><PERSON><PERSON> ekle", "add_hardware_wallet": "Bir donanım cüzdanı ekle", "account_details": {"header_title": "<PERSON><PERSON><PERSON>", "account_name": "<PERSON><PERSON><PERSON>", "networks": "<PERSON><PERSON><PERSON>", "account_address": "<PERSON><PERSON><PERSON>", "wallet": "Cüzdan", "private_key": "<PERSON><PERSON>", "private_keys": "<PERSON><PERSON>", "unlock_to_reveal": "Görmek için kilidini açın", "smart_account": "Akıllı hesap", "set_up": "<PERSON><PERSON>", "secret_recovery_phrase": "<PERSON><PERSON><PERSON>", "back_up": "<PERSON><PERSON><PERSON>", "remove_account": "Hesabı kaldır"}, "address_list": {"addresses": "<PERSON><PERSON><PERSON>", "receiving_address": "Alıcı adresi", "copied": "<PERSON><PERSON> k<PERSON>alandı"}, "private_key_list": {"list_title": "<PERSON><PERSON>", "warning_title": "<PERSON>zel anahtarınızı paylaşmayın", "warning_description": "<PERSON><PERSON> <PERSON>, ilişkili zincir için he<PERSON>ı<PERSON>ızın tüm kontrolünü verir.", "learn_more": "<PERSON>ha fazla bilgi edin", "enter_password": "Şifrenizi girin", "password_placeholder": "Şifre", "wrong_password": "<PERSON><PERSON>re <PERSON>ı<PERSON>", "copied": "Özel anahtar kopyalandı", "continue": "<PERSON><PERSON>", "cancel": "İptal"}, "accounts_list": {"details": "Ayrıntılar"}, "wallet_details": {"wallet_name": "Cüzdan Adı", "balance": "Bakiye", "create_account": "<PERSON><PERSON><PERSON>", "creating_account": "<PERSON><PERSON><PERSON>...", "back_up": "<PERSON><PERSON><PERSON>", "reveal_recovery_phrase_with_index": "Kurtarma İfadesini göster {{index}}"}, "smart_account": {"title": "Akıllı Hesabı Etkinleştir", "description": "Akıllı he<PERSON><PERSON>, desteklenen ağlarda etkinleştirebilirsiniz.", "learn_more": "<PERSON>ha fazla bilgi edin"}, "edit_account_name": {"title": "Hesap <PERSON>ı<PERSON>üzenle", "account_name": "<PERSON><PERSON><PERSON> adı", "confirm_button": "<PERSON><PERSON><PERSON>", "name": "Ad<PERSON>", "save_button": "<PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON> ad<PERSON>", "error_duplicate_name": "This name is already in use.", "error_empty_name": "<PERSON><PERSON><PERSON> adı boş olamaz"}, "delete_account": {"title": "Hesabı Kaldır", "warning_title": "<PERSON>u hesap <PERSON>'ten kaldırılacak.", "warning_description": "Kaldırmadan önce bu hesaba ait Gizli Kurtarma İfadenizin veya özel anahtarınızın olduğundan emin olun.", "remove_button": "Kaldır", "cancel_button": "İptal", "error": "<PERSON><PERSON><PERSON>"}, "share_address": {"title": "<PERSON><PERSON><PERSON>", "copy_address": "<PERSON><PERSON><PERSON>", "view_on_explorer_button": "{{explorer}} <PERSON><PERSON><PERSON><PERSON>", "view_on_block_explorer": "Blok Gezgininde Görüntüle"}, "share_address_qr": {"title": "{{networkName}} <PERSON><PERSON><PERSON>", "copy_address": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> ad<PERSON><PERSON> kull<PERSON> ve koleksiyon alın:", "description_prefix": "Şurada token ve koleksiyon almak için bu adresi kullanın:"}, "export_credentials": {"export_private_key": "<PERSON><PERSON>", "private_key_warning_title": "<PERSON><PERSON> anah<PERSON><PERSON> asla <PERSON>.", "private_key_warning_description": "Özel anahtarınıza sa<PERSON> olan her<PERSON>, hesabınızda tutulan tüm varlıkları çalabilir.", "credential_as_text": "<PERSON><PERSON>", "credential_as_qr": "QR kodu", "export_mnemonic": "Kurtarma ifadesini dışa aktar", "backup": "<PERSON><PERSON><PERSON>"}, "reveal_private_key": {"title": "<PERSON><PERSON>", "banner_title": "<PERSON><PERSON> anah<PERSON><PERSON> asla <PERSON>.", "banner_description": "Özel anahtarınıza sa<PERSON> olan her<PERSON>, hesabınızda tutulan tüm varlıkları çalabilir.", "enter_password": "Şifrenizi girin", "password_placeholder": "Şifre", "next": "<PERSON><PERSON><PERSON>", "copy": "Kopyala"}, "reveal_srp": {"header": "Güvenlik testi", "description": "Gizli Kurtarma İfadenizi görmek için iki soruyu doğru cevaplamanız gerekmektedir", "get_started": "Başlarken", "learn_more": "<PERSON>ha fazla bilgi edin"}, "address_rows_list": {"search_placeholder": "<PERSON>ğ ara", "no_networks_found": "<PERSON><PERSON> bulunamadı", "no_networks_available": "<PERSON>ğ yok"}}, "deep_link_modal": {"private_link": {"title": "Tekrar MetaMask'e yönlendiriliyorsunuz", "description": "<PERSON><PERSON> {{pageTitle}} sayfasını açacaksınız.", "checkbox_label": "Bana tekrar hatırlatma"}, "public_link": {"title": "<PERSON><PERSON><PERSON> il<PERSON>ley<PERSON>", "description": "Buraya MetaMask tarafından <PERSON>, üçüncü bir taraftan gönderildiniz. Devam ederseniz {{pageTitle}} sayfasını açacaksınız."}, "invalid": {"title": "<PERSON>u <PERSON><PERSON> mevcut değil", "description": "Aradığınız sayfayı bulamıyoruz.", "update_to_store_link": "Update to the latest version of MetaMask", "well_take_you_to_right_place": " and we'll take you to the right place."}, "go_to_home_button": "Go to the home page", "back_button": "<PERSON><PERSON>", "continue_button": "<PERSON><PERSON> et"}, "card": {"card": "MetaMask Kartı", "add_funds_bottomsheet": {"deposit": "Nakit ile fonla", "deposit_description": "Düşük maliyetli kart veya banka transferi", "swap": "<PERSON><PERSON><PERSON> ile fonla", "swap_description": "Linea'da token - {{symbol}} takas i<PERSON><PERSON>i yapın", "select_method": "Yöntem seç"}, "card_home": {"error_title": "<PERSON><PERSON><PERSON> alınamıyor", "error_description": "Bu sayfadaki içeriği görüntülemenizi önleyen bir sorun var gibi görünüyor. Lütfen bağlantınızı kontrol edin veya sayfayı yenilemeyi deneyin.", "try_again": "<PERSON><PERSON><PERSON> dene", "limited_spending_warning": "Gerçek harcama kapasiteniz sınırlı olabilir. Limitinizi ayarlamak için {{manageCard}} alan<PERSON>na gidin", "add_funds": "Para ekle", "manage_card_options": {"manage_card": "Kartı yönet", "advanced_card_management_description": "İşlemleri <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kartı dondurun ve daha fazlasını yapın"}}}, "onboarding_error_fallback": {"title": "<PERSON>ir hata o<PERSON>", "description": "Sorunu düzeltmemize ve MetaMask'i iyileştirmemize yardımcı olmak için bize bir hata raporu gönderin. Bu rapor gizli ve anonim olacaktır.", "recovery_warning": "Bu hatayı almaya devam ediyorsanız Gizli Kurtarma İfadenizi kaydedin ve uygulamayı yeniden yükleyin. Unutmayın: <PERSON><PERSON><PERSON> Kurtarma İfadeniz olmadan cüzdanınızı geri yükleyemezsiniz.", "error_message_report": "<PERSON><PERSON> rap<PERSON><PERSON>:", "copy": "Kopyala", "send_report": "<PERSON><PERSON><PERSON>", "try_again": "<PERSON><PERSON><PERSON> dene", "report_submitted": "<PERSON><PERSON> rap<PERSON><PERSON>."}, "pay_with_modal": {"title": "Ödeme yönte<PERSON> se<PERSON>"}, "connection_removed_modal": {"title": "Bağlantılar kaldırıldı", "content": "Bu cihazdaki işlemsizlikten dolayı bazı bağlantılar (donanım cüzdanları ve snap'ler gibi) kaldırıldı. Dilediğiniz zaman Ayarlar kısmından tekrar ekleyebilirsiniz.", "tryAgain": "<PERSON><PERSON><PERSON> dene", "close": "Ka<PERSON><PERSON>"}, "rewards": {"auth_fail_title": "Unknown Error.", "auth_fail_description": "An unknown error occurred while authenticating this account with the rewards program. Please try again later.", "failed_to_authenticate": "Failed to authenticate with rewards program", "not_implemented": "Çok yakında", "not_implemented_season_summary": "Sezon Özeti Çok Yakında", "referral_rewards_title": "Referanslar", "points": "<PERSON><PERSON>", "point": "<PERSON><PERSON>", "level": "<PERSON><PERSON><PERSON>", "to_level_up": "Seviye yükseltmek için", "season_ends": "<PERSON><PERSON> sona eriyor", "season_ended": "<PERSON><PERSON> sona erdi", "main_title": "<PERSON><PERSON><PERSON><PERSON>", "referral_title": "Referanslar", "tab_overview_title": "Genel Bakış", "tab_activity_title": "Aktivite", "tab_levels_title": "<PERSON><PERSON><PERSON><PERSON>", "referral_stats_earned_from_referrals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "referral_stats_referrals": "Referanslar", "loading_activity": "Loading activity...", "error_loading_activity": "Error loading activity", "activity_empty_title": "No recent activity.", "activity_empty_description": "Use MetaMask to earn points, level up, and unlock rewards.", "activity_empty_link": "See ways to earn", "toast_dismiss": "<PERSON><PERSON><PERSON>", "events": {"type": {"swap": "Kaydır", "perps": "Sürekli Vadeli İşlem Sözleşmeleri", "referral": "Referral", "referral_action": "Referral action", "sign_up_bonus": "Sign up bonus", "loyalty_bonus": "Loyalty bonus", "one_time_bonus": "One-time bonus", "open_position": "Opened position", "close_position": "Closed position", "take_profit": "Kazancı al", "stop_loss": "<PERSON><PERSON><PERSON> du<PERSON>", "uncategorized_event": "Uncategorized event"}}, "onboarding": {"not_supported_region_title": "<PERSON><PERSON><PERSON>", "not_supported_region_description": "Rewards are not supported in your region yet. We are working on expanding access, so check back later.", "not_supported_account_needed_title": "Ethereum account needed", "not_supported_account_needed_description": "MetaMask Rewards aren't available for Solana accounts yet. Switch to an Ethereum account to claim your points.", "not_supported_confirm": "<PERSON><PERSON><PERSON><PERSON>", "intro_title_1": "Season 1", "intro_title_2": "is Live", "intro_description": "Earn points for your your activity. \nAdvance through levels to unlock rewards.", "intro_confirm": "<PERSON><PERSON><PERSON> 250 points", "intro_confirm_geo_loading": "Checking region...", "checking_opt_in": "Checking opt-in for accounts...", "redirecting_to_dashboard": "Redirecting to dashboard...", "intro_skip": "<PERSON><PERSON><PERSON>", "step_confirm": "<PERSON><PERSON><PERSON>", "step1_title": "Earn points on every trade", "step1_description": "Every swap and perps trade you make in MetaMask gets you closer to rewards. Link your accounts and watch your points add up.", "step2_title": "Level up for bigger perks", "step2_description": "Hit points milestones to get perks like 50% off perps fees, exclusive tokens, and a free MetaMask Metal Card.", "step3_title": "Exclusive seasonal rewards", "step3_description": "Each season brings new perks. Join in, compete, and claim what you can before time runs out.", "step4_title": "You'll earn 250 points when you sign up!", "step4_title_referral_bonus": "You'll earn 500 points when you sign up with a code!", "step4_title_referral_validating": "Validating referral code...", "step4_referral_bonus_description": "Use a referral code to earn 250 points", "step4_referral_input_placeholder": "Referral code (optional)", "step4_confirm": "<PERSON>laim points", "step4_confirm_loading": "Claiming points...", "step4_linking_accounts": "Linking accounts... ({{current}}/{{total}})", "step4_linking_accounts_loading": "Linking additional accounts...", "step4_success_description": "You have successfully signed up for MetaMask Rewards!", "step4_legal_disclaimer": "Joining means we'll track your on-chain activity to reward you automatically.", "step4_legal_disclaimer_learn_more": "Daha fazla bilgi edinin."}, "settings": {"title": "<PERSON><PERSON>s", "subtitle": "Connect multiple accounts to combine your points and unlock rewards faster.", "error_title": "Unable to Load Linked Accounts", "error_description": "We couldn't detect your linked accounts. Please check your internet connection and try again.", "error_retry": "Retry", "tab_linked_accounts": "Linked Accounts ({{count}})", "tab_unlinked_accounts": "Unlinked Accounts ({{count}})", "no_linked_accounts": "No linked accounts", "all_accounts_linked_title": "All accounts are linked", "all_accounts_linked_description": "You have linked all your accounts to the rewards program.", "link_account_success_title": "Account {{accountName}} successfully linked", "link_account_error_title": "Failed to link account", "link_account_button": "Link"}, "optout": {"title": "Opt out of Rewards", "description": "This will erase your points and progress. You won't be able to undo this.", "confirm": "Opt out", "modal": {"confirmation_title": "Are you sure?", "confirmation_description": "This will remove all your progress, and can't be reversed. If you rejoin the Rewards program later, you'll restart at 0.", "cancel": "Cancel", "confirm": "Confirm", "error_message": "Failed to opt out of rewards program. Please try again.", "processing": "Processing..."}}, "unlinked_accounts_info": {"title": "There are unlinked accounts", "description": "If you want to earn points for the activity of these accounts you can link them in the settings page.", "go_to_settings": "Go to settings"}, "unlinked_account_info": {"title": "Account not linked", "description": "This account's activity is not being tracked for this season."}, "link_account": "Link account", "linking_account": "Linking...", "ways_to_earn": {"title": "Ways to earn", "supported_networks": "Supported Networks", "swap": {"title": "<PERSON><PERSON><PERSON>", "sheet_title": "Swap tokens", "sheet_description": "Swap tokens on supported networks to earn points for every dollar you trade.", "points": "80 points per $100", "cta_label": "Start a swap"}, "perps": {"title": "Perps", "sheet_title": "Trade perps", "sheet_description": "Earn points on every trade, including opens and closes, stop loss and take profit orders, and margin adjustments.", "points": "10 points per $100", "cta_label": "Start a trade"}, "referrals": {"title": "Referrals", "points": "Get 20% from friends you refer"}}, "referral": {"actions": {"share_referral_link": "Bir arkadaşına <PERSON>", "share_referral_subject": "MetaMask Ödüllerine katıl"}, "info": {"title": "Daha fazla kazanmak için kodunuzu paylaşın", "description": "Arkadaşlarınız bonus puan kazanır ve siz de onların ödüllerinin %20'sini alırsınız."}}, "active_boosts_title": "Active Boosts", "season_1": "Season 1"}, "time": {"minutes_format": "{{count}} dakika", "minutes_format_plural": "{{count}} dakika"}, "transaction_details": {"title": {"perps_deposit": "Fonlanmış sürekli vadeli işlem sözleşmeleri hesabı", "default": "İşlem bilgileri"}, "label": {"bridge_fee": "Köprü ücreti", "network_fee": "<PERSON><PERSON>", "paid_with": "Ödeme a<PERSON>ı", "total": "Toplam"}, "summary_title": {"bridge": "{{sourceSymbol}} - {{targetSymbol}} k<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridge_approval": "Approve {{approveSymbol}}", "default": "İşlem", "perps_deposit": "<PERSON>on e<PERSON>", "swap": "Token swap i<PERSON><PERSON>i ya<PERSON>ın", "swap_approval": "Approve tokens"}}}